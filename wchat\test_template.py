#!/usr/bin/env python3
"""
测试配置模板渲染
"""
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
current_dir = Path(__file__).parent
project_root = current_dir
sys.path.insert(0, str(project_root))

try:
    from config import config
    from flask import Flask, render_template
    
    app = Flask(__name__, template_folder='src/web/templates')
    
    with app.app_context():
        print("✅ 配置加载成功")
        print(f"微信配置: {config.wechat}")
        print(f"listen_all属性: {hasattr(config.wechat, 'listen_all')}")
        if hasattr(config.wechat, 'listen_all'):
            print(f"listen_all值: {config.wechat.listen_all}")
        
        # 测试模板渲染
        try:
            result = render_template('config.html', config=config)
            print("✅ 模板渲染成功")
            print(f"渲染结果长度: {len(result)} 字符")
        except Exception as e:
            print(f"❌ 模板渲染失败: {e}")
            import traceback
            traceback.print_exc()
            
except Exception as e:
    print(f"❌ 测试失败: {e}")
    import traceback
    traceback.print_exc()
