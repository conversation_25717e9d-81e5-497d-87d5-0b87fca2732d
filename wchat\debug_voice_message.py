#!/usr/bin/env python3
"""
调试语音消息处理功能
"""
import os
import sys
from pathlib import Path

# 添加项目路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def check_voice_config():
    """检查语音配置"""
    print("🔧 检查语音配置")
    print("=" * 60)
    
    try:
        from config import config
        
        print("语音相关配置:")
        print(f"  voice_to_text: {config.wechat.voice_to_text}")
        print(f"  voice_reply_enabled: {config.wechat.voice_reply_enabled}")
        print(f"  auto_reply: {config.wechat.auto_reply}")
        
        if config.wechat.voice_to_text:
            print("✅ 语音转文字功能已启用")
        else:
            print("❌ 语音转文字功能已禁用")
            
        return config.wechat.voice_to_text
        
    except Exception as e:
        print(f"❌ 配置检查失败: {e}")
        return False

def debug_message_types():
    """调试消息类型识别"""
    print("\n🔍 调试消息类型识别")
    print("=" * 60)
    
    # 模拟不同类型的消息
    test_messages = [
        {"type": "text", "content": "你好", "expected": "处理"},
        {"type": "voice", "content": "voice_001.amr", "expected": "语音转文字"},
        {"type": "audio", "content": "audio_001.wav", "expected": "语音转文字"},
        {"type": "image", "content": "image.jpg", "expected": "忽略"},
        {"type": "file", "content": "document.pdf", "expected": "忽略"}
    ]
    
    print("消息类型处理测试:")
    for msg in test_messages:
        msg_type = msg["type"]
        content = msg["content"]
        expected = msg["expected"]
        
        # 检查是否应该处理
        should_process = msg_type in ['text', 'voice', 'audio']
        is_voice = msg_type in ['voice', 'audio']
        
        print(f"\n消息类型: {msg_type}")
        print(f"  内容: {content}")
        print(f"  期望: {expected}")
        print(f"  应处理: {'是' if should_process else '否'}")
        print(f"  是语音: {'是' if is_voice else '否'}")
        
        if should_process:
            if is_voice:
                print(f"  处理流程: 语音转文字 → 生成回复")
            else:
                print(f"  处理流程: 直接生成回复")
        else:
            print(f"  处理流程: 忽略消息")

def test_voice_conversion_methods():
    """测试语音转换方法"""
    print("\n🎤 测试语音转换方法")
    print("=" * 60)
    
    # 模拟微信对象
    class MockWeChatAPI:
        def __init__(self, has_voice_api=False):
            self.has_voice_api = has_voice_api
            
        def GetVoiceText(self, voice_file):
            if self.has_voice_api:
                # 模拟成功转换
                voice_texts = {
                    "voice_001.amr": "你好",
                    "voice_002.amr": "推荐一款手机",
                    "voice_003.amr": "谢谢"
                }
                return voice_texts.get(voice_file, None)
            else:
                raise AttributeError("GetVoiceText method not available")
        
        def VoiceToText(self, voice_file):
            if self.has_voice_api:
                return self.GetVoiceText(voice_file)
            else:
                raise AttributeError("VoiceToText method not available")
    
    # 测试不同的API可用性情况
    scenarios = [
        ("支持语音API", MockWeChatAPI(has_voice_api=True)),
        ("不支持语音API", MockWeChatAPI(has_voice_api=False)),
        ("无微信对象", None)
    ]
    
    test_voices = ["voice_001.amr", "voice_002.amr", "voice_unknown.amr"]
    
    for scenario_name, mock_wx in scenarios:
        print(f"\n场景: {scenario_name}")
        print("-" * 30)
        
        for voice_file in test_voices:
            print(f"\n测试语音: {voice_file}")
            
            # 模拟语音转文字过程
            try:
                if mock_wx is None:
                    print("  结果: 无微信对象")
                    continue
                
                # 方法1: GetVoiceText
                try:
                    text1 = mock_wx.GetVoiceText(voice_file)
                    if text1:
                        print(f"  GetVoiceText: {text1}")
                    else:
                        print(f"  GetVoiceText: 无结果")
                except Exception as e:
                    print(f"  GetVoiceText: 失败 ({e})")
                
                # 方法2: VoiceToText
                try:
                    text2 = mock_wx.VoiceToText(voice_file)
                    if text2:
                        print(f"  VoiceToText: {text2}")
                    else:
                        print(f"  VoiceToText: 无结果")
                except Exception as e:
                    print(f"  VoiceToText: 失败 ({e})")
                    
            except Exception as e:
                print(f"  处理异常: {e}")

def analyze_voice_message_flow():
    """分析语音消息处理流程"""
    print("\n🔄 分析语音消息处理流程")
    print("=" * 60)
    
    print("当前语音消息处理流程:")
    print("1. 接收消息 → 检查消息类型")
    print("2. 如果是 voice/audio → 检查语音配置")
    print("3. 如果启用语音转文字 → 调用转换方法")
    print("4. 转换成功 → 按文字消息处理")
    print("5. 转换失败 → 忽略消息")
    
    print(f"\n可能的问题点:")
    print("❓ 1. 消息类型识别错误")
    print("❓ 2. 语音配置被禁用")
    print("❓ 3. 微信API不支持语音转文字")
    print("❓ 4. 语音文件格式不支持")
    print("❓ 5. 语音内容为空或损坏")
    
    print(f"\n调试建议:")
    print("💡 1. 检查日志中的消息类型")
    print("💡 2. 确认语音配置已启用")
    print("💡 3. 测试微信API可用性")
    print("💡 4. 添加更详细的调试日志")

def create_voice_debug_patch():
    """创建语音调试补丁"""
    print("\n🔧 创建语音调试补丁")
    print("=" * 60)
    
    patch_code = '''
# 在 wechat_handler.py 的 _handle_message 方法中添加调试日志

# 在消息类型检查后添加:
logger.info(f"消息详细信息 - 类型: {msg_type}, 内容: {content[:100]}, 发送者: {sender}")

# 在语音消息处理前添加:
if msg_type in ['voice', 'audio']:
    logger.info(f"检测到语音消息 - 类型: {msg_type}")
    logger.info(f"语音配置状态: voice_to_text={config.wechat.voice_to_text}")
    logger.info(f"语音内容: {content}")
    
    # 检查微信对象的语音API
    has_get_voice_text = hasattr(self.wx, 'GetVoiceText')
    has_voice_to_text = hasattr(self.wx, 'VoiceToText')
    logger.info(f"微信API可用性: GetVoiceText={has_get_voice_text}, VoiceToText={has_voice_to_text}")

# 在语音转文字方法中添加:
def _convert_voice_to_text(self, voice_content: str, sender: str) -> Optional[str]:
    logger.info(f"开始语音转文字 - 内容: {voice_content}, 发送者: {sender}")
    
    # ... 现有代码 ...
    
    # 在每个方法尝试前添加日志
    logger.debug(f"尝试方法1: GetVoiceText")
    # ... GetVoiceText 代码 ...
    
    logger.debug(f"尝试方法2: VoiceToText") 
    # ... VoiceToText 代码 ...
'''
    
    print("建议的调试补丁代码:")
    print(patch_code)
    
    return patch_code

def main():
    """主函数"""
    print("🎤 语音消息处理调试工具")
    print()
    
    # 运行调试步骤
    steps = [
        ("语音配置检查", check_voice_config),
        ("消息类型调试", debug_message_types),
        ("语音转换测试", test_voice_conversion_methods),
        ("流程分析", analyze_voice_message_flow),
        ("调试补丁", create_voice_debug_patch)
    ]
    
    results = []
    
    for step_name, step_func in steps:
        print(f"\n🔍 执行: {step_name}")
        try:
            result = step_func()
            results.append((step_name, True, result))
        except Exception as e:
            print(f"❌ {step_name} 失败: {e}")
            results.append((step_name, False, str(e)))
    
    # 总结和建议
    print(f"\n" + "=" * 60)
    print(f"          语音消息调试总结")
    print(f"=" * 60)
    
    print("🔍 可能的问题原因:")
    print("1. ❓ 微信版本不支持语音转文字API")
    print("2. ❓ 语音消息格式不被识别")
    print("3. ❓ 语音配置被意外禁用")
    print("4. ❓ 消息类型识别错误")
    
    print("\n💡 解决建议:")
    print("1. 🔧 检查微信PC版本是否支持语音转文字")
    print("2. 🔧 在代码中添加更详细的调试日志")
    print("3. 🔧 测试不同格式的语音消息")
    print("4. 🔧 考虑使用第三方语音识别API")
    
    print("\n🚀 下一步操作:")
    print("1. 添加详细的语音消息调试日志")
    print("2. 测试微信API的语音转文字功能")
    print("3. 如果微信API不可用，集成第三方语音识别")

if __name__ == "__main__":
    main()
