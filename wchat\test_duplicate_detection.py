#!/usr/bin/env python3
"""
测试重复消息检测功能
"""
import sys
import time
from pathlib import Path

# 添加项目路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def test_message_id_generation():
    """测试消息ID生成"""
    print("=" * 60)
    print("          测试消息ID生成")
    print("=" * 60)
    
    try:
        from src.bot.wechat_handler import WeChatHandler
        
        # 创建处理器
        handler = WeChatHandler()
        print("✅ 微信处理器创建成功")
        
        # 模拟消息对象
        class MockMessage:
            def __init__(self, sender, content, timestamp=None):
                self.sender = sender
                self.content = content
                self.time = timestamp or int(time.time())
        
        # 测试消息
        test_messages = [
            MockMessage("用户A", "你好", 1234567890),
            MockMessage("用户A", "你好", 1234567890),  # 相同消息
            MockMessage("用户A", "你好", 1234567891),  # 不同时间
            MockMessage("用户B", "你好", 1234567890),  # 不同发送者
            MockMessage("用户A", "再见", 1234567890),  # 不同内容
        ]
        
        print("测试消息ID生成:")
        message_ids = []
        for i, msg in enumerate(test_messages, 1):
            msg_id = handler._generate_message_id(msg)
            message_ids.append(msg_id)
            print(f"  {i}. {msg.sender} - {msg.content} (时间: {msg.time}) → ID: {msg_id[:16]}...")
        
        # 检查重复
        print("\n重复检测结果:")
        unique_ids = set(message_ids)
        print(f"  总消息数: {len(message_ids)}")
        print(f"  唯一ID数: {len(unique_ids)}")
        
        if len(unique_ids) == len(message_ids) - 1:  # 应该有一个重复
            print("✅ 消息ID生成正确，能够识别重复消息")
        else:
            print("❌ 消息ID生成可能有问题")
        
        return handler
        
    except Exception as e:
        print(f"❌ 消息ID生成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_duplicate_detection(handler):
    """测试重复检测机制"""
    print("\n" + "=" * 60)
    print("          测试重复检测机制")
    print("=" * 60)
    
    try:
        # 模拟消息对象
        class MockMessage:
            def __init__(self, sender, content, timestamp=None):
                self.sender = sender
                self.content = content
                self.time = timestamp or int(time.time())
        
        # 测试消息
        msg1 = MockMessage("测试用户", "你好", 1234567890)
        msg2 = MockMessage("测试用户", "你好", 1234567890)  # 完全相同
        msg3 = MockMessage("测试用户", "再见", 1234567890)  # 不同内容
        msg4 = MockMessage("测试用户", "你好", 1234567891)  # 不同时间
        
        print("测试重复检测:")
        
        # 第一次处理
        is_processed_1 = handler._is_message_processed(handler._generate_message_id(msg1))
        print(f"  1. 第一次处理消息1: {'已处理' if is_processed_1 else '未处理'}")
        
        # 第二次处理相同消息
        is_processed_2 = handler._is_message_processed(handler._generate_message_id(msg2))
        print(f"  2. 第二次处理相同消息: {'已处理' if is_processed_2 else '未处理'}")
        
        # 处理不同内容
        is_processed_3 = handler._is_message_processed(handler._generate_message_id(msg3))
        print(f"  3. 处理不同内容消息: {'已处理' if is_processed_3 else '未处理'}")
        
        # 处理不同时间
        is_processed_4 = handler._is_message_processed(handler._generate_message_id(msg4))
        print(f"  4. 处理不同时间消息: {'已处理' if is_processed_4 else '未处理'}")
        
        # 验证结果
        if not is_processed_1 and is_processed_2 and not is_processed_3 and not is_processed_4:
            print("\n✅ 重复检测机制工作正常")
            print("   - 第一次处理：未处理 → 正常")
            print("   - 重复消息：已处理 → 正确跳过")
            print("   - 不同内容：未处理 → 正常")
            print("   - 不同时间：未处理 → 正常")
        else:
            print("\n❌ 重复检测机制可能有问题")
        
        return True
        
    except Exception as e:
        print(f"❌ 重复检测测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_cache_management(handler):
    """测试缓存管理"""
    print("\n" + "=" * 60)
    print("          测试缓存管理")
    print("=" * 60)
    
    try:
        # 模拟大量消息
        class MockMessage:
            def __init__(self, sender, content, timestamp):
                self.sender = sender
                self.content = content
                self.time = timestamp
        
        print("测试缓存管理:")
        print(f"  缓存大小限制: {handler.message_cache_size}")
        
        # 生成大量消息ID
        initial_cache_size = len(handler.processed_messages)
        print(f"  初始缓存大小: {initial_cache_size}")
        
        # 添加消息直到超过缓存限制
        for i in range(handler.message_cache_size + 100):
            msg = MockMessage(f"用户{i}", f"消息{i}", 1234567890 + i)
            handler._is_message_processed(handler._generate_message_id(msg))
        
        final_cache_size = len(handler.processed_messages)
        print(f"  最终缓存大小: {final_cache_size}")
        
        if final_cache_size <= handler.message_cache_size:
            print("✅ 缓存管理正常，防止了内存泄漏")
        else:
            print("❌ 缓存管理可能有问题")
        
        return True
        
    except Exception as e:
        print(f"❌ 缓存管理测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("开始测试重复消息检测功能...")
    
    # 测试消息ID生成
    handler = test_message_id_generation()
    if not handler:
        print("\n❌ 消息ID生成测试失败")
        return
    
    # 测试重复检测
    if test_duplicate_detection(handler):
        print("\n✅ 重复检测测试通过")
    else:
        print("\n❌ 重复检测测试失败")
        return
    
    # 测试缓存管理
    if test_cache_management(handler):
        print("\n✅ 缓存管理测试通过")
    else:
        print("\n❌ 缓存管理测试失败")
    
    print("\n" + "=" * 60)
    print("测试完成！")
    print("=" * 60)
    
    print("\n重复消息检测机制说明:")
    print("1. ✅ 使用MD5生成唯一消息ID")
    print("2. ✅ 基于发送者、内容、时间戳的组合")
    print("3. ✅ 自动缓存管理，防止内存泄漏")
    print("4. ✅ 确保一条消息只回复一次")
    print("5. ✅ 支持不同时间的相同内容消息")

if __name__ == "__main__":
    main()
