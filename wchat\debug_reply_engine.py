#!/usr/bin/env python3
"""
调试增强回复引擎统计信息
"""
import sys
from pathlib import Path

# 添加项目根目录到Python路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def debug_reply_engine():
    """调试增强回复引擎"""
    print("🔍 调试增强回复引擎统计信息")
    print("=" * 60)
    
    try:
        from src.bot.enhanced_reply_engine import EnhancedReplyEngine
        
        print("✅ 成功导入 EnhancedReplyEngine")
        
        # 创建引擎实例
        engine = EnhancedReplyEngine()
        print("✅ 成功创建引擎实例")
        
        # 测试统计信息方法
        print("\n🔍 测试 get_statistics() 方法...")
        try:
            stats = engine.get_statistics()
            print("✅ 成功调用 get_statistics()")
            print(f"📊 统计信息类型: {type(stats)}")
            print(f"📊 统计信息内容: {stats}")
            
            # 检查关键字段
            if isinstance(stats, dict):
                print("\n🔍 检查关键字段:")
                required_fields = ['ai_available', 'faq_count', 'product_count']
                for field in required_fields:
                    if field in stats:
                        print(f"   ✅ {field}: {stats[field]}")
                    else:
                        print(f"   ❌ 缺少字段: {field}")
            else:
                print(f"❌ 统计信息不是字典类型: {type(stats)}")
                
        except Exception as e:
            print(f"❌ 调用 get_statistics() 失败: {e}")
            import traceback
            traceback.print_exc()
        
        # 测试FAQ读取器
        print("\n🔍 测试 FAQ 读取器...")
        try:
            faq_reader = engine.faq_reader
            print(f"✅ FAQ读取器类型: {type(faq_reader)}")
            
            if hasattr(faq_reader, 'get_categories'):
                categories = faq_reader.get_categories()
                print(f"✅ FAQ分类: {categories}")
            else:
                print("❌ FAQ读取器缺少 get_categories 方法")
                
        except Exception as e:
            print(f"❌ 测试FAQ读取器失败: {e}")
        
        # 测试产品读取器
        print("\n🔍 测试产品读取器...")
        try:
            product_reader = engine.product_reader
            print(f"✅ 产品读取器类型: {type(product_reader)}")
            
            if hasattr(product_reader, 'get_product_categories'):
                categories = engine.get_product_categories()
                print(f"✅ 产品分类: {categories}")
            else:
                print("❌ 产品读取器缺少相关方法")
                
        except Exception as e:
            print(f"❌ 测试产品读取器失败: {e}")
            
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()

def debug_llm_service():
    """调试LLM服务"""
    print("\n🔍 调试LLM服务")
    print("=" * 60)
    
    try:
        from src.ai.llm_service import LLMService
        from config import config
        
        print("✅ 成功导入 LLMService 和 config")
        
        # 创建LLM服务实例
        llm_service = LLMService(
            api_key=config.ai.api_key,
            base_url=config.ai.base_url,
            model=config.ai.model,
            max_tokens=config.ai.max_tokens,
            temperature=config.ai.temperature
        )
        print("✅ 成功创建 LLMService 实例")
        
        # 测试可用性检查
        is_available = llm_service.is_available()
        print(f"✅ AI服务可用性: {is_available}")
        
    except Exception as e:
        print(f"❌ 调试LLM服务失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_reply_engine()
    debug_llm_service()
