"""
安装依赖包脚本
"""
import subprocess
import sys
import os

def install_package(package):
    """安装单个包"""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        print(f"✅ 成功安装: {package}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 安装失败: {package} - {e}")
        return False

def main():
    """主函数"""
    print("开始安装微信客服机器人依赖包...")
    print("=" * 50)
    
    # 基础依赖包
    basic_packages = [
        "flask",
        "requests",
        "jieba",
        "fuzzywuzzy",
        "python-Levenshtein",
        "colorlog"
    ]
    
    # 可选依赖包
    optional_packages = [
        "pandas",
        "openpyxl",
        "wxauto"
    ]
    
    success_count = 0
    total_count = len(basic_packages) + len(optional_packages)
    
    print("安装基础依赖包...")
    for package in basic_packages:
        if install_package(package):
            success_count += 1
    
    print("\n安装可选依赖包...")
    for package in optional_packages:
        if install_package(package):
            success_count += 1
    
    print("\n" + "=" * 50)
    print(f"安装完成: {success_count}/{total_count} 个包安装成功")
    
    if success_count >= len(basic_packages):
        print("✅ 基础功能可用")
        if success_count == total_count:
            print("✅ 所有功能可用")
        else:
            print("⚠️  部分高级功能可能不可用")
    else:
        print("❌ 基础依赖安装不完整，请手动安装")
    
    print("\n使用说明:")
    print("1. 运行 python web_config.py 打开配置界面")
    print("2. 运行 python run.py 启动机器人")

if __name__ == "__main__":
    main()
