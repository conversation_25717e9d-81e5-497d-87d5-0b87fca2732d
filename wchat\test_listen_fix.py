"""
测试修复后的监听功能
验证全局消息监听是否正常工作
"""
import os
import sys
import time

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def test_global_message_listening():
    """测试全局消息监听"""
    print("=" * 50)
    print("测试全局消息监听")
    print("=" * 50)
    
    try:
        from wxauto import WeChat
        
        wx = WeChat()
        print(f"微信连接成功，用户: {wx.nickname}")
        
        print("\n开始全局消息监听（30秒）...")
        print("请在微信中发送一些测试消息")
        
        start_time = time.time()
        processed_messages = set()
        
        while time.time() - start_time < 30:
            try:
                # 获取所有新消息
                msgs = wx.GetNewMessage()
                if msgs:
                    for msg in msgs:
                        # 获取消息信息
                        sender = getattr(msg, 'sender', 'Unknown')
                        content = getattr(msg, 'content', 'No content')
                        msg_time = getattr(msg, 'time', time.time())
                        msg_type = type(msg).__name__
                        
                        # 创建消息ID避免重复处理
                        msg_id = f"{sender}_{msg_time}_{content[:20]}"
                        if msg_id in processed_messages:
                            continue
                        processed_messages.add(msg_id)
                        
                        print(f"\n收到消息:")
                        print(f"  发送者: {sender}")
                        print(f"  内容: {content}")
                        print(f"  类型: {msg_type}")
                        print(f"  时间: {msg_time}")
                        
                        # 检查是否应该处理这条消息
                        should_process = should_process_message(msg, ["樂"])
                        print(f"  是否处理: {should_process}")
                        
                        if should_process and 'Text' in msg_type and sender != "self":
                            print(f"  → 这条消息会被机器人处理")
                
                time.sleep(1)
                
            except Exception as e:
                print(f"获取消息失败: {e}")
                time.sleep(2)
        
        print(f"\n监听测试完成，共处理 {len(processed_messages)} 条消息")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


def should_process_message(msg, listen_list):
    """判断是否应该处理消息（模拟机器人逻辑）"""
    try:
        sender = getattr(msg, 'sender', '')
        
        # 如果监听列表为空，处理所有消息（除了自己的）
        if not listen_list:
            return sender != "self"
        
        # 检查发送者是否在监听列表中
        for chat_name in listen_list:
            if chat_name in sender or sender in chat_name:
                return True
        
        return False
        
    except Exception as e:
        print(f"判断消息处理失败: {e}")
        return False


def test_message_filtering():
    """测试消息过滤逻辑"""
    print("\n" + "=" * 50)
    print("测试消息过滤逻辑")
    print("=" * 50)
    
    # 模拟消息对象
    class MockMessage:
        def __init__(self, sender, content, msg_type="TextMessage"):
            self.sender = sender
            self.content = content
            self.time = time.time()
            self.__class__.__name__ = msg_type
    
    # 测试用例
    test_messages = [
        MockMessage("樂", "测试消息1"),
        MockMessage("self", "自己发送的消息"),
        MockMessage("其他用户", "其他用户的消息"),
        MockMessage("樂的朋友", "包含监听对象名称的消息"),
    ]
    
    listen_list = ["樂"]
    
    print(f"监听列表: {listen_list}")
    print("\n测试结果:")
    
    for i, msg in enumerate(test_messages):
        should_process = should_process_message(msg, listen_list)
        print(f"{i+1}. 发送者: {msg.sender}, 内容: {msg.content}")
        print(f"   是否处理: {'✅ 是' if should_process else '❌ 否'}")


def test_wechat_handler():
    """测试修复后的WeChatHandler"""
    print("\n" + "=" * 50)
    print("测试修复后的WeChatHandler")
    print("=" * 50)
    
    try:
        from src.bot.wechat_handler import WeChatHandler
        
        handler = WeChatHandler()
        
        if handler.initialize_wechat():
            print("✅ 微信初始化成功")
            
            # 获取状态
            status = handler.get_status()
            print(f"监听列表: {status['listen_list']}")
            print(f"自动回复: {status['auto_reply']}")
            
            # 测试消息处理判断
            class MockMessage:
                def __init__(self, sender, content):
                    self.sender = sender
                    self.content = content
                    self.time = time.time()
            
            test_msg = MockMessage("樂", "测试消息")
            should_process = handler._should_process_message(test_msg)
            print(f"测试消息处理判断: {'✅ 通过' if should_process else '❌ 失败'}")
            
            return True
        else:
            print("❌ 微信初始化失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主函数"""
    print("修复后的监听功能测试")
    print("=" * 50)
    
    # 测试消息过滤逻辑
    test_message_filtering()
    
    # 测试WeChatHandler
    if test_wechat_handler():
        print("\n" + "=" * 50)
        choice = input("是否进行实时消息监听测试？(y/n): ").strip().lower()
        if choice in ['y', 'yes', '是']:
            test_global_message_listening()
    
    print("\n测试完成！")


if __name__ == "__main__":
    main()
