<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据管理 - 私域自动化</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .upload-area {
            border: 2px dashed #dee2e6;
            border-radius: 8px;
            padding: 40px;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        .upload-area:hover {
            border-color: #0d6efd;
            background-color: #f8f9fa;
        }
        .upload-area.dragover {
            border-color: #0d6efd;
            background-color: #e7f3ff;
        }
        .file-info {
            background-color: #f8f9fa;
            border-radius: 6px;
            padding: 10px;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="bi bi-robot"></i> 私域自动化
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/dashboard">
                    <i class="bi bi-speedometer2"></i> 控制台
                </a>
                <a class="nav-link active" href="/data_management">
                    <i class="bi bi-database"></i> 数据管理
                </a>
                <a class="nav-link" href="/logout">
                    <i class="bi bi-box-arrow-right"></i> 退出
                </a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <h2><i class="bi bi-database"></i> 数据管理</h2>
                <p class="text-muted">管理FAQ和产品数据库，支持上传、替换和备份</p>
            </div>
        </div>

        <!-- FAQ数据管理 -->
        <div class="row mt-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="bi bi-question-circle"></i> FAQ数据管理</h5>
                    </div>
                    <div class="card-body">
                        <p class="text-muted">管理常见问题和答案数据</p>
                        
                        <!-- 上传区域 -->
                        <div class="upload-area" id="faqUploadArea">
                            <i class="bi bi-cloud-upload fs-1 text-muted"></i>
                            <p class="mt-2">点击或拖拽文件到此处上传FAQ数据</p>
                            <p class="text-muted small">支持 .xlsx 和 .csv 格式</p>
                            <input type="file" id="faqFileInput" accept=".xlsx,.csv" style="display: none;">
                        </div>
                        
                        <!-- 文件信息 -->
                        <div id="faqFileInfo" class="file-info" style="display: none;">
                            <div class="d-flex justify-content-between align-items-center">
                                <span id="faqFileName"></span>
                                <button class="btn btn-sm btn-outline-danger" onclick="clearFaqFile()">
                                    <i class="bi bi-x"></i>
                                </button>
                            </div>
                        </div>
                        
                        <!-- 选项 -->
                        <div class="mt-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="faqReplace">
                                <label class="form-check-label" for="faqReplace">
                                    替换现有数据（否则合并）
                                </label>
                            </div>
                        </div>
                        
                        <!-- 按钮 -->
                        <div class="mt-3 d-grid gap-2">
                            <button class="btn btn-primary" onclick="uploadFaq()" id="faqUploadBtn" disabled>
                                <i class="bi bi-upload"></i> 上传FAQ数据
                            </button>
                            <a href="/api/download_template/faq" class="btn btn-outline-secondary">
                                <i class="bi bi-download"></i> 下载FAQ模板
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 产品数据管理 -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="bi bi-box"></i> 产品数据管理</h5>
                    </div>
                    <div class="card-body">
                        <p class="text-muted">管理产品信息和关键词数据</p>
                        
                        <!-- 上传区域 -->
                        <div class="upload-area" id="productsUploadArea">
                            <i class="bi bi-cloud-upload fs-1 text-muted"></i>
                            <p class="mt-2">点击或拖拽文件到此处上传产品数据</p>
                            <p class="text-muted small">支持 .xlsx 和 .csv 格式</p>
                            <input type="file" id="productsFileInput" accept=".xlsx,.csv" style="display: none;">
                        </div>
                        
                        <!-- 文件信息 -->
                        <div id="productsFileInfo" class="file-info" style="display: none;">
                            <div class="d-flex justify-content-between align-items-center">
                                <span id="productsFileName"></span>
                                <button class="btn btn-sm btn-outline-danger" onclick="clearProductsFile()">
                                    <i class="bi bi-x"></i>
                                </button>
                            </div>
                        </div>
                        
                        <!-- 选项 -->
                        <div class="mt-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="productsReplace">
                                <label class="form-check-label" for="productsReplace">
                                    替换现有数据（否则合并）
                                </label>
                            </div>
                        </div>
                        
                        <!-- 按钮 -->
                        <div class="mt-3 d-grid gap-2">
                            <button class="btn btn-primary" onclick="uploadProducts()" id="productsUploadBtn" disabled>
                                <i class="bi bi-upload"></i> 上传产品数据
                            </button>
                            <a href="/api/download_template/products" class="btn btn-outline-secondary">
                                <i class="bi bi-download"></i> 下载产品模板
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 数据预览 -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="bi bi-eye"></i> 数据预览</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>FAQ数据结构</h6>
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>字段名</th>
                                                <th>说明</th>
                                                <th>必填</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td>问题关键词</td>
                                                <td>用逗号分隔的关键词</td>
                                                <td>✅</td>
                                            </tr>
                                            <tr>
                                                <td>标准问题</td>
                                                <td>标准化的问题描述</td>
                                                <td>✅</td>
                                            </tr>
                                            <tr>
                                                <td>回复内容</td>
                                                <td>回复的答案内容</td>
                                                <td>✅</td>
                                            </tr>
                                            <tr>
                                                <td>分类</td>
                                                <td>问题分类</td>
                                                <td>✅</td>
                                            </tr>
                                            <tr>
                                                <td>状态</td>
                                                <td>启用/禁用</td>
                                                <td>✅</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <h6>产品数据结构</h6>
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>字段名</th>
                                                <th>说明</th>
                                                <th>必填</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td>产品名称</td>
                                                <td>产品的名称</td>
                                                <td>✅</td>
                                            </tr>
                                            <tr>
                                                <td>产品关键词</td>
                                                <td>用逗号分隔的关键词</td>
                                                <td>✅</td>
                                            </tr>
                                            <tr>
                                                <td>产品描述</td>
                                                <td>产品的详细描述</td>
                                                <td>✅</td>
                                            </tr>
                                            <tr>
                                                <td>价格</td>
                                                <td>产品价格（数字）</td>
                                                <td>✅</td>
                                            </tr>
                                            <tr>
                                                <td>分类</td>
                                                <td>产品分类</td>
                                                <td>✅</td>
                                            </tr>
                                            <tr>
                                                <td>产品图片</td>
                                                <td>图片文件路径</td>
                                                <td>❌</td>
                                            </tr>
                                            <tr>
                                                <td>详细信息</td>
                                                <td>详细的产品信息</td>
                                                <td>❌</td>
                                            </tr>
                                            <tr>
                                                <td>库存状态</td>
                                                <td>有货/缺货</td>
                                                <td>❌</td>
                                            </tr>
                                            <tr>
                                                <td>状态</td>
                                                <td>上架/下架</td>
                                                <td>❌</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Toast 通知 -->
    <div class="toast-container position-fixed bottom-0 end-0 p-3">
        <div id="toast" class="toast" role="alert">
            <div class="toast-header">
                <strong class="me-auto">通知</strong>
                <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
            </div>
            <div class="toast-body" id="toastBody">
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // FAQ文件上传处理
        const faqUploadArea = document.getElementById('faqUploadArea');
        const faqFileInput = document.getElementById('faqFileInput');
        const faqFileInfo = document.getElementById('faqFileInfo');
        const faqFileName = document.getElementById('faqFileName');
        const faqUploadBtn = document.getElementById('faqUploadBtn');

        // 产品文件上传处理
        const productsUploadArea = document.getElementById('productsUploadArea');
        const productsFileInput = document.getElementById('productsFileInput');
        const productsFileInfo = document.getElementById('productsFileInfo');
        const productsFileName = document.getElementById('productsFileName');
        const productsUploadBtn = document.getElementById('productsUploadBtn');

        // FAQ上传区域事件
        faqUploadArea.addEventListener('click', () => faqFileInput.click());
        faqUploadArea.addEventListener('dragover', handleDragOver);
        faqUploadArea.addEventListener('drop', (e) => handleDrop(e, 'faq'));
        faqFileInput.addEventListener('change', (e) => handleFileSelect(e, 'faq'));

        // 产品上传区域事件
        productsUploadArea.addEventListener('click', () => productsFileInput.click());
        productsUploadArea.addEventListener('dragover', handleDragOver);
        productsUploadArea.addEventListener('drop', (e) => handleDrop(e, 'products'));
        productsFileInput.addEventListener('change', (e) => handleFileSelect(e, 'products'));

        function handleDragOver(e) {
            e.preventDefault();
            e.currentTarget.classList.add('dragover');
        }

        function handleDrop(e, type) {
            e.preventDefault();
            e.currentTarget.classList.remove('dragover');
            
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                if (type === 'faq') {
                    faqFileInput.files = files;
                    handleFileSelect({target: faqFileInput}, 'faq');
                } else {
                    productsFileInput.files = files;
                    handleFileSelect({target: productsFileInput}, 'products');
                }
            }
        }

        function handleFileSelect(e, type) {
            const file = e.target.files[0];
            if (file) {
                if (type === 'faq') {
                    faqFileName.textContent = file.name;
                    faqFileInfo.style.display = 'block';
                    faqUploadBtn.disabled = false;
                } else {
                    productsFileName.textContent = file.name;
                    productsFileInfo.style.display = 'block';
                    productsUploadBtn.disabled = false;
                }
            }
        }

        function clearFaqFile() {
            faqFileInput.value = '';
            faqFileInfo.style.display = 'none';
            faqUploadBtn.disabled = true;
        }

        function clearProductsFile() {
            productsFileInput.value = '';
            productsFileInfo.style.display = 'none';
            productsUploadBtn.disabled = true;
        }

        function uploadFaq() {
            const file = faqFileInput.files[0];
            const replace = document.getElementById('faqReplace').checked;
            
            if (!file) {
                showToast('请选择文件', 'error');
                return;
            }

            const formData = new FormData();
            formData.append('file', file);
            formData.append('replace', replace);

            faqUploadBtn.disabled = true;
            faqUploadBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> 上传中...';

            fetch('/api/upload_faq', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.error) {
                    showToast(data.error, 'error');
                } else {
                    showToast(data.message, 'success');
                    clearFaqFile();
                }
            })
            .catch(error => {
                showToast('上传失败: ' + error.message, 'error');
            })
            .finally(() => {
                faqUploadBtn.disabled = false;
                faqUploadBtn.innerHTML = '<i class="bi bi-upload"></i> 上传FAQ数据';
            });
        }

        function uploadProducts() {
            const file = productsFileInput.files[0];
            const replace = document.getElementById('productsReplace').checked;
            
            if (!file) {
                showToast('请选择文件', 'error');
                return;
            }

            const formData = new FormData();
            formData.append('file', file);
            formData.append('replace', replace);

            productsUploadBtn.disabled = true;
            productsUploadBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> 上传中...';

            fetch('/api/upload_products', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.error) {
                    showToast(data.error, 'error');
                } else {
                    showToast(data.message, 'success');
                    clearProductsFile();
                }
            })
            .catch(error => {
                showToast('上传失败: ' + error.message, 'error');
            })
            .finally(() => {
                productsUploadBtn.disabled = false;
                productsUploadBtn.innerHTML = '<i class="bi bi-upload"></i> 上传产品数据';
            });
        }

        function showToast(message, type) {
            const toast = document.getElementById('toast');
            const toastBody = document.getElementById('toastBody');
            
            toastBody.textContent = message;
            
            if (type === 'error') {
                toast.className = 'toast bg-danger text-white';
            } else {
                toast.className = 'toast bg-success text-white';
            }
            
            const bsToast = new bootstrap.Toast(toast);
            bsToast.show();
        }
    </script>
</body>
</html>
