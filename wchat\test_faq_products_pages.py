#!/usr/bin/env python3
"""
测试FAQ和产品页面数据加载
"""
import requests
from bs4 import BeautifulSoup

def test_faq_products_pages():
    """测试FAQ和产品页面"""
    base_url = "http://localhost:5000"
    
    print("🎯 FAQ和产品页面数据加载测试")
    print("=" * 60)
    
    # 创建会话
    session = requests.Session()
    
    # 登录
    print("🔐 登录系统...")
    login_data = {
        'username': 'admin',
        'password': 'admin123'
    }
    
    login_response = session.post(f"{base_url}/login", data=login_data)
    print(f"登录状态: {login_response.status_code}")
    
    if login_response.status_code != 200:
        print("❌ 登录失败")
        return
    
    # 测试FAQ页面
    print("\n📋 测试FAQ页面...")
    print("-" * 40)
    
    faq_response = session.get(f"{base_url}/faq")
    print(f"FAQ页面状态: {faq_response.status_code}")
    
    if faq_response.status_code == 200:
        soup = BeautifulSoup(faq_response.text, 'html.parser')
        
        # 检查是否有数据
        faq_table = soup.find('table')
        if faq_table:
            rows = faq_table.find_all('tr')[1:]  # 跳过表头
            print(f"✅ 找到FAQ表格，数据行数: {len(rows)}")
            
            if len(rows) > 0:
                print("✅ FAQ数据加载成功")
                # 显示前几条数据
                for i, row in enumerate(rows[:3]):
                    cells = row.find_all('td')
                    if len(cells) >= 3:
                        question = cells[0].get_text(strip=True)
                        category = cells[1].get_text(strip=True)
                        print(f"   {i+1}. 问题: {question[:30]}... 分类: {category}")
            else:
                print("❌ FAQ表格为空")
        else:
            # 检查是否显示"暂无数据"
            no_data = soup.find(text=lambda text: text and "暂无" in text)
            if no_data:
                print("❌ 显示暂无FAQ数据")
            else:
                print("❌ 未找到FAQ表格或数据提示")
    else:
        print("❌ FAQ页面访问失败")
    
    # 测试产品页面
    print("\n🛍️ 测试产品页面...")
    print("-" * 40)
    
    products_response = session.get(f"{base_url}/products")
    print(f"产品页面状态: {products_response.status_code}")
    
    if products_response.status_code == 200:
        soup = BeautifulSoup(products_response.text, 'html.parser')
        
        # 检查是否有数据
        product_table = soup.find('table')
        if product_table:
            rows = product_table.find_all('tr')[1:]  # 跳过表头
            print(f"✅ 找到产品表格，数据行数: {len(rows)}")
            
            if len(rows) > 0:
                print("✅ 产品数据加载成功")
                # 显示前几条数据
                for i, row in enumerate(rows[:3]):
                    cells = row.find_all('td')
                    if len(cells) >= 3:
                        name = cells[0].get_text(strip=True)
                        category = cells[1].get_text(strip=True)
                        price = cells[2].get_text(strip=True)
                        print(f"   {i+1}. 产品: {name} 分类: {category} 价格: {price}")
            else:
                print("❌ 产品表格为空")
        else:
            # 检查是否显示"暂无数据"
            no_data = soup.find(text=lambda text: text and "暂无" in text)
            if no_data:
                print("❌ 显示暂无产品数据")
            else:
                print("❌ 未找到产品表格或数据提示")
    else:
        print("❌ 产品页面访问失败")
    
    print("\n" + "=" * 60)
    print("📊 测试结果总结:")
    
    faq_success = faq_response.status_code == 200 and "找到FAQ表格" in locals().get('faq_result', '')
    products_success = products_response.status_code == 200 and "找到产品表格" in locals().get('products_result', '')
    
    if faq_response.status_code == 200:
        print("   FAQ页面访问: ✅ 正常")
    else:
        print("   FAQ页面访问: ❌ 异常")
        
    if products_response.status_code == 200:
        print("   产品页面访问: ✅ 正常")
    else:
        print("   产品页面访问: ❌ 异常")

if __name__ == "__main__":
    test_faq_products_pages()
