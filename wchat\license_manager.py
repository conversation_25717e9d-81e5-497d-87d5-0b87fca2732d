#!/usr/bin/env python3
"""
许可证管理工具
"""
import sys
import json
from pathlib import Path

# 添加项目路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

from src.security.hardware_lock import hardware_lock, create_license_generator

def show_hardware_info():
    """显示硬件信息"""
    print("🔍 硬件信息")
    print("=" * 50)
    
    hardware_info = hardware_lock.get_hardware_info()
    
    print(f"硬件ID: {hardware_info['hardware_id']}")
    print(f"系统平台: {hardware_info['platform']}")
    print(f"机器类型: {hardware_info['machine']}")
    print(f"处理器: {hardware_info['processor']}")
    print(f"操作系统: {hardware_info['system']}")
    print(f"计算机名: {hardware_info['node']}")

def check_license_status():
    """检查许可证状态"""
    print("📋 许可证状态")
    print("=" * 50)
    
    license_info = hardware_lock.check_license()
    
    if license_info["valid"]:
        print("✅ 许可证有效")
        print(f"到期日期: {license_info['expire_date']}")
        print(f"剩余天数: {license_info['days_remaining']} 天")
        print(f"授权功能:")
        for feature, enabled in license_info['features'].items():
            status = "✅" if enabled else "❌"
            print(f"  {status} {feature}")
    else:
        print("❌ 许可证无效")
        if license_info["error"]:
            print(f"错误: {license_info['error']}")

def install_license():
    """安装许可证"""
    print("📥 安装许可证")
    print("=" * 50)
    
    license_key = input("请输入许可证密钥: ").strip()
    
    if not license_key:
        print("❌ 许可证密钥不能为空")
        return
    
    print("正在安装许可证...")
    
    if hardware_lock.install_license(license_key):
        print("✅ 许可证安装成功！")
        check_license_status()
    else:
        print("❌ 许可证安装失败！")
        print("请检查:")
        print("  1. 许可证密钥是否正确")
        print("  2. 许可证是否适用于当前硬件")
        print("  3. 许可证是否已过期")

def generate_license():
    """生成许可证（仅用于授权）"""
    print("🔑 生成许可证")
    print("=" * 50)
    
    # 验证授权密码
    auth_password = input("请输入授权密码: ").strip()
    if auth_password != "WCHAT_ADMIN_2025":
        print("❌ 授权密码错误")
        return
    
    # 获取硬件ID
    hardware_id = input("请输入目标硬件ID: ").strip()
    if not hardware_id:
        print("❌ 硬件ID不能为空")
        return
    
    # 获取有效期
    try:
        days = int(input("请输入有效期天数 (默认365): ").strip() or "365")
    except ValueError:
        days = 365
    
    # 配置功能
    print("\n配置授权功能:")
    features = {}
    
    feature_list = [
        ("voice_recognition", "语音识别"),
        ("ai_chat", "AI对话"),
        ("product_recommendation", "产品推荐"),
        ("web_interface", "Web界面")
    ]
    
    for key, name in feature_list:
        enabled = input(f"启用 {name} (y/n, 默认y): ").strip().lower()
        features[key] = enabled != 'n'
    
    # 生成许可证
    generator = create_license_generator()
    license_key = generator(hardware_id, days, features)
    
    print(f"\n✅ 许可证生成成功!")
    print(f"硬件ID: {hardware_id}")
    print(f"有效期: {days} 天")
    print(f"许可证密钥:")
    print(f"{license_key}")
    
    # 保存到文件
    license_file = Path("generated_license.txt")
    with open(license_file, 'w', encoding='utf-8') as f:
        f.write(f"wchat许可证\n")
        f.write(f"=" * 50 + "\n")
        f.write(f"硬件ID: {hardware_id}\n")
        f.write(f"有效期: {days} 天\n")
        f.write(f"生成时间: {hardware_lock.check_license()['hardware_id']}\n")
        f.write(f"\n许可证密钥:\n")
        f.write(f"{license_key}\n")
    
    print(f"\n许可证已保存到: {license_file}")

def remove_license():
    """移除许可证"""
    print("🗑️ 移除许可证")
    print("=" * 50)
    
    confirm = input("确认移除许可证? (y/n): ").strip().lower()
    if confirm != 'y':
        print("操作已取消")
        return
    
    license_file = hardware_lock.license_file
    
    if license_file.exists():
        try:
            # 移除只读属性
            import os
            os.chmod(license_file, 0o666)
            license_file.unlink()
            print("✅ 许可证已移除")
        except Exception as e:
            print(f"❌ 移除许可证失败: {e}")
    else:
        print("❌ 许可证文件不存在")

def main():
    """主菜单"""
    while True:
        print("\n" + "=" * 60)
        print("           wchat许可证管理工具")
        print("=" * 60)
        
        print("1. 查看硬件信息")
        print("2. 检查许可证状态")
        print("3. 安装许可证")
        print("4. 生成许可证 (需要授权)")
        print("5. 移除许可证")
        print("0. 退出")
        
        choice = input("\n请选择操作 (0-5): ").strip()
        
        if choice == "1":
            show_hardware_info()
        elif choice == "2":
            check_license_status()
        elif choice == "3":
            install_license()
        elif choice == "4":
            generate_license()
        elif choice == "5":
            remove_license()
        elif choice == "0":
            print("👋 再见!")
            break
        else:
            print("❌ 无效选择，请重试")
        
        input("\n按回车键继续...")

if __name__ == "__main__":
    main()
