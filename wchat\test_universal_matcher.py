#!/usr/bin/env python3
"""
测试通用产品匹配器
"""
import os
import sys
from pathlib import Path

# 添加项目路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def test_universal_matcher():
    """测试通用匹配器"""
    print("🔧 测试通用产品匹配器")
    print("=" * 60)
    
    try:
        from src.database.universal_product_matcher import UniversalProductMatcher
        
        matcher = UniversalProductMatcher()
        
        # 测试产品数据（模拟不同类型的产品）
        test_products = [
            # 电子产品
            {"产品名称": "苹果iPhone 15", "分类": "智能手机", "产品描述": "最新款智能手机，A17芯片"},
            {"产品名称": "华为MateBook X", "分类": "笔记本电脑", "产品描述": "轻薄商务笔记本"},
            {"产品名称": "索尼WH-1000XM5", "分类": "无线耳机", "产品描述": "降噪蓝牙耳机"},
            {"产品名称": "罗技MX Master 3", "分类": "电脑配件", "产品描述": "无线办公鼠标"},
            {"产品名称": "机械键盘K380", "分类": "电脑配件", "产品描述": "蓝牙机械键盘"},
            
            # 家居用品
            {"产品名称": "宜家沙发", "分类": "家具", "产品描述": "三人座布艺沙发"},
            {"产品名称": "不锈钢炒锅", "分类": "厨具", "产品描述": "32cm不粘炒锅"},
            
            # 服装
            {"产品名称": "纯棉T恤", "分类": "上衣", "产品描述": "100%纯棉短袖T恤"},
            {"产品名称": "牛仔裤", "分类": "下装", "产品描述": "修身直筒牛仔裤"},
            {"产品名称": "运动鞋", "分类": "鞋类", "产品描述": "透气跑步鞋"},
            
            # 食品
            {"产品名称": "薯片", "分类": "零食", "产品描述": "原味薯片"},
            {"产品名称": "苹果汁", "分类": "饮料", "产品描述": "100%纯果汁"},
        ]
        
        # 测试查询
        test_queries = [
            # 电子产品查询
            "手机", "电脑", "耳机", "鼠标", "键盘",
            # 家居查询
            "沙发", "炒锅", "厨具",
            # 服装查询
            "T恤", "裤子", "鞋子",
            # 食品查询
            "零食", "饮料",
            # 组合查询
            "苹果手机", "华为电脑", "无线耳机", "办公鼠标"
        ]
        
        print("📦 测试产品:")
        for i, product in enumerate(test_products, 1):
            print(f"   {i}. {product['产品名称']} ({product['分类']})")
        
        print(f"\n🔍 测试查询匹配:")
        
        for query in test_queries:
            print(f"\n查询: '{query}'")
            matches = []
            
            for product in test_products:
                score = matcher.calculate_product_score(query, product)
                if score > 0.5:  # 阈值0.5
                    matches.append((product, score))
            
            # 按分数排序
            matches.sort(key=lambda x: x[1], reverse=True)
            
            if matches:
                print(f"  找到 {len(matches)} 个匹配:")
                for product, score in matches[:3]:  # 显示前3个
                    print(f"    {product['产品名称']} (分数: {score:.3f})")
            else:
                print(f"  无匹配产品")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_exclusion_rules():
    """测试排除规则"""
    print("\n🚫 测试排除规则")
    print("=" * 60)
    
    try:
        from src.database.universal_product_matcher import UniversalProductMatcher
        
        matcher = UniversalProductMatcher()
        
        # 测试排除规则的产品
        test_cases = [
            {
                "query": "电脑",
                "products": [
                    {"产品名称": "联想ThinkPad", "分类": "笔记本电脑", "产品描述": "商务笔记本"},
                    {"产品名称": "游戏鼠标", "分类": "电脑配件", "产品描述": "RGB游戏鼠标"},
                    {"产品名称": "机械键盘", "分类": "电脑配件", "产品描述": "青轴机械键盘"},
                ],
                "expected_top": "联想ThinkPad",
                "should_exclude": ["游戏鼠标", "机械键盘"]
            },
            {
                "query": "手机",
                "products": [
                    {"产品名称": "小米13", "分类": "智能手机", "产品描述": "5G智能手机"},
                    {"产品名称": "手机充电器", "分类": "手机配件", "产品描述": "快充充电器"},
                    {"产品名称": "手机壳", "分类": "手机配件", "产品描述": "透明保护壳"},
                ],
                "expected_top": "小米13",
                "should_exclude": ["手机充电器", "手机壳"]
            }
        ]
        
        all_passed = True
        
        for i, case in enumerate(test_cases, 1):
            print(f"\n测试案例 {i}: 查询 '{case['query']}'")
            
            matches = []
            for product in case['products']:
                score = matcher.calculate_product_score(case['query'], product)
                matches.append((product, score))
                print(f"  {product['产品名称']}: {score:.3f}")
            
            # 按分数排序
            matches.sort(key=lambda x: x[1], reverse=True)
            
            # 检查最高分是否是期望的产品
            if matches and matches[0][0]['产品名称'] == case['expected_top']:
                print(f"  ✅ 最高分产品正确: {case['expected_top']}")
            else:
                print(f"  ❌ 最高分产品错误: 期望 {case['expected_top']}, 实际 {matches[0][0]['产品名称'] if matches else 'None'}")
                all_passed = False
            
            # 检查排除的产品分数是否很低
            for product, score in matches:
                if product['产品名称'] in case['should_exclude']:
                    if score < 0.3:  # 排除的产品应该得分很低
                        print(f"  ✅ 正确排除: {product['产品名称']} (分数: {score:.3f})")
                    else:
                        print(f"  ❌ 排除失败: {product['产品名称']} (分数: {score:.3f})")
                        all_passed = False
        
        if all_passed:
            print(f"\n🎉 排除规则测试通过！")
        else:
            print(f"\n⚠️ 排除规则需要调整")
        
        return all_passed
        
    except Exception as e:
        print(f"❌ 排除规则测试失败: {e}")
        return False

def test_with_real_data():
    """使用真实数据测试"""
    print("\n📊 使用真实数据测试")
    print("=" * 60)
    
    try:
        from src.database.enhanced_reader import EnhancedProductReader
        from config import config
        
        product_reader = EnhancedProductReader(config.database.products_file)
        
        # 测试查询
        test_queries = [
            "电脑", "手机", "耳机", "鼠标", "充电器", "手表"
        ]
        
        for query in test_queries:
            print(f"\n查询: '{query}'")
            products = product_reader.search_products(query)
            
            if products:
                print(f"  找到 {len(products)} 个产品:")
                for product in products:
                    print(f"    {product['产品名称']} (分数: {product['score']:.3f})")
                    
                    # 显示产品特征分析
                    features = product_reader.matcher.extract_product_features(product)
                    print(f"      类型: {features['product_type']}, 配件: {features['is_accessory']}")
            else:
                print(f"  无匹配产品")
        
        return True
        
    except Exception as e:
        print(f"❌ 真实数据测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 通用产品匹配器测试")
    print()
    
    # 运行测试
    tests = [
        ("通用匹配", test_universal_matcher),
        ("排除规则", test_exclusion_rules),
        ("真实数据", test_with_real_data)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n🔍 运行测试: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
            if result:
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 总结
    print(f"\n" + "=" * 60)
    print(f"          测试总结")
    print(f"=" * 60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{status} {test_name}")
    
    print(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 通用产品匹配器工作正常！")
        print("\n💡 通用性特点:")
        print("   1. 支持任意产品类别")
        print("   2. 智能识别产品类型")
        print("   3. 自动排除不相关配件")
        print("   4. 基于语义的匹配算法")
        print("   5. 可扩展的产品层级关系")
        print("\n🚀 现在可以处理任何产品库了！")
    else:
        print("⚠️ 部分功能需要优化，请检查相关逻辑。")

if __name__ == "__main__":
    main()
