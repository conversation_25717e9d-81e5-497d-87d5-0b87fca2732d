#!/usr/bin/env python3
"""
测试语音消息支持功能
"""
import os
import sys
from pathlib import Path

# 添加项目路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def test_voice_configuration():
    """测试语音配置"""
    print("🔧 测试语音配置")
    print("=" * 60)
    
    try:
        from config import config
        
        print("语音相关配置:")
        print(f"  语音转文字: {config.wechat.voice_to_text}")
        print(f"  语音回复: {config.wechat.voice_reply_enabled}")
        print(f"  自动回复: {config.wechat.auto_reply}")
        print(f"  回复延迟: {config.wechat.reply_delay}秒")
        
        # 检查配置合理性
        if config.wechat.voice_to_text and config.wechat.voice_reply_enabled:
            print("✅ 语音功能配置完整")
        elif config.wechat.voice_to_text:
            print("⚠️ 只启用语音转文字，未启用语音回复")
        else:
            print("❌ 语音功能未启用")
        
        return config.wechat.voice_to_text
        
    except Exception as e:
        print(f"❌ 配置测试失败: {e}")
        return False

def test_voice_message_handling():
    """测试语音消息处理"""
    print("\n🎤 测试语音消息处理")
    print("=" * 60)
    
    try:
        from src.bot.wechat_handler import WeChatHandler
        
        # 创建模拟微信API
        class MockWeChatWithVoice:
            def __init__(self):
                self.sent_messages = []
                self.sent_files = []
                self.current_chat = None
                # 模拟语音转文字功能
                self.voice_texts = {
                    "voice_001.amr": "你好",
                    "voice_002.amr": "推荐一款手机",
                    "voice_003.amr": "这个产品怎么样",
                    "voice_004.amr": "谢谢"
                }
            
            def ChatWith(self, chat_name):
                self.current_chat = chat_name
                return True
            
            def SendMsg(self, msg):
                self.sent_messages.append((self.current_chat, msg))
                print(f"📤 发送消息: {msg}")
            
            def SendFiles(self, filepath):
                self.sent_files.append((self.current_chat, filepath))
                filename = os.path.basename(filepath)
                print(f"📷 发送图片: {filename}")
            
            def GetVoiceText(self, voice_file):
                """模拟微信语音转文字"""
                return self.voice_texts.get(voice_file, None)
            
            def VoiceToText(self, voice_file):
                """模拟微信VoiceToText API"""
                return self.voice_texts.get(voice_file, None)
        
        # 创建处理器
        handler = WeChatHandler()
        mock_wx = MockWeChatWithVoice()
        handler.wx = mock_wx
        
        # 测试语音转文字功能
        print("语音转文字测试:")
        print("-" * 40)
        
        test_voices = [
            ("voice_001.amr", "你好"),
            ("voice_002.amr", "推荐一款手机"),
            ("voice_003.amr", "这个产品怎么样"),
            ("voice_004.amr", "谢谢"),
            ("voice_unknown.amr", None)  # 未知语音文件
        ]
        
        for voice_file, expected_text in test_voices:
            print(f"\n测试语音: {voice_file}")
            print(f"期望文字: {expected_text}")
            
            result_text = handler._convert_voice_to_text(voice_file, "测试用户")
            
            if result_text:
                print(f"转换结果: {result_text}")
                if expected_text and result_text == expected_text:
                    print("✅ 转换正确")
                elif expected_text:
                    print("⚠️ 转换结果与期望不符")
                else:
                    print("✅ 意外成功转换")
            else:
                print("转换结果: (失败)")
                if expected_text:
                    print("❌ 转换失败")
                else:
                    print("✅ 正确识别为无法转换")
        
        return True
        
    except Exception as e:
        print(f"❌ 语音处理测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_voice_message_flow():
    """测试完整的语音消息流程"""
    print("\n🔄 测试完整语音消息流程")
    print("=" * 60)
    
    try:
        from src.bot.wechat_handler import WeChatHandler
        
        # 创建模拟微信API（同上）
        class MockWeChatWithVoice:
            def __init__(self):
                self.sent_messages = []
                self.sent_files = []
                self.current_chat = None
                self.voice_texts = {
                    "voice_hello.amr": "你好",
                    "voice_phone.amr": "推荐一款手机",
                    "voice_thanks.amr": "谢谢"
                }
            
            def ChatWith(self, chat_name):
                self.current_chat = chat_name
                return True
            
            def SendMsg(self, msg):
                self.sent_messages.append((self.current_chat, msg))
                print(f"📤 回复: {msg}")
            
            def SendFiles(self, filepath):
                self.sent_files.append((self.current_chat, filepath))
                filename = os.path.basename(filepath)
                print(f"📷 发送图片: {filename}")
            
            def GetVoiceText(self, voice_file):
                return self.voice_texts.get(voice_file, None)
        
        # 创建处理器
        handler = WeChatHandler()
        mock_wx = MockWeChatWithVoice()
        handler.wx = mock_wx
        
        # 模拟语音消息对象
        voice_messages = [
            {
                "sender": "张三",
                "content": "voice_hello.amr",
                "type": "voice",
                "expected_text": "你好",
                "should_reply": True
            },
            {
                "sender": "李四", 
                "content": "voice_phone.amr",
                "type": "voice",
                "expected_text": "推荐一款手机",
                "should_reply": True
            },
            {
                "sender": "王五",
                "content": "voice_thanks.amr", 
                "type": "voice",
                "expected_text": "谢谢",
                "should_reply": True
            }
        ]
        
        print("完整语音消息流程测试:")
        print("-" * 40)
        
        for i, voice_msg in enumerate(voice_messages, 1):
            print(f"\n场景 {i}: {voice_msg['sender']} 发送语音")
            print(f"语音文件: {voice_msg['content']}")
            print(f"期望转换: {voice_msg['expected_text']}")
            
            # 模拟消息处理（简化版）
            try:
                # 检查消息类型
                if voice_msg['type'] in ['voice', 'audio']:
                    # 转换语音为文字
                    text_content = handler._convert_voice_to_text(
                        voice_msg['content'], 
                        voice_msg['sender']
                    )
                    
                    if text_content:
                        print(f"✅ 语音转文字: {text_content}")
                        
                        # 生成回复
                        reply, image_paths = handler._generate_reply(
                            text_content,
                            voice_msg['sender'],
                            voice_msg['sender']
                        )
                        
                        if reply:
                            # 发送回复
                            handler._send_reply(reply, voice_msg['sender'])
                            
                            # 发送图片（如果有）
                            if image_paths:
                                handler._send_product_images(image_paths, voice_msg['sender'])
                            
                            print(f"✅ 场景 {i} 处理成功")
                        else:
                            print(f"⚠️ 场景 {i} 无回复生成")
                    else:
                        print(f"❌ 场景 {i} 语音转文字失败")
                        
            except Exception as e:
                print(f"❌ 场景 {i} 处理异常: {e}")
        
        # 统计结果
        print(f"\n处理结果统计:")
        print(f"  发送文本: {len(mock_wx.sent_messages)} 条")
        print(f"  发送图片: {len(mock_wx.sent_files)} 张")
        
        return len(mock_wx.sent_messages) > 0
        
    except Exception as e:
        print(f"❌ 完整流程测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_voice_api_compatibility():
    """测试语音API兼容性"""
    print("\n🔌 测试语音API兼容性")
    print("=" * 60)
    
    try:
        from src.bot.wechat_handler import WeChatHandler
        
        handler = WeChatHandler()
        
        # 测试不同的微信API情况
        api_scenarios = [
            {
                "name": "支持GetVoiceText",
                "wx_attrs": {"GetVoiceText": lambda x: "测试文字1"}
            },
            {
                "name": "支持VoiceToText", 
                "wx_attrs": {"VoiceToText": lambda x: "测试文字2"}
            },
            {
                "name": "不支持语音API",
                "wx_attrs": {}
            },
            {
                "name": "API异常",
                "wx_attrs": {"GetVoiceText": lambda x: None}
            }
        ]
        
        for scenario in api_scenarios:
            print(f"\n测试场景: {scenario['name']}")
            
            # 创建模拟微信对象
            class MockWX:
                pass
            
            mock_wx = MockWX()
            for attr_name, attr_value in scenario['wx_attrs'].items():
                setattr(mock_wx, attr_name, attr_value)
            
            handler.wx = mock_wx
            
            # 测试语音转文字
            result = handler._convert_voice_to_text("test_voice.amr", "测试用户")
            
            if result:
                print(f"  转换结果: {result}")
                print("  ✅ 兼容")
            else:
                print("  转换结果: (失败)")
                print("  ⚠️ 不兼容或无法处理")
        
        return True
        
    except Exception as e:
        print(f"❌ API兼容性测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 语音消息支持功能测试")
    print()
    
    # 运行测试
    tests = [
        ("语音配置", test_voice_configuration),
        ("语音处理", test_voice_message_handling),
        ("完整流程", test_voice_message_flow),
        ("API兼容性", test_voice_api_compatibility)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n🔍 运行测试: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
            if result:
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 总结
    print(f"\n" + "=" * 60)
    print(f"          语音功能测试总结")
    print(f"=" * 60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{status} {test_name}")
    
    print(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 语音消息支持功能完全正常！")
        print("\n💡 功能特点:")
        print("   1. ✅ 自动识别语音消息")
        print("   2. ✅ 多种语音转文字API支持")
        print("   3. ✅ 语音转文字后正常回复")
        print("   4. ✅ 支持产品查询语音")
        print("   5. ✅ 配置开关控制")
        print("\n🚀 重启微信机器人，即可支持语音消息！")
        print("\n📋 使用说明:")
        print("   - 用户发送语音消息")
        print("   - 系统自动转换为文字")
        print("   - 按文字内容正常回复")
        print("   - 支持产品查询+图片发送")
    else:
        print("⚠️ 部分功能存在问题")
        print("\n💡 注意事项:")
        print("   - 需要微信API支持语音转文字")
        print("   - 确保语音配置已启用")
        print("   - 语音质量影响转换效果")

if __name__ == "__main__":
    main()
