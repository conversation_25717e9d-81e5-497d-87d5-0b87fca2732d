#!/usr/bin/env python3
"""
WChat 启动程序 - 带选项版本
"""
import os
import sys
import subprocess
import webbrowser
import time
import threading
from pathlib import Path

def print_banner():
    """打印启动横幅"""
    print("=" * 60)
    print("          WChat 智能客服系统 - 启动选项")
    print("=" * 60)

def start_web_only():
    """仅启动Web管理界面"""
    current_dir = Path(__file__).parent
    
    try:
        # 切换到Web目录
        web_dir = current_dir / "src" / "web"
        if not web_dir.exists():
            print("错误: 未找到Web应用目录")
            return None
        
        original_dir = os.getcwd()
        os.chdir(web_dir)
        
        # 设置环境变量
        env = os.environ.copy()
        env['PYTHONPATH'] = str(current_dir)
        
        # 启动Flask应用
        process = subprocess.Popen(
            [sys.executable, "app.py"],
            cwd=web_dir,
            env=env
        )
        
        print("✅ Web管理界面启动成功")
        return process
        
    except Exception as e:
        print(f"❌ Web管理界面启动失败: {e}")
        return None

def start_bot_only():
    """仅启动微信机器人"""
    current_dir = Path(__file__).parent
    
    try:
        # 检查run.py文件
        bot_file = current_dir / "run.py"
        if not bot_file.exists():
            print("❌ 未找到机器人启动文件 run.py")
            return None
        
        # 设置环境变量
        env = os.environ.copy()
        env['PYTHONPATH'] = str(current_dir)
        
        # 启动机器人
        process = subprocess.Popen(
            [sys.executable, "run.py"],
            cwd=current_dir,
            env=env
        )
        
        print("✅ 微信机器人启动成功")
        return process
        
    except Exception as e:
        print(f"❌ 微信机器人启动失败: {e}")
        return None

def start_both():
    """启动Web界面和微信机器人"""
    print("正在启动Web管理界面...")
    web_process = start_web_only()
    
    time.sleep(2)
    
    print("正在启动微信机器人...")
    bot_process = start_bot_only()
    
    return web_process, bot_process

def open_browser_delayed():
    """延迟打开浏览器"""
    time.sleep(3)
    try:
        webbrowser.open("http://localhost:5000")
        print("✅ 浏览器已打开")
    except:
        print("请手动访问: http://localhost:5000")

def main():
    """主函数"""
    print_banner()
    
    print("\n请选择启动模式:")
    print("1. 仅启动Web管理界面")
    print("2. 仅启动微信机器人")
    print("3. 同时启动Web界面和机器人 (推荐)")
    print("0. 退出")
    
    while True:
        try:
            choice = input("\n请输入选择 (0-3): ").strip()
            
            if choice == "0":
                print("退出程序")
                return
            elif choice == "1":
                print("\n启动Web管理界面...")
                web_process = start_web_only()
                if web_process:
                    # 打开浏览器
                    browser_thread = threading.Thread(target=open_browser_delayed)
                    browser_thread.daemon = True
                    browser_thread.start()
                    
                    print("\n" + "=" * 50)
                    print("Web管理界面启动成功!")
                    print("访问地址: http://localhost:5000")
                    print("默认账号: admin")
                    print("默认密码: admin123")
                    print("=" * 50)
                    print("按 Ctrl+C 停止")
                    
                    try:
                        web_process.wait()
                    except KeyboardInterrupt:
                        print("\n正在停止...")
                        web_process.terminate()
                        web_process.wait(timeout=5)
                        print("已停止")
                break
                
            elif choice == "2":
                print("\n启动微信机器人...")
                print("重要提示:")
                print("1. 请确保微信PC版已登录")
                print("2. 请先在Web配置界面中设置监听列表")
                print("3. 按 Ctrl+C 可以停止机器人")
                
                bot_process = start_bot_only()
                if bot_process:
                    print("\n" + "=" * 50)
                    print("微信机器人启动成功!")
                    print("机器人正在监听微信消息...")
                    print("=" * 50)
                    print("按 Ctrl+C 停止")
                    
                    try:
                        bot_process.wait()
                    except KeyboardInterrupt:
                        print("\n正在停止...")
                        bot_process.terminate()
                        bot_process.wait(timeout=5)
                        print("已停止")
                break
                
            elif choice == "3":
                print("\n启动完整系统...")
                web_process, bot_process = start_both()
                
                if web_process:
                    # 打开浏览器
                    browser_thread = threading.Thread(target=open_browser_delayed)
                    browser_thread.daemon = True
                    browser_thread.start()
                
                print("\n" + "=" * 50)
                print("WChat智能客服系统启动成功!")
                print("Web管理界面: http://localhost:5000")
                print("默认账号: admin")
                print("默认密码: admin123")
                if bot_process:
                    print("微信机器人: 已启动")
                else:
                    print("微信机器人: 启动失败")
                print("=" * 50)
                print("按 Ctrl+C 停止系统")
                
                try:
                    processes = []
                    if web_process:
                        processes.append(web_process)
                    if bot_process:
                        processes.append(bot_process)
                    
                    while processes:
                        for process in processes[:]:
                            if process.poll() is not None:
                                processes.remove(process)
                        time.sleep(1)
                        
                except KeyboardInterrupt:
                    print("\n正在停止系统...")
                    
                    if web_process and web_process.poll() is None:
                        web_process.terminate()
                        web_process.wait(timeout=5)
                    
                    if bot_process and bot_process.poll() is None:
                        bot_process.terminate()
                        bot_process.wait(timeout=5)
                    
                    print("系统已停止")
                break
                
            else:
                print("无效选择，请重新输入")
                
        except KeyboardInterrupt:
            print("\n\n退出程序")
            break
        except Exception as e:
            print(f"发生错误: {e}")
    
    print("\n感谢使用 WChat 智能客服系统!")

if __name__ == "__main__":
    main()
