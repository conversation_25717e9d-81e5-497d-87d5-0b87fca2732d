# 管理员许可证生成指南

## 🔐 概述

私域自动化系统现在支持管理员远程生成许可证功能。管理员可以通过Web界面为任何客户端设备生成定制化的许可证，支持自定义有效期和功能配置。

## 👨‍💼 管理员权限

### 权限级别说明

系统支持两种用户权限：

1. **普通用户** (`admin123`)
   - 查看系统状态
   - 管理FAQ和产品数据
   - 查看和安装许可证
   - 基础系统配置

2. **管理员** (`admin888`)
   - 拥有普通用户的所有权限
   - **生成许可证** ⭐
   - 高级系统配置
   - 用户权限管理

## 🚀 快速开始

### 1. 启动系统并登录

```bash
# 启动Web配置界面
cd wchat
python web_config.py

# 或使用批处理文件
双击 启动.bat
```

### 2. 管理员登录

1. 浏览器访问：`http://127.0.0.1:5000`
2. 使用管理员密码：`admin888`
3. 登录后界面会显示"管理员"标识
4. 左侧菜单会出现"许可证生成器"选项

### 3. 访问许可证生成器

- 点击左侧菜单的"许可证生成器"
- 页面标题会显示"管理员专用"标识

## 📋 许可证生成流程

### 步骤1：获取客户端硬件信息

客户端需要先运行系统获取硬件ID：

```bash
# 客户端操作
1. 启动私域自动化系统
2. 访问Web界面并登录
3. 进入"许可证管理"页面
4. 复制显示的硬件ID
5. 将硬件ID发送给管理员
```

### 步骤2：配置许可证参数

在许可证生成器页面：

#### 🔧 基础配置
- **硬件ID**：粘贴客户端提供的硬件ID
- **有效期**：选择1-3650天（最多10年）
  - 快捷选项：30天、90天、1年、3年
  - 或手动输入天数

#### ⚙️ 功能配置
选择要授权的功能模块：

- ✅ **语音识别**：支持语音消息转文字
- ✅ **AI聊天**：智能对话和回复功能
- ✅ **产品管理**：产品库管理和推荐
- ✅ **FAQ管理**：常见问题库管理
- ✅ **Web界面**：Web配置界面访问

💡 **建议**：根据客户购买的版本选择对应功能

### 步骤3：生成许可证

1. 确认所有配置信息
2. 点击"生成许可证"按钮
3. 系统会显示生成结果

### 步骤4：交付许可证

生成成功后，可以通过以下方式交付：

#### 📋 复制许可证密钥
- 点击许可证密钥右侧的复制按钮
- 通过聊天工具发送给客户

#### 📄 复制完整信息
- 点击"复制完整信息"按钮
- 包含硬件ID、有效期、功能列表等详细信息

#### 💾 下载许可证文件
- 点击"下载许可证"按钮
- 生成包含许可证的文本文件
- 通过邮件或文件传输发送给客户

## 🛠️ 客户端安装流程

### 客户端操作步骤

1. **接收许可证**：从管理员处获取许可证密钥
2. **访问系统**：打开Web界面并登录
3. **进入许可证管理**：点击左侧菜单"许可证管理"
4. **安装许可证**：
   - 在"许可证操作"区域粘贴许可证密钥
   - 点击"安装"按钮
   - 等待安装完成
5. **验证状态**：确认许可证状态显示为"有效"

## 📊 许可证管理

### 生成记录

每次生成许可证时，系统会记录：
- 生成时间
- 硬件ID（部分显示）
- 有效期天数
- 授权功能列表

### 状态监控

管理员可以通过以下方式监控许可证状态：
- 查看系统日志文件
- 客户端反馈许可证状态
- 远程协助检查许可证

### 许可证更新

如需更新许可证：
1. 使用相同硬件ID生成新许可证
2. 客户端先移除旧许可证
3. 安装新许可证

## 🔒 安全特性

### 硬件绑定
- 每个许可证与特定硬件ID绑定
- 无法在其他设备上使用
- 防止许可证被复制或转移

### 数字签名
- 许可证使用数字签名技术
- 防止许可证被篡改
- 确保许可证的完整性

### 权限控制
- 只有管理员可以生成许可证
- 普通用户只能查看和安装
- 操作日志记录所有活动

## 🚨 故障排除

### 常见问题

#### 1. 无法访问许可证生成器
```
原因：使用普通用户密码登录
解决：使用管理员密码 admin888 重新登录
```

#### 2. 硬件ID格式错误
```
原因：硬件ID复制不完整或包含额外字符
解决：确保完整复制客户端显示的硬件ID
```

#### 3. 许可证生成失败
```
原因：网络问题或系统错误
解决：检查系统日志，重试生成操作
```

#### 4. 客户端安装失败
```
原因：许可证与硬件不匹配
解决：确认硬件ID正确，重新生成许可证
```

### 日志查看

系统日志位置：
- Windows：`wchat/logs/`
- 包含详细的操作记录和错误信息

## 📞 技术支持

### 联系方式

如遇到技术问题：
1. 查看系统日志获取详细错误信息
2. 记录操作步骤和错误截图
3. 联系技术支持并提供：
   - 错误信息
   - 系统版本
   - 操作环境

### 最佳实践

1. **定期备份**：备份许可证生成记录
2. **权限管理**：妥善保管管理员密码
3. **版本控制**：为不同版本客户生成对应功能的许可证
4. **客户服务**：及时响应客户的许可证需求

---

**私域自动化系统 - 管理员许可证生成**  
**安全可靠 · 功能强大 · 管理便捷** 🔐👨‍💼✨
