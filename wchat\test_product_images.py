#!/usr/bin/env python3
"""
测试产品图片功能
"""
import os
import sys
from pathlib import Path

# 添加项目路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def get_product_image_path(product_name: str, images_dir: str = "data/images") -> str:
    """
    根据产品名称获取对应的图片路径
    
    Args:
        product_name: 产品名称，如"智能手机A1"
        images_dir: 图片目录
        
    Returns:
        str: 图片文件路径，如果找不到则返回None
    """
    if not os.path.exists(images_dir):
        return None
    
    # 产品名称到图片文件的映射规则
    name_mapping = {
        "智能手机A1": "phone_a1.jpg",
        "无线蓝牙耳机B2": "earphone_b2.jpg", 
        "智能手表C3": "watch_c3.jpg",
        "笔记本电脑D4": "laptop_d4.jpg",
        "无线充电器E5": "charger_e5.jpg",
        "游戏鼠标F6": "mouse_f6.jpg"
    }
    
    # 查找对应的图片文件
    image_filename = name_mapping.get(product_name)
    if image_filename:
        image_path = os.path.join(images_dir, image_filename)
        if os.path.exists(image_path):
            return image_path
    
    # 如果没有精确匹配，尝试模糊匹配
    for filename in os.listdir(images_dir):
        if filename.endswith(('.jpg', '.jpeg', '.png', '.gif')):
            # 提取产品代码（如A1, B2等）
            if product_name:
                # 从产品名称中提取代码
                import re
                code_match = re.search(r'[A-Z]\d+', product_name)
                if code_match:
                    code = code_match.group().lower()
                    if code in filename.lower():
                        return os.path.join(images_dir, filename)
    
    return None

def format_product_reply_with_images(products: list, images_dir: str = "data/images") -> tuple:
    """
    格式化产品回复，包含图片信息
    
    Args:
        products: 产品列表
        images_dir: 图片目录
        
    Returns:
        tuple: (文本回复, 图片路径列表)
    """
    if not products:
        return "抱歉，没有找到相关产品。", []
    
    # 根据产品数量调整开场白
    if len(products) == 1:
        reply = f"为您推荐这款产品：\n\n"
    else:
        reply = f"为您推荐 {len(products)} 款相关产品：\n\n"
    
    image_paths = []
    
    for i, product in enumerate(products, 1):
        name = product.get('产品名称', '')
        desc = product.get('产品描述', '')
        price = product.get('价格', '')
        details = product.get('详细信息', '')
        
        reply += f"🛍️ {i}. {name}\n"
        reply += f"💰 价格：¥{price}\n"
        reply += f"📝 描述：{desc}\n"
        
        if details:
            reply += f"ℹ️ 详情：{details}\n"
        
        # 查找对应的图片
        image_path = get_product_image_path(name, images_dir)
        if image_path:
            image_paths.append(image_path)
            reply += f"📷 图片：{os.path.basename(image_path)}\n"
        
        reply += "\n"
    
    # 根据产品数量调整结尾
    if len(products) == 1:
        reply += "这款怎么样？"
    else:
        reply += "看看你喜欢哪款～"
    
    return reply, image_paths

def test_product_images():
    """测试产品图片功能"""
    print("=" * 60)
    print("          测试产品图片功能")
    print("=" * 60)
    
    try:
        from src.database.enhanced_reader import EnhancedProductReader
        from config import config
        
        # 加载产品数据
        product_reader = EnhancedProductReader(config.database.products_file)
        print(f"产品数据加载: {len(product_reader.data)} 条记录")
        
        # 测试产品查询
        test_queries = [
            "手机",
            "耳机", 
            "笔记本",
            "充电器",
            "鼠标"
        ]
        
        for query in test_queries:
            print(f"\n" + "-" * 50)
            print(f"查询: '{query}'")
            
            # 搜索产品
            products = product_reader.search_products(query)
            print(f"找到 {len(products)} 个产品")
            
            if products:
                # 格式化回复（包含图片）
                reply, image_paths = format_product_reply_with_images(products[:2])  # 最多显示2个
                
                print("\n回复内容:")
                print(reply)
                
                print(f"\n图片文件: {len(image_paths)} 张")
                for img_path in image_paths:
                    print(f"  - {img_path}")
                    # 检查文件是否存在
                    if os.path.exists(img_path):
                        file_size = os.path.getsize(img_path)
                        print(f"    ✅ 文件存在 ({file_size} bytes)")
                    else:
                        print(f"    ❌ 文件不存在")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_image_mapping():
    """测试图片映射功能"""
    print("\n" + "=" * 60)
    print("          测试图片映射")
    print("=" * 60)
    
    # 测试产品名称
    test_products = [
        "智能手机A1",
        "无线蓝牙耳机B2", 
        "智能手表C3",
        "笔记本电脑D4",
        "无线充电器E5",
        "游戏鼠标F6"
    ]
    
    for product_name in test_products:
        image_path = get_product_image_path(product_name)
        print(f"产品: {product_name}")
        if image_path:
            print(f"  ✅ 图片: {image_path}")
            if os.path.exists(image_path):
                file_size = os.path.getsize(image_path)
                print(f"     文件大小: {file_size} bytes")
            else:
                print(f"     ❌ 文件不存在")
        else:
            print(f"  ❌ 未找到对应图片")
        print()

if __name__ == "__main__":
    test_image_mapping()
    test_product_images()
