#!/usr/bin/env python3
"""
私域自动化系统 - 动态域名配置工具
支持花生壳、No-IP等动态域名服务
"""

import json
import os
import sys
import time
import requests
import subprocess
from pathlib import Path

class DDNSManager:
    """动态域名管理器"""
    
    def __init__(self):
        self.config_file = Path("config/ddns_config.json")
        self.config = self.load_config()
    
    def load_config(self):
        """加载DDNS配置"""
        if self.config_file.exists():
            with open(self.config_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        return {
            "provider": "",
            "domain": "",
            "username": "",
            "password": "",
            "update_interval": 300,  # 5分钟
            "last_ip": "",
            "enabled": False
        }
    
    def save_config(self):
        """保存DDNS配置"""
        self.config_file.parent.mkdir(exist_ok=True)
        with open(self.config_file, 'w', encoding='utf-8') as f:
            json.dump(self.config, f, indent=2, ensure_ascii=False)
    
    def get_public_ip(self):
        """获取公网IP"""
        try:
            response = requests.get("https://api.ipify.org", timeout=10)
            return response.text.strip()
        except:
            try:
                response = requests.get("https://ifconfig.me", timeout=10)
                return response.text.strip()
            except:
                return None
    
    def update_noip(self, domain, username, password, ip):
        """更新No-IP域名"""
        try:
            url = f"https://dynupdate.no-ip.com/nic/update"
            params = {
                'hostname': domain,
                'myip': ip
            }
            
            response = requests.get(url, params=params, 
                                  auth=(username, password), timeout=30)
            
            if response.status_code == 200:
                result = response.text.strip()
                if result.startswith('good') or result.startswith('nochg'):
                    return True, f"更新成功: {result}"
                else:
                    return False, f"更新失败: {result}"
            else:
                return False, f"HTTP错误: {response.status_code}"
        except Exception as e:
            return False, f"网络错误: {e}"
    
    def update_dyndns(self, domain, username, password, ip):
        """更新DynDNS域名"""
        try:
            url = f"https://members.dyndns.org/nic/update"
            params = {
                'hostname': domain,
                'myip': ip
            }
            
            response = requests.get(url, params=params, 
                                  auth=(username, password), timeout=30)
            
            if response.status_code == 200:
                result = response.text.strip()
                if result.startswith('good') or result.startswith('nochg'):
                    return True, f"更新成功: {result}"
                else:
                    return False, f"更新失败: {result}"
            else:
                return False, f"HTTP错误: {response.status_code}"
        except Exception as e:
            return False, f"网络错误: {e}"
    
    def update_ddns(self):
        """更新动态域名"""
        if not self.config.get('enabled', False):
            return False, "DDNS未启用"
        
        current_ip = self.get_public_ip()
        if not current_ip:
            return False, "无法获取公网IP"
        
        # 检查IP是否变化
        if current_ip == self.config.get('last_ip', ''):
            return True, f"IP未变化: {current_ip}"
        
        provider = self.config.get('provider', '').lower()
        domain = self.config.get('domain', '')
        username = self.config.get('username', '')
        password = self.config.get('password', '')
        
        if not all([provider, domain, username, password]):
            return False, "DDNS配置不完整"
        
        # 根据服务商更新域名
        if provider == 'no-ip':
            success, message = self.update_noip(domain, username, password, current_ip)
        elif provider == 'dyndns':
            success, message = self.update_dyndns(domain, username, password, current_ip)
        else:
            return False, f"不支持的服务商: {provider}"
        
        if success:
            self.config['last_ip'] = current_ip
            self.save_config()
        
        return success, message

def configure_ddns():
    """配置动态域名"""
    ddns = DDNSManager()
    
    print("\n" + "=" * 60)
    print("           动态域名配置")
    print("=" * 60)
    
    # 选择服务商
    print("\n支持的动态域名服务商:")
    print("1. No-IP (no-ip.com)")
    print("2. DynDNS (dyn.com)")
    print("3. 花生壳 (oray.com) - 需要客户端")
    
    choice = input("\n请选择服务商 (1-3): ").strip()
    
    if choice == '1':
        provider = 'no-ip'
    elif choice == '2':
        provider = 'dyndns'
    elif choice == '3':
        print("\n花生壳需要安装专用客户端，请访问 https://hsk.oray.com/")
        input("按回车键继续...")
        return
    else:
        print("❌ 无效选择")
        return
    
    # 配置域名信息
    domain = input(f"\n请输入域名 (如: myhost.{provider}.org): ").strip()
    username = input("请输入用户名: ").strip()
    password = input("请输入密码: ").strip()
    
    if not all([domain, username, password]):
        print("❌ 配置信息不完整")
        return
    
    # 更新间隔
    interval = input("请输入更新间隔（秒，默认300）: ").strip()
    if interval.isdigit():
        interval = int(interval)
    else:
        interval = 300
    
    # 保存配置
    ddns.config.update({
        'provider': provider,
        'domain': domain,
        'username': username,
        'password': password,
        'update_interval': interval,
        'enabled': True
    })
    
    ddns.save_config()
    
    # 测试更新
    print(f"\n🔄 测试域名更新...")
    success, message = ddns.update_ddns()
    
    if success:
        print(f"✅ {message}")
        print(f"🌐 域名访问地址: http://{domain}:5000")
    else:
        print(f"❌ {message}")
    
    print("\n✅ 动态域名配置完成")

def test_ddns():
    """测试动态域名"""
    ddns = DDNSManager()
    
    print("\n" + "=" * 60)
    print("           动态域名测试")
    print("=" * 60)
    
    if not ddns.config.get('enabled', False):
        print("❌ 动态域名未配置或未启用")
        return
    
    print(f"服务商: {ddns.config.get('provider', '未知')}")
    print(f"域名: {ddns.config.get('domain', '未设置')}")
    print(f"上次IP: {ddns.config.get('last_ip', '未知')}")
    
    # 获取当前IP
    current_ip = ddns.get_public_ip()
    print(f"当前IP: {current_ip or '获取失败'}")
    
    # 测试更新
    print(f"\n🔄 正在测试域名更新...")
    success, message = ddns.update_ddns()
    
    if success:
        print(f"✅ {message}")
    else:
        print(f"❌ {message}")

def start_ddns_service():
    """启动动态域名服务"""
    ddns = DDNSManager()
    
    if not ddns.config.get('enabled', False):
        print("❌ 动态域名未配置或未启用")
        return
    
    print("\n" + "=" * 60)
    print("           动态域名服务")
    print("=" * 60)
    print(f"域名: {ddns.config.get('domain', '')}")
    print(f"更新间隔: {ddns.config.get('update_interval', 300)}秒")
    print("按 Ctrl+C 停止服务")
    print("=" * 60)
    
    try:
        while True:
            success, message = ddns.update_ddns()
            timestamp = time.strftime("%Y-%m-%d %H:%M:%S")
            
            if success:
                print(f"[{timestamp}] ✅ {message}")
            else:
                print(f"[{timestamp}] ❌ {message}")
            
            time.sleep(ddns.config.get('update_interval', 300))
    
    except KeyboardInterrupt:
        print("\n\n👋 动态域名服务已停止")

def show_ddns_status():
    """显示动态域名状态"""
    ddns = DDNSManager()
    
    print("\n" + "=" * 60)
    print("           动态域名状态")
    print("=" * 60)
    
    if not ddns.config.get('enabled', False):
        print("状态: 未启用")
        return
    
    print(f"状态: 已启用")
    print(f"服务商: {ddns.config.get('provider', '未设置')}")
    print(f"域名: {ddns.config.get('domain', '未设置')}")
    print(f"用户名: {ddns.config.get('username', '未设置')}")
    print(f"更新间隔: {ddns.config.get('update_interval', 300)}秒")
    print(f"上次IP: {ddns.config.get('last_ip', '未知')}")
    
    # 获取当前IP
    current_ip = ddns.get_public_ip()
    print(f"当前IP: {current_ip or '获取失败'}")
    
    # 显示访问地址
    domain = ddns.config.get('domain', '')
    if domain:
        print(f"\n访问地址:")
        print(f"  http://{domain}:5000")
        print(f"  https://{domain}:5000 (如果启用了HTTPS)")

def main():
    """主菜单"""
    while True:
        print("\n" + "=" * 60)
        print("           动态域名配置工具")
        print("=" * 60)
        print("1. 配置动态域名")
        print("2. 查看状态")
        print("3. 测试更新")
        print("4. 启动服务")
        print("0. 退出")
        print("=" * 60)
        
        choice = input("请选择操作 (0-4): ").strip()
        
        if choice == '1':
            configure_ddns()
        elif choice == '2':
            show_ddns_status()
        elif choice == '3':
            test_ddns()
        elif choice == '4':
            start_ddns_service()
        elif choice == '0':
            print("\n👋 感谢使用动态域名配置工具！")
            break
        else:
            print("❌ 无效选择，请重新输入")

if __name__ == "__main__":
    main()
