#!/usr/bin/env python3
"""
快速测试修复
"""
import sys
import os
from pathlib import Path

# 添加项目路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def test_format_function():
    """测试格式化函数"""
    print("测试产品回复格式化函数...")
    
    try:
        from src.database.enhanced_reader import format_product_reply
        
        # 测试单个产品
        single_product = [{
            'name': '智能手机A1',
            'price': '2999',
            'description': '高性能智能手机，拍照清晰',
            'stock': '有货',
            'details': '6.1英寸屏幕，128GB存储'
        }]
        
        print("\n单个产品:")
        print("-" * 40)
        reply = format_product_reply(single_product)
        print(reply)
        
        # 测试多个产品
        multiple_products = [
            {
                'name': '笔记本电脑D4',
                'price': '4999',
                'description': '轻薄便携，办公首选',
                'stock': '有货'
            },
            {
                'name': '游戏电脑G1',
                'price': '7999',
                'description': '高性能游戏电脑',
                'stock': '有货'
            }
        ]
        
        print("\n多个产品:")
        print("-" * 40)
        reply = format_product_reply(multiple_products)
        print(reply)
        
        # 测试无产品
        print("\n无产品:")
        print("-" * 40)
        reply = format_product_reply([])
        print(reply)
        
        print("\n✅ 格式化函数测试完成")
        
        # 检查修复效果
        print("\n修复效果检查:")
        test_reply = format_product_reply(multiple_products)
        
        if "联系客服" not in test_reply:
            print("✅ 已去掉'联系客服'")
        else:
            print("❌ 仍包含'联系客服'")
            
        if "为您推荐" in test_reply:
            print("✅ 使用'为您推荐'")
        else:
            print("❌ 未使用'为您推荐'")
            
        if "哪款" in test_reply or "感兴趣" in test_reply:
            print("✅ 包含互动询问")
        else:
            print("❌ 缺少互动询问")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_reply_engine():
    """测试回复引擎"""
    print("\n测试回复引擎...")
    
    try:
        from src.bot.enhanced_reply_engine import EnhancedReplyEngine
        
        reply_engine = EnhancedReplyEngine()
        
        # 测试产品查询
        queries = ["手机", "电脑", "耳机"]
        
        for query in queries:
            print(f"\n查询: {query}")
            print("-" * 30)
            
            reply = reply_engine.generate_reply(query)
            print(reply[:200] + "..." if len(reply) > 200 else reply)
        
        return True
        
    except Exception as e:
        print(f"❌ 回复引擎测试失败: {e}")
        return False

if __name__ == "__main__":
    print("快速测试产品回复修复...")
    
    if test_format_function():
        print("\n✅ 格式化函数测试通过")
    
    if test_reply_engine():
        print("\n✅ 回复引擎测试通过")
    
    print("\n🎉 修复测试完成！")
