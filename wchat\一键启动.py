#!/usr/bin/env python3
"""
私域自动化系统 - 一键启动脚本
"""

import os
import sys
import socket
import subprocess
import time
from pathlib import Path

def get_local_ip():
    """获取本机IP地址"""
    try:
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        local_ip = s.getsockname()[0]
        s.close()
        return local_ip
    except:
        return "127.0.0.1"

def check_python():
    """检查Python环境"""
    try:
        version = sys.version_info
        if version.major >= 3 and version.minor >= 8:
            return True, f"Python {version.major}.{version.minor}.{version.micro}"
        else:
            return False, f"Python版本过低: {version.major}.{version.minor}.{version.micro}"
    except:
        return False, "Python检查失败"

def check_dependencies():
    """检查依赖包"""
    try:
        import flask
        return True, "依赖包检查通过"
    except ImportError:
        return False, "Flask未安装"

def show_banner():
    """显示启动横幅"""
    print("=" * 50)
    print("   私域自动化系统 - 一键启动")
    print("=" * 50)
    print()

def show_network_info():
    """显示网络信息"""
    local_ip = get_local_ip()
    
    print("🌐 网络信息:")
    print(f"   本机IP: {local_ip}")
    print(f"   本地访问: http://127.0.0.1:5000")
    print(f"   局域网访问: http://{local_ip}:5000")
    print()

def show_login_info():
    """显示登录信息"""
    print("🔐 登录信息:")
    print("   普通用户密码: admin123")
    print("   管理员密码: admin888 (可生成许可证)")
    print()

def show_usage_tips():
    """显示使用提示"""
    print("💡 使用提示:")
    print("   1. 启动后在浏览器中访问上述地址")
    print("   2. 使用对应密码登录系统")
    print("   3. 管理员可以生成许可证")
    print("   4. 按 Ctrl+C 停止服务")
    print()

def start_web_server():
    """启动Web服务器"""
    print("🚀 正在启动Web服务器...")
    print("=" * 50)
    
    try:
        # 启动web_config.py
        subprocess.run([sys.executable, "web_config.py"], check=True)
    except KeyboardInterrupt:
        print("\n\n👋 服务已停止")
    except subprocess.CalledProcessError as e:
        print(f"\n❌ 启动失败: {e}")
    except FileNotFoundError:
        print("\n❌ 找不到web_config.py文件")

def main():
    """主函数"""
    # 显示横幅
    show_banner()
    
    # 检查Python环境
    python_ok, python_msg = check_python()
    if python_ok:
        print(f"✅ {python_msg}")
    else:
        print(f"❌ {python_msg}")
        print("请安装Python 3.8或更高版本")
        input("按回车键退出...")
        return
    
    # 检查依赖包
    deps_ok, deps_msg = check_dependencies()
    if deps_ok:
        print(f"✅ {deps_msg}")
    else:
        print(f"❌ {deps_msg}")
        print("正在尝试安装依赖包...")
        try:
            subprocess.run([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"], 
                         check=True, capture_output=True)
            print("✅ 依赖包安装成功")
        except:
            print("❌ 依赖包安装失败，请手动运行: pip install -r requirements.txt")
            input("按回车键退出...")
            return
    
    print()
    
    # 显示网络信息
    show_network_info()
    
    # 显示登录信息
    show_login_info()
    
    # 显示使用提示
    show_usage_tips()
    
    # 启动Web服务器
    start_web_server()

if __name__ == "__main__":
    main()
