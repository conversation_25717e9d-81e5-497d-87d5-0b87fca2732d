"""
Excel/CSV文件读取模块
负责读取和管理FAQ库和产品库的Excel/CSV文件
"""
import os
import csv
from typing import List, Dict, Any, Optional
from src.utils.logger import get_logger

try:
    import pandas as pd
    HAS_PANDAS = True
except ImportError:
    HAS_PANDAS = False

logger = get_logger("excel_reader")


class SimpleDataFrame:
    """简单的DataFrame替代品，用于不依赖pandas的情况"""

    def __init__(self, data):
        self.data = data if data else []
        self.columns = list(data[0].keys()) if data else []

    def __len__(self):
        return len(self.data)

    def __getitem__(self, key):
        if isinstance(key, str):
            # 返回列数据
            return [row.get(key, '') for row in self.data]
        elif isinstance(key, int):
            # 返回行数据
            return self.data[key] if 0 <= key < len(self.data) else {}
        else:
            return []

    def to_dict(self, orient='records'):
        if orient == 'records':
            return self.data
        return self.data

    def iterrows(self):
        for i, row in enumerate(self.data):
            yield i, SimpleRow(row)

    @property
    def empty(self):
        return len(self.data) == 0


class SimpleRow:
    """简单的行对象"""

    def __init__(self, data):
        self.data = data

    def get(self, key, default=''):
        return self.data.get(key, default)

    def to_dict(self):
        return self.data


class ExcelReader:
    """Excel文件读取器"""
    
    def __init__(self, file_path: str):
        self.file_path = file_path
        self.data = None
        self.last_modified = None
        
    def load_data(self, force_reload: bool = False) -> bool:
        """
        加载Excel数据
        
        Args:
            force_reload: 是否强制重新加载
            
        Returns:
            bool: 加载是否成功
        """
        try:
            if not os.path.exists(self.file_path):
                logger.warning(f"Excel文件不存在: {self.file_path}")
                return False
            
            # 检查文件是否有更新
            current_modified = os.path.getmtime(self.file_path)
            if not force_reload and self.last_modified == current_modified and self.data is not None:
                return True
            
            # 读取文件
            if self.file_path.endswith('.csv'):
                self.data = self._read_csv_file()
            elif HAS_PANDAS:
                if self.file_path.endswith('.xlsx') or self.file_path.endswith('.xls'):
                    self.data = pd.read_excel(self.file_path)
                else:
                    self.data = pd.read_csv(self.file_path)
            else:
                logger.error("无法读取文件：缺少pandas库且文件不是CSV格式")
                return False

            self.last_modified = current_modified

            if self.data is not None:
                logger.info(f"成功加载文件: {self.file_path}, 共{len(self.data)}条记录")
                return True
            else:
                return False
            
        except Exception as e:
            logger.error(f"加载文件失败 {self.file_path}: {e}")
            return False

    def _read_csv_file(self):
        """读取CSV文件并转换为DataFrame格式"""
        try:
            data_rows = []
            with open(self.file_path, 'r', encoding='utf-8-sig') as f:
                reader = csv.DictReader(f)
                for row in reader:
                    data_rows.append(row)

            if HAS_PANDAS:
                return pd.DataFrame(data_rows)
            else:
                # 创建简单的DataFrame替代品
                return SimpleDataFrame(data_rows)

        except Exception as e:
            logger.error(f"读取CSV文件失败: {e}")
            return None
    
    def get_data(self) -> Optional[pd.DataFrame]:
        """获取数据"""
        if self.data is None:
            self.load_data()
        return self.data
    
    def search_by_column(self, column: str, value: str, exact_match: bool = False) -> List[Dict]:
        """
        按列搜索数据
        
        Args:
            column: 列名
            value: 搜索值
            exact_match: 是否精确匹配
            
        Returns:
            List[Dict]: 匹配的记录列表
        """
        if self.data is None or column not in self.data.columns:
            return []
        
        try:
            if HAS_PANDAS and hasattr(self.data, 'astype'):
                # 使用pandas方法
                if exact_match:
                    mask = self.data[column].astype(str).str.lower() == value.lower()
                else:
                    mask = self.data[column].astype(str).str.contains(value, case=False, na=False)
                results = self.data[mask].to_dict('records')
            else:
                # 使用简单匹配
                results = []
                column_data = self.data[column] if hasattr(self.data, '__getitem__') else []
                for i, cell_value in enumerate(column_data):
                    cell_str = str(cell_value).lower()
                    value_str = value.lower()

                    if exact_match:
                        if cell_str == value_str:
                            results.append(self.data[i])
                    else:
                        if value_str in cell_str:
                            results.append(self.data[i])

            return results
            
        except Exception as e:
            logger.error(f"搜索数据失败: {e}")
            return []


class FAQReader(ExcelReader):
    """FAQ库读取器"""
    
    def __init__(self, file_path: str):
        super().__init__(file_path)
        self.required_columns = ['问题关键词', '标准问题', '回复内容', '分类', '状态']
    
    def validate_format(self) -> bool:
        """验证Excel格式是否正确"""
        if self.data is None:
            return False
        
        missing_columns = [col for col in self.required_columns if col not in self.data.columns]
        if missing_columns:
            logger.error(f"FAQ文件缺少必要列: {missing_columns}")
            return False
        
        return True
    
    def search_faq(self, question: str) -> List[Dict]:
        """
        搜索FAQ
        
        Args:
            question: 用户问题
            
        Returns:
            List[Dict]: 匹配的FAQ记录
        """
        if not self.load_data() or not self.validate_format():
            return []
        
        # 只返回状态为"启用"的记录
        if HAS_PANDAS and hasattr(self.data, 'iterrows'):
            active_data = self.data[self.data['状态'] == '启用']
            results = []

            # 在问题关键词中搜索
            for _, row in active_data.iterrows():
                keywords = str(row['问题关键词']).split(',')
                for keyword in keywords:
                    keyword = keyword.strip()
                    if keyword and keyword.lower() in question.lower():
                        results.append(row.to_dict())
                        break
        else:
            # 简单匹配方式
            results = []
            for row in self.data.data if hasattr(self.data, 'data') else []:
                if row.get('状态') == '启用':
                    keywords = str(row.get('问题关键词', '')).split(',')
                    for keyword in keywords:
                        keyword = keyword.strip()
                        if keyword and keyword.lower() in question.lower():
                            results.append(row)
                            break
        
        return results
    
    def get_categories(self) -> List[str]:
        """获取所有分类"""
        if not self.load_data() or not self.validate_format():
            return []

        if HAS_PANDAS and hasattr(self.data, 'dropna'):
            return self.data['分类'].dropna().unique().tolist()
        else:
            # 简单方式获取分类
            categories = set()
            for row in self.data.data if hasattr(self.data, 'data') else []:
                category = row.get('分类')
                if category and category.strip():
                    categories.add(category.strip())
            return list(categories)


class ProductReader(ExcelReader):
    """产品库读取器"""
    
    def __init__(self, file_path: str):
        super().__init__(file_path)
        self.required_columns = ['产品名称', '产品描述', '价格', '分类', '详细信息', '状态']
    
    def validate_format(self) -> bool:
        """验证Excel格式是否正确"""
        if self.data is None:
            return False
        
        missing_columns = [col for col in self.required_columns if col not in self.data.columns]
        if missing_columns:
            logger.error(f"产品文件缺少必要列: {missing_columns}")
            return False
        
        return True
    
    def search_products(self, query: str) -> List[Dict]:
        """
        搜索产品
        
        Args:
            query: 搜索关键词
            
        Returns:
            List[Dict]: 匹配的产品记录
        """
        if not self.load_data() or not self.validate_format():
            return []
        
        # 只返回状态为"上架"的记录
        active_data = self.data[self.data['状态'] == '上架']
        
        results = []
        
        # 在产品名称和描述中搜索
        for _, row in active_data.iterrows():
            product_name = str(row['产品名称']).lower()
            product_desc = str(row['产品描述']).lower()
            query_lower = query.lower()
            
            if query_lower in product_name or query_lower in product_desc:
                results.append(row.to_dict())
        
        return results
    
    def get_product_by_name(self, name: str) -> Optional[Dict]:
        """根据产品名称获取产品信息"""
        if not self.load_data() or not self.validate_format():
            return None
        
        active_data = self.data[self.data['状态'] == '上架']
        matches = active_data[active_data['产品名称'].str.contains(name, case=False, na=False)]
        
        if not matches.empty:
            return matches.iloc[0].to_dict()
        
        return None
    
    def get_categories(self) -> List[str]:
        """获取所有产品分类"""
        if not self.load_data() or not self.validate_format():
            return []
        
        return self.data['分类'].dropna().unique().tolist()
