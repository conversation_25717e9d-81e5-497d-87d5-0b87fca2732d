#!/usr/bin/env python3
"""
硬件锁定和许可证验证模块
"""
import hashlib
import platform
import subprocess
import uuid
import json
import os
from datetime import datetime, timedelta
from pathlib import Path
from typing import Optional, Dict, Any
import logging

logger = logging.getLogger(__name__)

class HardwareLock:
    """硬件锁定管理器"""
    
    def __init__(self):
        self.license_file = Path(__file__).parent.parent.parent / "license.dat"
        self.hardware_id = self._generate_hardware_id()
        
    def _generate_hardware_id(self) -> str:
        """生成唯一的硬件ID"""
        try:
            # 收集硬件信息
            hardware_info = []
            
            # 1. CPU信息
            try:
                if platform.system() == "Windows":
                    result = subprocess.run(['wmic', 'cpu', 'get', 'ProcessorId'], 
                                          capture_output=True, text=True, timeout=10)
                    if result.returncode == 0:
                        cpu_id = result.stdout.strip().split('\n')[-1].strip()
                        if cpu_id and cpu_id != "ProcessorId":
                            hardware_info.append(f"CPU:{cpu_id}")
                else:
                    # Linux/Mac
                    result = subprocess.run(['cat', '/proc/cpuinfo'], 
                                          capture_output=True, text=True, timeout=10)
                    if result.returncode == 0:
                        for line in result.stdout.split('\n'):
                            if 'serial' in line.lower():
                                hardware_info.append(f"CPU:{line.split(':')[-1].strip()}")
                                break
            except:
                pass
            
            # 2. 主板信息
            try:
                if platform.system() == "Windows":
                    result = subprocess.run(['wmic', 'baseboard', 'get', 'SerialNumber'], 
                                          capture_output=True, text=True, timeout=10)
                    if result.returncode == 0:
                        board_id = result.stdout.strip().split('\n')[-1].strip()
                        if board_id and board_id != "SerialNumber":
                            hardware_info.append(f"BOARD:{board_id}")
            except:
                pass
            
            # 3. MAC地址
            try:
                mac = ':'.join(['{:02x}'.format((uuid.getnode() >> elements) & 0xff) 
                               for elements in range(0,2*6,2)][::-1])
                hardware_info.append(f"MAC:{mac}")
            except:
                pass
            
            # 4. 磁盘序列号
            try:
                if platform.system() == "Windows":
                    result = subprocess.run(['wmic', 'diskdrive', 'get', 'SerialNumber'], 
                                          capture_output=True, text=True, timeout=10)
                    if result.returncode == 0:
                        lines = result.stdout.strip().split('\n')
                        for line in lines[1:]:  # 跳过标题行
                            disk_id = line.strip()
                            if disk_id and disk_id != "SerialNumber":
                                hardware_info.append(f"DISK:{disk_id}")
                                break
            except:
                pass
            
            # 5. 系统UUID
            try:
                if platform.system() == "Windows":
                    result = subprocess.run(['wmic', 'csproduct', 'get', 'UUID'], 
                                          capture_output=True, text=True, timeout=10)
                    if result.returncode == 0:
                        system_uuid = result.stdout.strip().split('\n')[-1].strip()
                        if system_uuid and system_uuid != "UUID":
                            hardware_info.append(f"UUID:{system_uuid}")
                else:
                    # Linux
                    try:
                        with open('/sys/class/dmi/id/product_uuid', 'r') as f:
                            system_uuid = f.read().strip()
                            hardware_info.append(f"UUID:{system_uuid}")
                    except:
                        pass
            except:
                pass
            
            # 如果没有收集到足够的硬件信息，使用备用方案
            if len(hardware_info) < 2:
                hardware_info.append(f"PLATFORM:{platform.platform()}")
                hardware_info.append(f"MACHINE:{platform.machine()}")
                hardware_info.append(f"PROCESSOR:{platform.processor()}")
            
            # 生成硬件指纹
            hardware_string = "|".join(sorted(hardware_info))
            hardware_hash = hashlib.sha256(hardware_string.encode()).hexdigest()
            
            logger.debug(f"硬件信息: {hardware_info}")
            logger.info(f"硬件ID: {hardware_hash[:16]}...")
            
            return hardware_hash
            
        except Exception as e:
            logger.error(f"生成硬件ID失败: {e}")
            # 备用方案：使用系统基本信息
            fallback_info = f"{platform.system()}-{platform.machine()}-{uuid.getnode()}"
            return hashlib.sha256(fallback_info.encode()).hexdigest()
    
    def generate_license_key(self, days: int = 365, features: Optional[Dict[str, Any]] = None) -> str:
        """生成许可证密钥（用于授权）"""
        if features is None:
            features = {
                "voice_recognition": True,
                "ai_chat": True,
                "product_recommendation": True,
                "web_interface": True,
                "max_users": 1
            }
        
        license_data = {
            "hardware_id": self.hardware_id,
            "issue_date": datetime.now().isoformat(),
            "expire_date": (datetime.now() + timedelta(days=days)).isoformat(),
            "features": features,
            "version": "1.0.0"
        }
        
        # 生成签名
        license_string = json.dumps(license_data, sort_keys=True)
        signature = hashlib.sha256(f"{license_string}WCHAT_SECRET_KEY_2025".encode()).hexdigest()
        
        license_data["signature"] = signature
        
        # 编码许可证
        license_json = json.dumps(license_data)
        license_encoded = license_json.encode().hex()
        
        return license_encoded
    
    def install_license(self, license_key: str) -> bool:
        """安装许可证"""
        try:
            # 解码许可证
            license_json = bytes.fromhex(license_key).decode()
            license_data = json.loads(license_json)
            
            # 验证许可证
            if not self._verify_license(license_data):
                logger.error("许可证验证失败")
                return False
            
            # 保存许可证文件
            with open(self.license_file, 'w', encoding='utf-8') as f:
                json.dump(license_data, f, indent=2)
            
            # 设置文件为只读
            try:
                os.chmod(self.license_file, 0o444)
            except:
                pass
            
            logger.info("许可证安装成功")
            return True
            
        except Exception as e:
            logger.error(f"安装许可证失败: {e}")
            return False
    
    def _verify_license(self, license_data: Dict[str, Any]) -> bool:
        """验证许可证"""
        try:
            # 检查必要字段
            required_fields = ["hardware_id", "issue_date", "expire_date", "signature"]
            for field in required_fields:
                if field not in license_data:
                    logger.error(f"许可证缺少必要字段: {field}")
                    return False
            
            # 验证硬件ID
            if license_data["hardware_id"] != self.hardware_id:
                logger.error("硬件ID不匹配")
                return False
            
            # 验证签名
            temp_data = license_data.copy()
            signature = temp_data.pop("signature")
            license_string = json.dumps(temp_data, sort_keys=True)
            expected_signature = hashlib.sha256(f"{license_string}WCHAT_SECRET_KEY_2025".encode()).hexdigest()
            
            if signature != expected_signature:
                logger.error("许可证签名无效")
                return False
            
            # 验证有效期
            expire_date = datetime.fromisoformat(license_data["expire_date"])
            if datetime.now() > expire_date:
                logger.error("许可证已过期")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"验证许可证异常: {e}")
            return False
    
    def check_license(self) -> Dict[str, Any]:
        """检查许可证状态"""
        result = {
            "valid": False,
            "hardware_id": self.hardware_id,
            "error": None,
            "features": {},
            "expire_date": None,
            "days_remaining": 0
        }
        
        try:
            # 检查许可证文件是否存在
            if not self.license_file.exists():
                result["error"] = "许可证文件不存在"
                return result
            
            # 读取许可证文件
            with open(self.license_file, 'r', encoding='utf-8') as f:
                license_data = json.load(f)
            
            # 验证许可证
            if not self._verify_license(license_data):
                result["error"] = "许可证验证失败"
                return result
            
            # 计算剩余天数
            expire_date = datetime.fromisoformat(license_data["expire_date"])
            days_remaining = (expire_date - datetime.now()).days
            
            result.update({
                "valid": True,
                "features": license_data.get("features", {}),
                "expire_date": license_data["expire_date"],
                "days_remaining": max(0, days_remaining)
            })
            
            return result
            
        except Exception as e:
            result["error"] = f"检查许可证异常: {e}"
            return result
    
    def get_hardware_info(self) -> Dict[str, str]:
        """获取硬件信息（用于生成许可证）"""
        return {
            "hardware_id": self.hardware_id,
            "platform": platform.platform(),
            "machine": platform.machine(),
            "processor": platform.processor(),
            "system": platform.system(),
            "node": platform.node()
        }

def create_license_generator():
    """创建许可证生成器（仅用于授权）"""
    def generate_license_for_hardware(hardware_id: str, days: int = 365, features: Optional[Dict[str, Any]] = None) -> str:
        """为指定硬件ID生成许可证"""
        if features is None:
            features = {
                "voice_recognition": True,
                "ai_chat": True,
                "product_recommendation": True,
                "web_interface": True,
                "max_users": 1
            }
        
        license_data = {
            "hardware_id": hardware_id,
            "issue_date": datetime.now().isoformat(),
            "expire_date": (datetime.now() + timedelta(days=days)).isoformat(),
            "features": features,
            "version": "1.0.0"
        }
        
        # 生成签名
        license_string = json.dumps(license_data, sort_keys=True)
        signature = hashlib.sha256(f"{license_string}WCHAT_SECRET_KEY_2025".encode()).hexdigest()
        
        license_data["signature"] = signature
        
        # 编码许可证
        license_json = json.dumps(license_data)
        license_encoded = license_json.encode().hex()
        
        return license_encoded
    
    return generate_license_for_hardware

# 全局硬件锁实例
hardware_lock = HardwareLock()

def check_authorization() -> bool:
    """检查授权状态"""
    license_status = hardware_lock.check_license()
    return license_status["valid"]

def get_license_info() -> Dict[str, Any]:
    """获取许可证信息"""
    return hardware_lock.check_license()

def install_license_key(license_key: str) -> bool:
    """安装许可证密钥"""
    return hardware_lock.install_license(license_key)

def get_hardware_fingerprint() -> str:
    """获取硬件指纹"""
    return hardware_lock.hardware_id
