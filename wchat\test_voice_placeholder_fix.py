#!/usr/bin/env python3
"""
测试语音占位符修复效果
"""
import os
import sys
from pathlib import Path

# 添加项目路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def test_voice_placeholder_detection():
    """测试语音占位符检测"""
    print("🔍 测试语音占位符检测")
    print("=" * 60)
    
    try:
        from src.bot.wechat_handler import WeChatHandler
        
        handler = WeChatHandler()
        
        # 测试用例
        test_cases = [
            # 语音占位符 (应该被检测为True)
            {"content": "[语音]2秒,未播放", "expected": True, "desc": "标准语音占位符"},
            {"content": "[语音]5秒,未播放", "expected": True, "desc": "5秒语音占位符"},
            {"content": "语音消息 3秒", "expected": True, "desc": "语音消息格式"},
            {"content": "voice message 2s", "expected": True, "desc": "英文语音消息"},
            
            # 正常文字 (应该被检测为False)
            {"content": "你好", "expected": False, "desc": "普通问候"},
            {"content": "推荐一款手机", "expected": False, "desc": "产品查询"},
            {"content": "谢谢你的帮助", "expected": False, "desc": "感谢消息"},
            {"content": "今天天气不错", "expected": False, "desc": "闲聊内容"},
            
            # 边界情况
            {"content": "语音", "expected": False, "desc": "单独的语音关键词"},
            {"content": "播放音乐", "expected": False, "desc": "包含播放但非占位符"},
            {"content": "", "expected": False, "desc": "空字符串"},
        ]
        
        print("语音占位符检测测试:")
        correct = 0
        total = len(test_cases)
        
        for i, case in enumerate(test_cases, 1):
            content = case["content"]
            expected = case["expected"]
            desc = case["desc"]
            
            result = handler._is_voice_placeholder(content)
            is_correct = result == expected
            
            status = "✅" if is_correct else "❌"
            print(f"{status} 测试{i}: {desc}")
            print(f"    内容: '{content}'")
            print(f"    结果: {result} (期望: {expected})")
            
            if is_correct:
                correct += 1
            else:
                print(f"    ⚠️ 检测错误")
        
        print(f"\n测试结果: {correct}/{total} 通过 ({correct/total*100:.1f}%)")
        return correct == total
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_voice_guidance_message():
    """测试语音引导消息"""
    print("\n💬 测试语音引导消息")
    print("=" * 60)
    
    try:
        from src.bot.wechat_handler import WeChatHandler
        
        handler = WeChatHandler()
        
        guidance = handler._get_voice_guidance_message()
        
        print("语音引导消息内容:")
        print("-" * 40)
        print(guidance)
        print("-" * 40)
        
        # 检查引导消息的质量
        checks = []
        
        if "语音消息" in guidance:
            checks.append("✅ 提及语音消息")
        else:
            checks.append("❌ 未提及语音消息")
        
        if "文字" in guidance:
            checks.append("✅ 建议使用文字")
        else:
            checks.append("❌ 未建议使用文字")
        
        if "转文字" in guidance:
            checks.append("✅ 提及转文字功能")
        else:
            checks.append("❌ 未提及转文字功能")
        
        if len(guidance) > 20:
            checks.append("✅ 内容充实")
        else:
            checks.append("❌ 内容过短")
        
        print(f"\n引导消息质量检查:")
        for check in checks:
            print(f"  {check}")
        
        return all("✅" in check for check in checks)
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def simulate_voice_message_handling():
    """模拟语音消息处理流程"""
    print("\n🎤 模拟语音消息处理流程")
    print("=" * 60)
    
    try:
        from src.bot.wechat_handler import WeChatHandler
        
        # 创建模拟微信对象
        class MockWeChat:
            def __init__(self):
                self.sent_messages = []
                self.current_chat = None
            
            def ChatWith(self, chat_name):
                self.current_chat = chat_name
                return True
            
            def SendMsg(self, msg):
                self.sent_messages.append((self.current_chat, msg))
                print(f"📤 发送消息到 {self.current_chat}: {msg[:50]}...")
        
        handler = WeChatHandler()
        mock_wx = MockWeChat()
        handler.wx = mock_wx
        
        # 模拟语音消息场景
        voice_scenarios = [
            {
                "sender": "用户A",
                "content": "[语音]3秒,未播放",
                "type": "voice",
                "desc": "标准语音占位符"
            },
            {
                "sender": "用户B", 
                "content": "推荐一款手机",
                "type": "voice",
                "desc": "有效的语音转文字结果"
            }
        ]
        
        print("语音消息处理模拟:")
        
        for i, scenario in enumerate(voice_scenarios, 1):
            print(f"\n场景 {i}: {scenario['desc']}")
            print(f"  发送者: {scenario['sender']}")
            print(f"  内容: {scenario['content']}")
            print(f"  类型: {scenario['type']}")
            
            # 模拟语音转文字过程
            text_content = handler._convert_voice_to_text(scenario['content'], scenario['sender'])
            
            if text_content:
                if handler._is_voice_placeholder(text_content):
                    print(f"  检测结果: 语音占位符")
                    print(f"  处理方式: 发送引导消息")
                    
                    guidance = handler._get_voice_guidance_message()
                    handler._send_reply(guidance, scenario['sender'])
                    print(f"  ✅ 已发送引导消息")
                else:
                    print(f"  检测结果: 有效文字内容")
                    print(f"  处理方式: 正常回复处理")
                    print(f"  ✅ 继续正常消息处理流程")
            else:
                print(f"  检测结果: 转换失败")
                print(f"  处理方式: 发送引导消息")
                
                guidance = handler._get_voice_guidance_message()
                handler._send_reply(guidance, scenario['sender'])
                print(f"  ✅ 已发送引导消息")
        
        print(f"\n发送的消息统计:")
        print(f"  总计发送: {len(mock_wx.sent_messages)} 条消息")
        
        return True
        
    except Exception as e:
        print(f"❌ 模拟失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_file_path_detection():
    """测试文件路径检测"""
    print("\n📁 测试文件路径检测")
    print("=" * 60)
    
    try:
        from src.bot.wechat_handler import WeChatHandler
        
        handler = WeChatHandler()
        
        # 测试用例
        test_cases = [
            # 文件路径 (应该被检测为True)
            {"content": "/path/to/voice.amr", "expected": True, "desc": "Unix路径"},
            {"content": "C:\\voice\\file.wav", "expected": True, "desc": "Windows路径"},
            {"content": "voice_001.amr", "expected": True, "desc": "文件名"},
            {"content": "audio.mp3", "expected": True, "desc": "音频文件"},
            {"content": "12345abcdef", "expected": True, "desc": "短ID"},
            
            # 普通文字 (应该被检测为False)
            {"content": "你好世界", "expected": False, "desc": "普通文字"},
            {"content": "推荐一款手机产品", "expected": False, "desc": "长文字"},
            {"content": "今天天气很好", "expected": False, "desc": "包含空格的文字"},
        ]
        
        print("文件路径检测测试:")
        correct = 0
        total = len(test_cases)
        
        for i, case in enumerate(test_cases, 1):
            content = case["content"]
            expected = case["expected"]
            desc = case["desc"]
            
            result = handler._looks_like_file_path(content)
            is_correct = result == expected
            
            status = "✅" if is_correct else "❌"
            print(f"{status} 测试{i}: {desc}")
            print(f"    内容: '{content}'")
            print(f"    结果: {result} (期望: {expected})")
            
            if is_correct:
                correct += 1
        
        print(f"\n测试结果: {correct}/{total} 通过 ({correct/total*100:.1f}%)")
        return correct == total
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🔧 语音占位符修复测试")
    print()
    
    # 运行测试
    tests = [
        ("语音占位符检测", test_voice_placeholder_detection),
        ("语音引导消息", test_voice_guidance_message),
        ("语音消息处理", simulate_voice_message_handling),
        ("文件路径检测", test_file_path_detection)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n🔍 运行测试: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
            if result:
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 总结
    print(f"\n" + "=" * 60)
    print(f"          语音占位符修复测试总结")
    print(f"=" * 60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{status} {test_name}")
    
    print(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 语音占位符修复完成！")
        print("\n💡 修复效果:")
        print("   1. ✅ 正确识别语音占位符 '[语音]X秒,未播放'")
        print("   2. ✅ 避免将占位符当作转换结果")
        print("   3. ✅ 提供用户友好的引导消息")
        print("   4. ✅ 引导用户使用文字或微信转文字功能")
        print("\n🚀 重启机器人测试语音消息处理!")
    else:
        print("⚠️ 部分测试失败，请检查代码")

if __name__ == "__main__":
    main()
