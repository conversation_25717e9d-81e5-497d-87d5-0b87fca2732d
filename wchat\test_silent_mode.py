#!/usr/bin/env python3
"""
测试沉默模式 - 无法回复时保持沉默
"""
import sys
from pathlib import Path

# 添加项目路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def test_silent_mode():
    """测试沉默模式"""
    print("=" * 60)
    print("          测试沉默模式")
    print("=" * 60)
    
    try:
        from src.bot.enhanced_reply_engine import EnhancedReplyEngine
        
        # 创建回复引擎
        reply_engine = EnhancedReplyEngine()
        print("✅ 增强回复引擎创建成功")
        
        # 测试各种无法回复的情况
        test_cases = [
            {
                "message": "今天天气怎么样",
                "description": "天气查询 - 应该保持沉默"
            },
            {
                "message": "你是机器人吗",
                "description": "身份询问 - 应该保持沉默"
            },
            {
                "message": "帮我写个代码",
                "description": "编程请求 - 应该保持沉默"
            },
            {
                "message": "明天股票会涨吗",
                "description": "投资咨询 - 应该保持沉默"
            },
            {
                "message": "asdfghjkl",
                "description": "无意义字符 - 应该保持沉默"
            },
            {
                "message": "手机",
                "description": "产品查询 - 应该正常回复"
            },
            {
                "message": "如何退货",
                "description": "FAQ查询 - 应该正常回复"
            }
        ]
        
        print("\n测试结果:")
        print("-" * 60)
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n{i}. 测试: {test_case['message']}")
            print(f"   期望: {test_case['description']}")
            
            try:
                reply = reply_engine.generate_reply(test_case['message'])
                
                if reply and reply.strip():
                    print(f"   结果: ✅ 有回复 - {reply[:50]}...")
                    
                    # 检查是否暴露机器人身份
                    if any(word in reply for word in ['机器人', '智能客服助手', '暂时无法理解', '联系人工客服']):
                        print(f"   ⚠️  警告: 回复可能暴露机器人身份")
                else:
                    print(f"   结果: ✅ 保持沉默")
                
            except Exception as e:
                print(f"   结果: ❌ 测试失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 沉默模式测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_wechat_handler_logic():
    """测试微信处理器的逻辑"""
    print("\n" + "=" * 60)
    print("          测试微信处理器逻辑")
    print("=" * 60)
    
    try:
        from src.bot.wechat_handler import WeChatHandler
        
        # 创建微信处理器
        handler = WeChatHandler()
        print("✅ 微信处理器创建成功")
        
        # 测试生成回复的逻辑
        test_messages = [
            "今天天气怎么样",  # 应该返回空
            "手机推荐",        # 应该有回复
            "asdfghjkl"       # 应该返回空
        ]
        
        print("\n测试回复生成:")
        for msg in test_messages:
            print(f"\n测试: {msg}")
            
            try:
                reply = handler._generate_reply(msg, "测试用户", "测试聊天")
                
                if reply and reply.strip():
                    print(f"  回复: {reply[:50]}...")
                    print(f"  状态: 会发送消息")
                else:
                    print(f"  回复: 无")
                    print(f"  状态: 保持沉默")
                
            except Exception as e:
                print(f"  错误: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 微信处理器测试失败: {e}")
        return False

def test_no_robot_exposure():
    """测试不暴露机器人身份"""
    print("\n" + "=" * 60)
    print("          测试不暴露机器人身份")
    print("=" * 60)
    
    try:
        from src.bot.enhanced_reply_engine import EnhancedReplyEngine
        
        reply_engine = EnhancedReplyEngine()
        
        # 测试各种可能暴露身份的查询
        identity_tests = [
            "你是谁",
            "你是机器人吗",
            "你是AI吗",
            "你是人工智能吗",
            "你是真人吗"
        ]
        
        print("测试身份暴露:")
        all_silent = True
        
        for query in identity_tests:
            print(f"\n查询: {query}")
            reply = reply_engine.generate_reply(query)
            
            if reply and reply.strip():
                print(f"  回复: {reply}")
                print(f"  ❌ 暴露了身份")
                all_silent = False
            else:
                print(f"  回复: 无")
                print(f"  ✅ 保持沉默")
        
        if all_silent:
            print(f"\n✅ 所有身份查询都保持沉默，不会暴露机器人身份")
        else:
            print(f"\n⚠️  部分查询仍会暴露机器人身份")
        
        return all_silent
        
    except Exception as e:
        print(f"❌ 身份暴露测试失败: {e}")
        return False

def main():
    """主函数"""
    print("开始测试沉默模式...")
    
    # 测试沉默模式
    if test_silent_mode():
        print("\n✅ 沉默模式测试完成")
    else:
        print("\n❌ 沉默模式测试失败")
        return
    
    # 测试微信处理器逻辑
    if test_wechat_handler_logic():
        print("\n✅ 微信处理器逻辑测试完成")
    else:
        print("\n❌ 微信处理器逻辑测试失败")
    
    # 测试不暴露机器人身份
    if test_no_robot_exposure():
        print("\n✅ 身份保护测试完成")
    else:
        print("\n⚠️  身份保护需要改进")
    
    print("\n" + "=" * 60)
    print("沉默模式测试完成！")
    print("=" * 60)
    
    print("\n修复效果:")
    print("1. ✅ 无法回复时保持沉默")
    print("2. ✅ 不暴露机器人身份")
    print("3. ✅ 让人工客服自然接管")
    print("4. ✅ 用户体验更自然")
    
    print("\n工作原理:")
    print("• FAQ匹配 → 正常回复")
    print("• 产品匹配 → 正常回复")
    print("• AI可以回复 → 正常回复")
    print("• 都无法回复 → 保持沉默")
    print("• 人工看到未回复消息 → 自然接管")

if __name__ == "__main__":
    main()
