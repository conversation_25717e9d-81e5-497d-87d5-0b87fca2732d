#!/usr/bin/env python3
"""
WChat私域自动化 快速启动程序 - 简化版
"""
import os
import sys
import subprocess
import webbrowser
import time
import threading
from pathlib import Path

def start_bot():
    """启动微信机器人"""
    current_dir = Path(__file__).parent

    try:
        # 检查run.py文件
        bot_file = current_dir / "run.py"
        if not bot_file.exists():
            print("警告: 未找到机器人启动文件")
            return None

        # 设置环境变量
        env = os.environ.copy()
        env['PYTHONPATH'] = str(current_dir)

        # 启动机器人
        process = subprocess.Popen(
            [sys.executable, "run.py"],
            cwd=current_dir,
            env=env
        )

        print("微信机器人启动中...")
        return process

    except Exception as e:
        print(f"启动机器人失败: {e}")
        return None

def check_license():
    """检查许可证"""
    try:
        from src.security.hardware_lock import check_authorization, get_license_info, get_hardware_fingerprint

        print("🔐 检查许可证...")

        # 获取硬件指纹
        hardware_id = get_hardware_fingerprint()
        print(f"硬件ID: {hardware_id[:16]}...")

        # 检查许可证
        if not check_authorization():
            license_info = get_license_info()
            print("❌ 许可证验证失败!")
            print(f"错误: {license_info.get('error', '未知错误')}")
            print(f"当前硬件ID: {hardware_id}")
            print()
            print("💡 解决方案:")
            print("   1. 运行 'python license_manager.py' 安装许可证")
            print("   2. 联系管理员获取许可证密钥")
            print("   3. 确保许可证适用于当前硬件")
            return False

        # 显示许可证信息
        license_info = get_license_info()
        print("✅ 许可证验证通过!")
        print(f"剩余天数: {license_info['days_remaining']} 天")

        # 检查即将过期
        if license_info['days_remaining'] <= 7:
            print(f"⚠️ 许可证将在 {license_info['days_remaining']} 天后过期!")
            print("请及时续期许可证")

        return True

    except ImportError:
        print("❌ 许可证模块加载失败!")
        return False
    except Exception as e:
        print(f"❌ 许可证检查异常: {e}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("          WChat私域自动化系统 - 快速启动")
    print("=" * 60)

    # 设置当前目录
    current_dir = Path(__file__).parent

    # 添加到Python路径
    if str(current_dir) not in sys.path:
        sys.path.insert(0, str(current_dir))

    # 检查许可证
    if not check_license():
        print("\n❌ 程序无法启动，请先安装有效的许可证")
        input("按回车键退出...")
        return

    print("=" * 60)

    print("正在启动Web服务器...")

    try:
        # 切换到Web目录
        web_dir = current_dir / "src" / "web"
        if not web_dir.exists():
            print("错误: 未找到Web应用目录")
            input("按回车键退出...")
            return

        original_dir = os.getcwd()
        os.chdir(web_dir)

        # 设置环境变量
        env = os.environ.copy()
        env['PYTHONPATH'] = str(current_dir)

        # 启动Flask应用
        web_process = subprocess.Popen(
            [sys.executable, "app.py"],
            env=env
        )

        print("Web服务器启动中...")
        time.sleep(2)

        # 启动微信机器人
        print("正在启动微信机器人...")
        bot_process = start_bot()
        time.sleep(1)
        
        # 在后台线程中打开浏览器
        def open_browser():
            time.sleep(2)
            try:
                webbrowser.open("http://localhost:5000")
                print("浏览器已打开: http://localhost:5000")
            except:
                print("请手动访问: http://localhost:5000")
        
        browser_thread = threading.Thread(target=open_browser)
        browser_thread.daemon = True
        browser_thread.start()
        
        print("\n" + "=" * 60)
        print("启动成功!")
        print("Web界面: http://localhost:5000")
        print("默认账号: admin")
        print("默认密码: admin123")
        if bot_process:
            print("微信机器人: 已启动")
        else:
            print("微信机器人: 启动失败 (请检查微信PC版是否已登录)")
        print("=" * 60)
        print("按 Ctrl+C 停止系统")
        print("=" * 60)

        # 等待进程
        try:
            processes = [web_process]
            if bot_process:
                processes.append(bot_process)

            # 等待任一进程结束
            while processes:
                for process in processes[:]:
                    if process.poll() is not None:
                        processes.remove(process)
                time.sleep(1)

        except KeyboardInterrupt:
            print("\n正在停止系统...")

            # 停止Web服务器
            if web_process.poll() is None:
                web_process.terminate()
                try:
                    web_process.wait(timeout=5)
                except subprocess.TimeoutExpired:
                    web_process.kill()

            # 停止机器人
            if bot_process and bot_process.poll() is None:
                bot_process.terminate()
                try:
                    bot_process.wait(timeout=5)
                except subprocess.TimeoutExpired:
                    bot_process.kill()

            print("系统已停止")
        
    except Exception as e:
        print(f"启动失败: {e}")
        input("按回车键退出...")
    finally:
        print("感谢使用 WChat私域自动化系统!")

if __name__ == "__main__":
    main()
