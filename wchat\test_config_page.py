#!/usr/bin/env python3
"""
测试配置页面访问
"""
import requests
import sys

def test_config_page():
    """测试配置页面"""
    try:
        # 先访问登录页面获取session
        session = requests.Session()
        
        # 登录
        login_data = {'password': 'admin123'}
        login_response = session.post('http://localhost:5000/login', data=login_data)
        print(f"登录状态: {login_response.status_code}")
        
        if login_response.status_code == 200:
            # 访问配置页面
            config_response = session.get('http://localhost:5000/config')
            print(f"配置页面状态: {config_response.status_code}")
            
            if config_response.status_code == 500:
                print("❌ 配置页面返回500错误")
                print("响应内容:")
                print(config_response.text[:1000])  # 显示前1000个字符
            elif config_response.status_code == 200:
                print("✅ 配置页面访问成功")
                if "监听模式" in config_response.text:
                    print("✅ 监听模式功能正常")
                else:
                    print("⚠️ 监听模式功能可能有问题")
            else:
                print(f"⚠️ 配置页面返回状态码: {config_response.status_code}")
        else:
            print("❌ 登录失败")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_config_page()
