#!/usr/bin/env python3
"""
调试微信语音消息对象结构
"""
import os
import sys
from pathlib import Path

# 添加项目路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def analyze_wxauto_voice_message():
    """分析wxauto语音消息对象结构"""
    print("🔍 分析wxauto语音消息对象结构")
    print("=" * 60)
    
    try:
        import wxauto
        
        print("wxauto版本信息:")
        print(f"  版本: {wxauto.__version__ if hasattr(wxauto, '__version__') else '未知'}")
        print(f"  路径: {wxauto.__file__}")
        
        # 检查wxauto的消息类型
        print(f"\nwxauto消息类型:")
        
        # 查看wxauto模块的内容
        wxauto_attrs = [attr for attr in dir(wxauto) if not attr.startswith('_')]
        print(f"  可用属性: {wxauto_attrs}")
        
        # 查找语音相关的类
        voice_related = [attr for attr in wxauto_attrs if 'voice' in attr.lower() or 'audio' in attr.lower() or 'message' in attr.lower()]
        print(f"  语音相关: {voice_related}")
        
        return True
        
    except ImportError as e:
        print(f"❌ wxauto导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        return False

def create_voice_file_extractor():
    """创建语音文件提取器"""
    print("\n🎤 创建语音文件提取器")
    print("=" * 60)
    
    extractor_code = '''
def extract_voice_file_from_message(msg_obj) -> Optional[str]:
    """
    从微信消息对象中提取语音文件路径
    
    Args:
        msg_obj: 微信消息对象
        
    Returns:
        Optional[str]: 语音文件路径，失败时返回None
    """
    try:
        logger.debug(f"开始提取语音文件 - 消息类型: {type(msg_obj)}")
        
        # 方法1: 直接属性访问
        voice_attrs = [
            'path', 'file_path', 'voice_path', 'filepath', 'voice_filepath',
            'file', 'voice_file', 'audio_path', 'audio_file', 'audio_filepath',
            'local_path', 'download_path', 'save_path', 'temp_path'
        ]
        
        for attr in voice_attrs:
            if hasattr(msg_obj, attr):
                value = getattr(msg_obj, attr, None)
                if value and isinstance(value, str):
                    # 检查是否看起来像文件路径
                    if _looks_like_voice_file_path(value):
                        logger.info(f"通过属性 {attr} 找到语音文件: {value}")
                        return value
        
        # 方法2: 调用下载/保存方法
        download_methods = [
            'download', 'save', 'save_file', 'download_file',
            'get_path', 'get_file_path', 'get_voice_path',
            'to_file', 'export', 'extract'
        ]
        
        for method_name in download_methods:
            if hasattr(msg_obj, method_name):
                try:
                    method = getattr(msg_obj, method_name)
                    if callable(method):
                        # 尝试不同的调用方式
                        try:
                            # 无参数调用
                            result = method()
                        except TypeError:
                            # 可能需要参数，尝试常见的参数
                            try:
                                result = method("temp_voice.amr")
                            except:
                                try:
                                    result = method(path="temp_voice.amr")
                                except:
                                    continue
                        
                        if result and isinstance(result, str):
                            if _looks_like_voice_file_path(result):
                                logger.info(f"通过方法 {method_name} 获取语音文件: {result}")
                                return result
                                
                except Exception as e:
                    logger.debug(f"调用方法 {method_name} 失败: {e}")
        
        # 方法3: 检查消息对象的所有属性
        all_attrs = [attr for attr in dir(msg_obj) if not attr.startswith('_')]
        logger.debug(f"消息对象所有属性: {all_attrs}")
        
        for attr in all_attrs:
            try:
                value = getattr(msg_obj, attr, None)
                if value and isinstance(value, str) and len(value) > 5:
                    if _looks_like_voice_file_path(value):
                        logger.info(f"通过遍历属性 {attr} 找到可能的语音文件: {value}")
                        return value
            except:
                continue
        
        logger.warning("未能从消息对象中提取语音文件路径")
        return None
        
    except Exception as e:
        logger.error(f"提取语音文件异常: {e}")
        return None

def _looks_like_voice_file_path(path: str) -> bool:
    """检查路径是否看起来像语音文件"""
    if not path or not isinstance(path, str):
        return False
    
    # 检查文件扩展名
    voice_extensions = ['.amr', '.wav', '.mp3', '.m4a', '.silk', '.pcm', '.aac', '.flac']
    path_lower = path.lower()
    
    for ext in voice_extensions:
        if path_lower.endswith(ext):
            return True
    
    # 检查路径特征
    if ('/' in path or '\\\\' in path) and ('voice' in path_lower or 'audio' in path_lower):
        return True
    
    # 检查是否包含临时文件特征
    temp_indicators = ['temp', 'tmp', 'cache', 'download']
    for indicator in temp_indicators:
        if indicator in path_lower and any(ext in path_lower for ext in voice_extensions):
            return True
    
    return False
'''
    
    print("语音文件提取器代码:")
    print(extractor_code)
    
    return extractor_code

def suggest_voice_handling_strategies():
    """建议语音处理策略"""
    print("\n💡 语音处理策略建议")
    print("=" * 60)
    
    strategies = [
        {
            "name": "策略1: 消息对象深度分析",
            "description": "在接收到语音消息时，详细分析消息对象的所有属性和方法",
            "implementation": [
                "记录消息对象的类型和所有属性",
                "尝试调用可能的下载/保存方法",
                "查找包含文件路径的属性"
            ]
        },
        {
            "name": "策略2: 微信缓存目录扫描",
            "description": "扫描微信的缓存目录，查找最新的语音文件",
            "implementation": [
                "获取微信安装目录",
                "扫描语音缓存文件夹",
                "根据时间戳匹配最新的语音文件"
            ]
        },
        {
            "name": "策略3: 屏幕截图+OCR",
            "description": "截取微信窗口，使用OCR识别语音转文字结果",
            "implementation": [
                "检测语音消息位置",
                "模拟点击转文字按钮",
                "截图并OCR识别转换结果"
            ]
        },
        {
            "name": "策略4: 用户引导优化",
            "description": "优化用户引导，提供更好的使用体验",
            "implementation": [
                "检测用户发送语音的频率",
                "提供个性化的引导消息",
                "记住用户偏好，减少重复引导"
            ]
        }
    ]
    
    for i, strategy in enumerate(strategies, 1):
        print(f"\n{strategy['name']}:")
        print(f"  描述: {strategy['description']}")
        print(f"  实现方案:")
        for impl in strategy['implementation']:
            print(f"    - {impl}")
    
    print(f"\n推荐优先级:")
    print(f"  1. 策略1 (消息对象深度分析) - 最直接有效")
    print(f"  2. 策略4 (用户引导优化) - 提升用户体验")
    print(f"  3. 策略2 (缓存目录扫描) - 技术复杂但可行")
    print(f"  4. 策略3 (屏幕截图+OCR) - 最后的备选方案")

def create_enhanced_voice_handler():
    """创建增强的语音处理器"""
    print("\n🔧 创建增强的语音处理器")
    print("=" * 60)
    
    handler_code = '''
class EnhancedVoiceHandler:
    """增强的语音消息处理器"""
    
    def __init__(self):
        self.voice_cache = {}  # 语音文件缓存
        self.user_preferences = {}  # 用户偏好设置
    
    def handle_voice_message(self, msg_obj, sender: str) -> Optional[str]:
        """
        处理语音消息的主入口
        
        Args:
            msg_obj: 微信消息对象
            sender: 发送者
            
        Returns:
            Optional[str]: 处理结果文字
        """
        try:
            # 1. 尝试提取真实语音文件
            voice_file = self.extract_voice_file(msg_obj)
            
            if voice_file:
                # 2. 使用百度语音识别
                text = self.recognize_with_baidu(voice_file)
                if text:
                    logger.info(f"语音识别成功: {text}")
                    return text
            
            # 3. 尝试其他识别方法
            text = self.try_alternative_recognition(msg_obj)
            if text:
                return text
            
            # 4. 发送智能引导
            return self.send_smart_guidance(sender)
            
        except Exception as e:
            logger.error(f"语音消息处理异常: {e}")
            return self.send_smart_guidance(sender)
    
    def extract_voice_file(self, msg_obj) -> Optional[str]:
        """提取语音文件路径"""
        # 实现语音文件提取逻辑
        pass
    
    def recognize_with_baidu(self, voice_file: str) -> Optional[str]:
        """使用百度语音识别"""
        # 实现百度识别逻辑
        pass
    
    def try_alternative_recognition(self, msg_obj) -> Optional[str]:
        """尝试其他识别方法"""
        # 实现备选识别方法
        pass
    
    def send_smart_guidance(self, sender: str) -> str:
        """发送智能引导消息"""
        # 根据用户历史和偏好发送个性化引导
        user_pref = self.user_preferences.get(sender, {})
        guidance_count = user_pref.get('guidance_count', 0)
        
        if guidance_count == 0:
            # 首次引导，详细说明
            return self.get_detailed_guidance()
        elif guidance_count < 3:
            # 简化引导
            return self.get_simple_guidance()
        else:
            # 用户多次发送语音，可能有特殊需求
            return self.get_understanding_guidance()
'''
    
    print("增强语音处理器代码:")
    print(handler_code)
    
    return handler_code

def main():
    """主函数"""
    print("🎤 微信语音消息对象调试工具")
    print()
    
    steps = [
        ("wxauto分析", analyze_wxauto_voice_message),
        ("语音文件提取器", create_voice_file_extractor),
        ("处理策略建议", suggest_voice_handling_strategies),
        ("增强处理器", create_enhanced_voice_handler)
    ]
    
    for step_name, step_func in steps:
        print(f"\n🔍 执行: {step_name}")
        try:
            step_func()
        except Exception as e:
            print(f"❌ {step_name} 失败: {e}")
    
    print(f"\n" + "=" * 60)
    print(f"          调试总结和建议")
    print(f"=" * 60)
    
    print("🎯 当前问题:")
    print("  语音消息内容是占位符文本 '[语音]X秒,未播放'")
    print("  需要获取真实的语音文件路径进行识别")
    
    print("\n💡 解决方案:")
    print("  1. 深度分析微信消息对象，查找语音文件路径")
    print("  2. 尝试调用消息对象的下载/保存方法")
    print("  3. 扫描微信缓存目录查找语音文件")
    print("  4. 优化用户引导，提供更好的体验")
    
    print("\n🚀 下一步行动:")
    print("  1. 重启机器人，查看详细的语音消息对象调试信息")
    print("  2. 发送语音消息，观察日志中的对象属性")
    print("  3. 根据调试信息调整语音文件提取逻辑")

if __name__ == "__main__":
    main()
