#!/usr/bin/env python3
"""
测试拟人化AI提示词效果
"""
import os
import sys
from pathlib import Path

# 添加项目路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def test_ai_system_prompt():
    """测试AI系统提示词"""
    print("🤖 测试拟人化AI提示词")
    print("=" * 60)
    
    try:
        from config import config
        
        print("当前AI系统提示词:")
        print("-" * 40)
        print(config.ai.system_prompt)
        print("-" * 40)
        
        # 检查提示词特点
        prompt = config.ai.system_prompt
        
        print("\n提示词分析:")
        
        # 检查拟人化特点
        humanized_keywords = ["朋友", "聊天", "自然", "随和", "热情", "友好"]
        found_humanized = [kw for kw in humanized_keywords if kw in prompt]
        if found_humanized:
            print(f"✅ 拟人化特点: {', '.join(found_humanized)}")
        else:
            print("❌ 缺少拟人化特点")
        
        # 检查是否避免AI身份暴露
        ai_keywords = ["AI", "机器人", "智能客服", "人工智能"]
        found_ai = [kw for kw in ai_keywords if kw in prompt]
        if not found_ai:
            print("✅ 避免AI身份暴露")
        else:
            print(f"❌ 仍有AI身份词汇: {', '.join(found_ai)}")
        
        # 检查是否避免"联系客服"
        service_keywords = ["联系客服", "人工客服", "客服", "人工服务"]
        found_service = [kw for kw in service_keywords if kw in prompt]
        if not found_service:
            print("✅ 避免提及客服")
        else:
            print(f"❌ 仍有客服相关词汇: {', '.join(found_service)}")
        
        # 检查语言风格
        casual_keywords = ["挺不错", "我觉得", "建议你", "这款", "看看"]
        found_casual = [kw for kw in casual_keywords if kw in prompt]
        if found_casual:
            print(f"✅ 口语化表达: {', '.join(found_casual)}")
        else:
            print("⚠️ 可以增加更多口语化表达")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_ai_reply_simulation():
    """模拟测试AI回复效果"""
    print("\n💬 模拟AI回复测试")
    print("=" * 60)
    
    try:
        from src.ai.llm_service import LLMService
        from config import config
        
        # 检查AI是否可用
        if not config.ai.api_key:
            print("⚠️ AI API密钥未配置，跳过实际回复测试")
            return True
        
        # 创建LLM服务
        llm_service = LLMService(
            api_key=config.ai.api_key,
            base_url=config.ai.base_url,
            model=config.ai.model,
            max_tokens=config.ai.max_tokens,
            temperature=config.ai.temperature,
            system_prompt=config.ai.system_prompt
        )
        
        # 测试问题
        test_questions = [
            "你好",
            "你是谁",
            "我想买个手机",
            "有什么推荐的吗",
            "这个产品怎么样",
            "价格多少",
            "你们是什么公司",
            "我有问题想咨询"
        ]
        
        print("测试AI回复效果:")
        print("-" * 40)
        
        for question in test_questions:
            print(f"\n用户: {question}")
            
            try:
                reply = llm_service.generate_reply(question)
                if reply:
                    print(f"AI: {reply}")
                    
                    # 分析回复质量
                    analysis = []
                    
                    # 检查是否暴露AI身份
                    ai_words = ["AI", "机器人", "智能客服", "人工智能", "程序"]
                    if any(word in reply for word in ai_words):
                        analysis.append("❌ 暴露AI身份")
                    else:
                        analysis.append("✅ 未暴露AI身份")
                    
                    # 检查是否提及客服
                    service_words = ["联系客服", "人工客服", "客服", "人工服务"]
                    if any(word in reply for word in service_words):
                        analysis.append("❌ 提及客服")
                    else:
                        analysis.append("✅ 未提及客服")
                    
                    # 检查语言风格
                    casual_words = ["挺", "觉得", "建议", "看看", "不错", "可以"]
                    if any(word in reply for word in casual_words):
                        analysis.append("✅ 语言自然")
                    else:
                        analysis.append("⚠️ 语言较正式")
                    
                    # 检查长度
                    if len(reply) <= 150:
                        analysis.append("✅ 长度适中")
                    else:
                        analysis.append("⚠️ 回复较长")
                    
                    print(f"分析: {' | '.join(analysis)}")
                else:
                    print("AI: (无回复)")
                    
            except Exception as e:
                print(f"AI: (回复失败: {e})")
        
        return True
        
    except Exception as e:
        print(f"❌ AI回复测试失败: {e}")
        return False

def test_default_reply():
    """测试默认回复设置"""
    print("\n🔇 测试默认回复设置")
    print("=" * 60)
    
    try:
        from config import config
        
        print(f"默认回复内容: '{config.reply.default_reply}'")
        
        if not config.reply.default_reply or config.reply.default_reply.strip() == "":
            print("✅ 默认回复为空，支持沉默模式")
        else:
            print(f"⚠️ 默认回复不为空: {config.reply.default_reply}")
            
            # 检查默认回复是否包含不当内容
            bad_words = ["联系客服", "人工客服", "AI", "机器人", "智能客服"]
            found_bad = [word for word in bad_words if word in config.reply.default_reply]
            
            if found_bad:
                print(f"❌ 默认回复包含不当词汇: {', '.join(found_bad)}")
            else:
                print("✅ 默认回复内容合适")
        
        return True
        
    except Exception as e:
        print(f"❌ 默认回复测试失败: {e}")
        return False

def test_reply_engine_integration():
    """测试回复引擎集成效果"""
    print("\n🔗 测试回复引擎集成")
    print("=" * 60)
    
    try:
        from src.bot.enhanced_reply_engine_with_images import EnhancedReplyEngineWithImages
        
        reply_engine = EnhancedReplyEngineWithImages()
        
        # 测试不同类型的查询
        test_cases = [
            {
                "query": "你好",
                "type": "通用问候",
                "expected": "应该有友好回复，不暴露AI身份"
            },
            {
                "query": "手机推荐",
                "type": "产品查询",
                "expected": "应该推荐具体产品"
            },
            {
                "query": "随便聊聊",
                "type": "闲聊",
                "expected": "应该保持沉默或简单回复"
            }
        ]
        
        for case in test_cases:
            print(f"\n测试: {case['query']} ({case['type']})")
            print(f"期望: {case['expected']}")
            
            reply, image_paths = reply_engine.generate_reply_with_images(case['query'])
            
            if reply:
                print(f"回复: {reply[:100]}...")
                print(f"图片: {len(image_paths)} 张")
                
                # 简单分析
                if "联系客服" in reply or "人工客服" in reply:
                    print("❌ 回复包含客服相关内容")
                elif "AI" in reply or "机器人" in reply:
                    print("❌ 回复暴露AI身份")
                else:
                    print("✅ 回复内容合适")
            else:
                print("回复: (保持沉默)")
                print("✅ 沉默处理")
        
        return True
        
    except Exception as e:
        print(f"❌ 回复引擎测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 拟人化AI提示词测试")
    print()
    
    # 运行测试
    tests = [
        ("AI提示词", test_ai_system_prompt),
        ("AI回复模拟", test_ai_reply_simulation),
        ("默认回复", test_default_reply),
        ("回复引擎集成", test_reply_engine_integration)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n🔍 运行测试: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
            if result:
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 总结
    print(f"\n" + "=" * 60)
    print(f"          拟人化AI测试总结")
    print(f"=" * 60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{status} {test_name}")
    
    print(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 拟人化AI配置成功！")
        print("\n💡 改进效果:")
        print("   1. ✅ AI提示词更加拟人化")
        print("   2. ✅ 避免暴露AI身份")
        print("   3. ✅ 不再提及'联系客服'")
        print("   4. ✅ 语言风格更自然随和")
        print("   5. ✅ 支持沉默模式")
        print("\n🚀 现在AI回复更像真人了！")
    else:
        print("⚠️ 部分配置需要调整")

if __name__ == "__main__":
    main()
