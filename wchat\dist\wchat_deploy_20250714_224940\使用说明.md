# WChat微信客服机器人 - 部署使用指南

## 🚀 快速开始

### 1. 安装依赖
```bash
# Windows
install.bat

# Linux/macOS  
chmod +x install.sh
./install.sh
```

### 2. 配置API密钥
编辑 `config/config.json` 文件，设置您的AI API密钥：
```json
{
  "ai": {
    "api_key": "您的硅基流动API密钥",
    "base_url": "https://api.siliconflow.cn/v1",
    "model": "Qwen/Qwen2.5-7B-Instruct"
  }
}
```

### 3. 启动机器人
```bash
# 方式1: 快速启动
python quick_start.py

# 方式2: 完整启动
python start_wchat.py

# 方式3: Windows批处理
双击 "快速启动.bat"
```

### 4. Web配置界面
启动后访问: http://localhost:5000
- 实时监控机器人状态
- 在线配置参数
- 管理FAQ和产品数据

## 📋 功能特点

### 🤖 AI智能聊天
- 支持自然语言对话
- 拟人化回复，不暴露AI身份
- 基于硅基流动API

### 📦 产品推荐
- 智能产品匹配
- 自动发送产品图片
- 支持Excel产品库管理

### 🎤 语音消息
- 语音转文字功能
- 支持语音产品查询
- 自动语音回复

### 💬 FAQ自动回复
- Excel格式FAQ管理
- 智能问题匹配
- 优先级回复逻辑

## ⚙️ 配置说明

### 基础配置
```json
{
  "wechat": {
    "auto_reply": true,           // 自动回复
    "reply_delay": 2,             // 回复延迟(秒)
    "listen_all": true,           // 监听所有消息
    "voice_to_text": true         // 语音转文字
  }
}
```

### AI配置
```json
{
  "ai": {
    "enabled": true,              // 启用AI
    "api_key": "your_api_key",    // API密钥
    "model": "Qwen/Qwen2.5-7B-Instruct"
  }
}
```

## 📁 目录结构

```
wchat/
├── src/                 # 源代码
│   ├── ai/             # AI服务
│   ├── bot/            # 机器人核心
│   ├── database/       # 数据处理
│   └── web/            # Web界面
├── config/             # 配置文件
├── data/               # 数据文件
│   ├── faq.xlsx        # FAQ数据
│   ├── products.xlsx   # 产品数据
│   └── images/         # 产品图片
├── quick_start.py      # 快速启动
└── requirements.txt    # 依赖列表
```

## 🔧 常见问题

### Q: 如何获取API密钥？
A: 访问 https://siliconflow.cn 注册并获取API密钥

### Q: 微信无法连接？
A: 确保微信PC版已登录，并允许程序访问

### Q: 语音转文字不工作？
A: 检查微信版本是否支持语音转文字API

### Q: 产品图片不显示？
A: 确保图片文件存在于 data/images/ 目录中

## 📞 技术支持

如有问题，请检查：
1. Python版本 >= 3.8
2. 依赖包是否正确安装
3. 配置文件格式是否正确
4. 微信PC版是否正常运行

## 🎉 开始使用

1. 完成上述配置后启动机器人
2. 向机器人发送"你好"测试基本功能
3. 发送"推荐手机"测试产品功能
4. 发送语音消息测试语音功能

祝您使用愉快！
