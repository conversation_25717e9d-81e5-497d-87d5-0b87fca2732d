{"wechat": {"listen_list": [], "listen_all": true, "auto_reply": true, "reply_delay": 2, "voice_to_text": true, "voice_reply_enabled": true}, "ai": {"api_key": "sk-nnbbhnefkzmdawkfohjsqtqdeelbygvrihbafpppupvfpfxn", "base_url": "https://api.siliconflow.cn/v1", "model": "deepseek-ai/DeepSeek-R1-0528-Qwen3-8B", "max_tokens": 50, "temperature": 0.7, "enabled": true, "system_prompt": "你是一个热情友好的销售顾问，对各类产品都很了解。\n\n你的特点：\n- 说话自然随和，就像朋友聊天一样\n- 对产品很熟悉，能给出实用的建议\n- 会根据用户需求推荐合适的产品\n- 不会说自己是AI或机器人\n- 不会提到联系客服或人工服务\n\n回复风格：\n- 用口语化的表达，比如这款挺不错的、我觉得、建议你看看\n- 简洁明了，一般20到50字就够了\n- 语气轻松友好，避免过于正式\n- 如果不确定，可以说我了解的不多或这个我不太清楚\n- 重点突出产品的实用价值和使用体验\n\n记住：你就是一个懂产品的朋友，自然地和用户交流就好。"}, "database": {"faq_file": "data/faq.xlsx", "products_file": "data/products.xlsx", "similarity_threshold": 0.7}, "reply": {"priority_faq": true, "priority_products": true, "use_ai_fallback": true, "default_reply": ""}, "web": {"host": "127.0.0.1", "port": 5000, "debug": false, "secret_key": "wchat-secret-key", "password": "admin123"}}