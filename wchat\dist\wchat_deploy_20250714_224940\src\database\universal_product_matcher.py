#!/usr/bin/env python3
"""
通用产品匹配器
适用于任何产品库的智能匹配算法
"""
import re
import jieba
from fuzzywuzzy import fuzz
from typing import List, Dict, Set, Tuple, Optional

class UniversalProductMatcher:
    """通用产品匹配器"""
    
    def __init__(self):
        # 产品层级关系定义
        self.product_hierarchy = {
            # 主产品类别 -> 相关配件类别
            "电脑": ["电脑配件", "计算机配件", "PC配件"],
            "手机": ["手机配件", "手机壳", "手机膜"],
            "相机": ["相机配件", "镜头", "摄影配件"],
            "汽车": ["汽车配件", "车载用品", "汽车用品"],
            "家电": ["家电配件", "电器配件"],
            "运动": ["运动配件", "健身配件"],
            "音响": ["音响配件", "音频配件"]
        }
        
        # 产品类型关键词
        self.product_keywords = {
            # 电子产品
            "电脑类": ["电脑", "计算机", "pc", "笔记本", "台式机", "一体机"],
            "手机类": ["手机", "智能手机", "phone", "移动电话"],
            "平板类": ["平板", "tablet", "ipad"],
            "耳机类": ["耳机", "耳麦", "headphone", "earphone"],
            "音响类": ["音响", "音箱", "speaker", "喇叭"],
            "相机类": ["相机", "摄像头", "camera", "摄像机"],
            "手表类": ["手表", "手环", "watch", "智能手表"],
            
            # 配件类
            "鼠标类": ["鼠标", "mouse"],
            "键盘类": ["键盘", "keyboard"],
            "充电器类": ["充电器", "充电线", "charger", "adapter"],
            "数据线类": ["数据线", "连接线", "cable"],
            "保护套类": ["保护套", "手机壳", "case", "套子"],
            
            # 家居用品
            "家具类": ["桌子", "椅子", "床", "沙发", "柜子"],
            "厨具类": ["锅", "碗", "盘子", "刀具", "厨具"],
            "清洁类": ["洗衣液", "清洁剂", "拖把", "扫帚"],
            
            # 服装类
            "上衣类": ["衬衫", "t恤", "外套", "毛衣", "夹克"],
            "下装类": ["裤子", "裙子", "短裤", "牛仔裤"],
            "鞋类": ["鞋子", "运动鞋", "皮鞋", "凉鞋", "靴子"],
            
            # 食品类
            "零食类": ["薯片", "饼干", "糖果", "巧克力"],
            "饮料类": ["饮料", "果汁", "茶", "咖啡", "水"],
            "生鲜类": ["水果", "蔬菜", "肉类", "海鲜"]
        }
        
        # 排除词（用户查询主产品时不应该匹配的配件）
        self.exclusion_rules = {
            "电脑": ["鼠标", "键盘", "音响", "耳机", "配件"],  # 用户问电脑时排除这些配件
            "手机": ["充电器", "数据线", "耳机", "手机壳", "手机膜", "保护套", "配件"],  # 用户问手机时排除这些配件
            "相机": ["三脚架", "存储卡", "镜头", "配件"],      # 用户问相机时排除这些配件
        }
    
    def calculate_product_score(self, query: str, product_data: Dict) -> float:
        """
        通用产品匹配分数计算
        
        Args:
            query: 用户查询
            product_data: 产品数据字典
            
        Returns:
            float: 匹配分数 (0-1)
        """
        query = query.lower().strip()
        scores = []
        
        # 1. 产品名称匹配
        product_name = str(product_data.get('产品名称', '')).lower()
        name_score = self._calculate_name_score(query, product_name)
        scores.append(name_score * 2.0)  # 名称权重最高
        
        # 2. 关键词匹配
        keywords = str(product_data.get('产品关键词', '')).lower()
        keyword_score = self._calculate_keyword_score(query, keywords)
        if keyword_score > 0:
            scores.append(keyword_score * 1.5)
        
        # 3. 描述匹配
        description = str(product_data.get('产品描述', '')).lower()
        desc_score = self._calculate_description_score(query, description)
        scores.append(desc_score * 0.8)
        
        # 4. 分类匹配（通用逻辑）
        category = str(product_data.get('分类', '')).lower()
        category_score = self._calculate_category_score(query, category, product_name)
        scores.append(category_score * 1.2)
        
        # 5. 排除规则检查
        exclusion_penalty = self._check_exclusion_rules(query, product_name, category)
        
        final_score = max(scores) if scores else 0
        return final_score * exclusion_penalty  # 应用排除惩罚
    
    def _calculate_name_score(self, query: str, product_name: str) -> float:
        """计算产品名称匹配分数"""
        if not product_name:
            return 0
        
        # 直接包含匹配
        if query in product_name:
            return 0.95
        
        # 分词匹配
        query_words = set(jieba.lcut(query))
        name_words = set(jieba.lcut(product_name))
        
        if query_words & name_words:  # 有交集
            intersection = len(query_words & name_words)
            union = len(query_words | name_words)
            return intersection / union * 0.9
        
        # 模糊匹配
        fuzzy_score = fuzz.ratio(query, product_name) / 100
        return fuzzy_score
    
    def _calculate_keyword_score(self, query: str, keywords: str) -> float:
        """计算关键词匹配分数"""
        if not keywords or keywords == 'nan':
            return 0
        
        keyword_list = [k.strip() for k in keywords.split(',') if k.strip()]
        if not keyword_list:
            return 0
        
        max_score = 0
        for keyword in keyword_list:
            if query in keyword or keyword in query:
                max_score = max(max_score, 1.0)
            else:
                fuzzy_score = fuzz.partial_ratio(keyword, query) / 100
                if fuzzy_score > 0.8:
                    max_score = max(max_score, fuzzy_score)
        
        return max_score
    
    def _calculate_description_score(self, query: str, description: str) -> float:
        """计算描述匹配分数"""
        if not description:
            return 0
        
        # 分词匹配
        query_words = set(jieba.lcut(query))
        desc_words = set(jieba.lcut(description))
        
        if query_words & desc_words:
            intersection = len(query_words & desc_words)
            return min(intersection / len(query_words), 0.8)  # 最高0.8分
        
        # 模糊匹配
        return fuzz.partial_ratio(query, description) / 100 * 0.6
    
    def _calculate_category_score(self, query: str, category: str, product_name: str) -> float:
        """通用分类匹配分数计算"""
        if not category:
            return 0
        
        # 1. 识别查询意图
        query_intent = self._identify_query_intent(query)
        
        # 2. 识别产品类型
        product_type = self._identify_product_type(product_name, category)
        
        # 3. 计算匹配度
        if query_intent and product_type:
            if query_intent == product_type:
                return 0.95  # 完全匹配
            elif self._is_related_category(query_intent, product_type):
                return 0.7   # 相关匹配
            elif self._is_accessory_mismatch(query_intent, product_type):
                return 0.1   # 配件误匹配，低分
        
        # 4. 直接分类匹配
        if query in category:
            return 0.8
        
        # 5. 模糊分类匹配
        return fuzz.partial_ratio(query, category) / 100 * 0.6
    
    def _identify_query_intent(self, query: str) -> Optional[str]:
        """识别用户查询意图"""
        for intent, keywords in self.product_keywords.items():
            for keyword in keywords:
                if keyword in query:
                    return intent
        return None
    
    def _identify_product_type(self, product_name: str, category: str) -> Optional[str]:
        """识别产品类型"""
        text = (product_name + " " + category).lower()
        
        for product_type, keywords in self.product_keywords.items():
            for keyword in keywords:
                if keyword in text:
                    return product_type
        return None
    
    def _is_related_category(self, query_intent: str, product_type: str) -> bool:
        """判断是否为相关类别"""
        # 定义相关类别映射
        related_mapping = {
            "电脑类": ["鼠标类", "键盘类"],
            "手机类": ["充电器类", "保护套类"],
            "相机类": ["数据线类"],
        }
        
        return product_type in related_mapping.get(query_intent, [])
    
    def _is_accessory_mismatch(self, query_intent: str, product_type: str) -> bool:
        """判断是否为配件误匹配"""
        # 用户查询主产品，但匹配到了不相关的配件
        main_categories = ["电脑类", "手机类", "相机类", "平板类"]
        accessory_categories = ["鼠标类", "键盘类", "充电器类", "保护套类"]
        
        return (query_intent in main_categories and 
                product_type in accessory_categories and 
                not self._is_related_category(query_intent, product_type))
    
    def _check_exclusion_rules(self, query: str, product_name: str, category: str) -> float:
        """检查排除规则"""
        product_text = (product_name + " " + category).lower()

        for main_product, excluded_items in self.exclusion_rules.items():
            if main_product in query:
                for excluded_item in excluded_items:
                    if excluded_item in product_text:
                        # 检查是否是强排除（产品名称直接包含排除词）
                        if excluded_item in product_name.lower():
                            return 0.05  # 强排除，几乎为0
                        else:
                            return 0.1   # 一般排除
        return 1.0  # 无惩罚
    
    def extract_product_features(self, product_data: Dict) -> Dict[str, any]:
        """提取产品特征（用于调试和分析）"""
        product_name = str(product_data.get('产品名称', ''))
        category = str(product_data.get('分类', ''))
        
        return {
            'product_type': self._identify_product_type(product_name, category),
            'name_words': list(jieba.lcut(product_name)),
            'category_words': list(jieba.lcut(category)),
            'is_accessory': any(acc in (product_name + category).lower() 
                              for acc_list in self.exclusion_rules.values() 
                              for acc in acc_list)
        }
