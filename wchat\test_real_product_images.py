#!/usr/bin/env python3
"""
真实的产品图片发送测试
模拟实际的微信机器人产品推荐场景
"""
import os
import sys
import time
from pathlib import Path

# 添加项目路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

class MockWeChatAPI:
    """模拟微信API"""
    
    def __init__(self):
        self.sent_messages = []
        self.sent_files = []
    
    def SendMsg(self, msg: str, who: str):
        """发送文本消息"""
        print(f"📤 发送消息到 {who}: {msg}")
        self.sent_messages.append((who, msg))
        time.sleep(0.5)  # 模拟发送延迟
    
    def SendFiles(self, filepath: str, who: str):
        """发送文件"""
        filename = os.path.basename(filepath)
        if os.path.exists(filepath):
            file_size = os.path.getsize(filepath)
            print(f"📷 发送图片到 {who}: {filename} ({file_size} bytes)")
            self.sent_files.append((who, filepath))
            time.sleep(1)  # 模拟图片发送延迟
            return True
        else:
            print(f"❌ 图片发送失败: {filename} 不存在")
            return False

def test_product_recommendation_with_images():
    """测试带图片的产品推荐"""
    print("=" * 60)
    print("          真实产品推荐测试")
    print("=" * 60)
    
    try:
        from src.database.enhanced_reader import EnhancedProductReader
        from src.bot.product_image_handler import ProductImageHandler
        from config import config
        
        # 创建组件
        product_reader = EnhancedProductReader(config.database.products_file)
        image_handler = ProductImageHandler()
        mock_wx = MockWeChatAPI()
        
        print(f"✅ 产品数据加载: {len(product_reader.data)} 条记录")
        
        # 模拟用户查询场景
        test_scenarios = [
            {
                "user": "张三",
                "chat_id": "user123",
                "message": "推荐一款手机",
                "description": "用户询问手机推荐"
            },
            {
                "user": "李四", 
                "chat_id": "user456",
                "message": "有什么好耳机",
                "description": "用户询问耳机推荐"
            },
            {
                "user": "王五",
                "chat_id": "user789", 
                "message": "笔记本电脑多少钱",
                "description": "用户询问笔记本价格"
            },
            {
                "user": "赵六",
                "chat_id": "group001",
                "message": "充电器",
                "description": "群聊中询问充电器"
            }
        ]
        
        for i, scenario in enumerate(test_scenarios, 1):
            print(f"\n" + "=" * 50)
            print(f"场景 {i}: {scenario['description']}")
            print(f"👤 {scenario['user']}: {scenario['message']}")
            
            # 搜索产品
            products = product_reader.search_products(scenario['message'])
            
            if products:
                # 限制推荐数量（避免刷屏）
                display_products = products[:1]  # 只推荐1个产品
                
                # 格式化回复
                reply, image_paths = image_handler.format_product_reply_with_images(display_products)
                
                # 发送文本回复
                mock_wx.SendMsg(reply, scenario['chat_id'])
                
                # 发送产品图片
                if image_paths:
                    print(f"📷 准备发送 {len(image_paths)} 张产品图片...")
                    for img_path in image_paths:
                        success = mock_wx.SendFiles(img_path, scenario['chat_id'])
                        if not success:
                            print(f"⚠️ 图片发送失败: {os.path.basename(img_path)}")
                else:
                    print(f"ℹ️ 该产品暂无图片")
            else:
                # 没找到产品
                fallback_reply = "抱歉，没有找到相关产品。您可以描述更具体的需求，我会为您推荐合适的产品。"
                mock_wx.SendMsg(fallback_reply, scenario['chat_id'])
            
            print(f"✅ 场景 {i} 处理完成")
        
        # 统计发送结果
        print(f"\n" + "=" * 50)
        print(f"发送统计:")
        print(f"📝 文本消息: {len(mock_wx.sent_messages)} 条")
        print(f"📷 图片文件: {len(mock_wx.sent_files)} 张")
        
        if mock_wx.sent_files:
            print(f"\n发送的图片:")
            for chat_id, filepath in mock_wx.sent_files:
                filename = os.path.basename(filepath)
                print(f"   {filename} -> {chat_id}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_batch_product_images():
    """测试批量产品图片处理"""
    print("=" * 60)
    print("          批量产品图片测试")
    print("=" * 60)
    
    try:
        from src.bot.product_image_handler import ProductImageHandler
        
        image_handler = ProductImageHandler()
        
        # 模拟多个产品的情况
        mock_products = [
            {"产品名称": "智能手机A1", "价格": "2999", "产品描述": "高性能智能手机"},
            {"产品名称": "无线蓝牙耳机B2", "价格": "399", "产品描述": "降噪蓝牙耳机"},
            {"产品名称": "笔记本电脑D4", "价格": "5999", "产品描述": "轻薄便携笔记本"}
        ]
        
        print(f"测试产品数量: {len(mock_products)}")
        
        # 获取所有产品的图片
        image_paths = image_handler.get_products_images(mock_products)
        print(f"找到图片数量: {len(image_paths)}")
        
        # 格式化回复
        reply, image_paths = image_handler.format_product_reply_with_images(mock_products)
        
        print(f"\n回复内容:")
        print(reply)
        
        print(f"\n图片列表:")
        for i, img_path in enumerate(image_paths, 1):
            filename = os.path.basename(img_path)
            exists = "✅" if os.path.exists(img_path) else "❌"
            print(f"   {i}. {filename} {exists}")
        
        # 模拟发送
        mock_wx = MockWeChatAPI()
        chat_id = "test_user"
        
        print(f"\n模拟发送过程:")
        mock_wx.SendMsg(reply, chat_id)
        
        for img_path in image_paths:
            mock_wx.SendFiles(img_path, chat_id)
        
        print(f"\n✅ 批量发送完成")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_image_validation():
    """测试图片验证功能"""
    print("=" * 60)
    print("          图片验证测试")
    print("=" * 60)
    
    try:
        from src.bot.product_image_handler import ProductImageHandler
        
        image_handler = ProductImageHandler()
        
        # 验证所有图片
        validation_result = image_handler.validate_image_files()
        
        print("图片验证结果:")
        all_valid = True
        for product_name, exists in validation_result.items():
            status = "✅ 存在" if exists else "❌ 缺失"
            print(f"   {product_name}: {status}")
            if not exists:
                all_valid = False
        
        # 检查缺失的图片
        missing_images = image_handler.get_missing_images()
        if missing_images:
            print(f"\n⚠️ 缺失的图片文件:")
            for img in missing_images:
                print(f"   - {img}")
        
        # 获取详细信息
        image_info = image_handler.get_image_info()
        total_size = sum(info['size'] for info in image_info.values() if info['exists'])
        
        print(f"\n📊 统计信息:")
        print(f"   总图片数: {len(image_info)}")
        print(f"   存在数量: {sum(1 for info in image_info.values() if info['exists'])}")
        print(f"   总大小: {total_size} bytes")
        
        if all_valid:
            print(f"\n🎉 所有产品图片都正常！")
        else:
            print(f"\n⚠️ 部分产品图片缺失，请检查图片文件。")
        
        return all_valid
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 开始真实产品图片功能测试")
    print()
    
    # 运行所有测试
    tests = [
        ("图片验证", test_image_validation),
        ("产品推荐", test_product_recommendation_with_images),
        ("批量处理", test_batch_product_images)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n🔍 运行测试: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
            if result:
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 总结
    print(f"\n" + "=" * 60)
    print(f"          测试总结")
    print(f"=" * 60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{status} {test_name}")
    
    print(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 产品图片功能完全正常！可以在实际微信机器人中使用。")
        print("\n💡 使用建议:")
        print("   1. 确保所有产品图片文件存在且可读")
        print("   2. 图片文件大小适中，避免发送过大的文件")
        print("   3. 在群聊中使用时注意发送频率，避免刷屏")
        print("   4. 可以根据需要调整同时推荐的产品数量")
    else:
        print("⚠️ 部分功能存在问题，请检查后再使用。")

if __name__ == "__main__":
    main()
