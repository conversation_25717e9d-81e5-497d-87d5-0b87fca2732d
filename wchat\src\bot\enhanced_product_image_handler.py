#!/usr/bin/env python3
"""
增强的产品图片处理器
支持更灵活的产品库替换和图片映射
"""
import os
import re
import json
from typing import List, Optional, Tuple, Dict
from pathlib import Path

class EnhancedProductImageHandler:
    """增强的产品图片处理器"""
    
    def __init__(self, images_dir: str = "data/images", config_file: str = "data/image_mapping.json"):
        self.images_dir = images_dir
        self.config_file = config_file
        
        # 默认的产品名称到图片文件的映射规则
        self.default_mapping = {
            "智能手机A1": "phone_a1.jpg",
            "无线蓝牙耳机B2": "earphone_b2.jpg", 
            "智能手表C3": "watch_c3.jpg",
            "笔记本电脑D4": "laptop_d4.jpg",
            "无线充电器E5": "charger_e5.jpg",
            "游戏鼠标F6": "mouse_f6.jpg"
        }
        
        # 产品分类到默认图片的映射
        self.category_mapping = {
            "数码电子": "default_electronics.jpg",
            "数码配件": "default_accessory.jpg",
            "智能穿戴": "default_wearable.jpg",
            "电脑办公": "default_computer.jpg",
            "电脑配件": "default_pc_accessory.jpg",
            "智能家居": "default_smart_home.jpg"
        }
        
        # 加载自定义映射配置
        self.custom_mapping = self.load_mapping_config()
    
    def load_mapping_config(self) -> Dict[str, str]:
        """加载自定义图片映射配置"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            print(f"⚠️ 加载图片映射配置失败: {e}")
        
        return {}
    
    def save_mapping_config(self, mapping: Dict[str, str]) -> bool:
        """保存图片映射配置"""
        try:
            os.makedirs(os.path.dirname(self.config_file), exist_ok=True)
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(mapping, f, ensure_ascii=False, indent=2)
            return True
        except Exception as e:
            print(f"❌ 保存图片映射配置失败: {e}")
            return False
    
    def extract_product_code(self, product_name: str) -> Optional[str]:
        """从产品名称中提取产品代码"""
        if not product_name:
            return None
        
        # 支持多种代码格式: A1, A-1, A_1, A 1
        patterns = [
            r'[A-Z]\d+',           # A1, B2
            r'[A-Z]-\d+',          # A-1, B-2
            r'[A-Z]_\d+',          # A_1, B_2
            r'[A-Z]\s+\d+',        # A 1, B 2
        ]
        
        for pattern in patterns:
            match = re.search(pattern, product_name.upper())
            if match:
                # 标准化代码格式为 A1, B2 等
                code = match.group().replace('-', '').replace('_', '').replace(' ', '')
                return code.lower()
        
        return None
    
    def get_product_image_path(self, product_name: str, product_category: str = "") -> Optional[str]:
        """
        根据产品名称获取对应的图片路径
        支持多种匹配策略
        """
        if not os.path.exists(self.images_dir):
            return None
        
        # 策略1: 检查自定义映射配置
        if product_name in self.custom_mapping:
            image_filename = self.custom_mapping[product_name]
            image_path = os.path.join(self.images_dir, image_filename)
            if os.path.exists(image_path):
                return image_path
        
        # 策略2: 检查默认映射
        if product_name in self.default_mapping:
            image_filename = self.default_mapping[product_name]
            image_path = os.path.join(self.images_dir, image_filename)
            if os.path.exists(image_path):
                return image_path
        
        # 策略3: 基于产品代码的模糊匹配
        product_code = self.extract_product_code(product_name)
        if product_code:
            for filename in os.listdir(self.images_dir):
                if filename.endswith(('.jpg', '.jpeg', '.png', '.gif')):
                    if product_code in filename.lower():
                        return os.path.join(self.images_dir, filename)
        
        # 策略4: 基于关键词匹配
        keywords = self.extract_keywords(product_name)
        for keyword in keywords:
            for filename in os.listdir(self.images_dir):
                if filename.endswith(('.jpg', '.jpeg', '.png', '.gif')):
                    if keyword.lower() in filename.lower():
                        return os.path.join(self.images_dir, filename)
        
        # 策略5: 基于产品分类的默认图片
        if product_category and product_category in self.category_mapping:
            default_image = self.category_mapping[product_category]
            image_path = os.path.join(self.images_dir, default_image)
            if os.path.exists(image_path):
                return image_path
        
        return None
    
    def extract_keywords(self, product_name: str) -> List[str]:
        """从产品名称中提取关键词"""
        if not product_name:
            return []
        
        # 产品类型关键词映射
        keyword_mapping = {
            "手机": ["phone", "mobile"],
            "耳机": ["earphone", "headphone", "earbuds"],
            "手表": ["watch", "smartwatch"],
            "笔记本": ["laptop", "notebook"],
            "电脑": ["computer", "pc"],
            "充电器": ["charger", "adapter"],
            "鼠标": ["mouse"],
            "键盘": ["keyboard"],
            "音箱": ["speaker"],
            "平板": ["tablet", "pad"]
        }
        
        keywords = []
        product_lower = product_name.lower()
        
        for chinese_keyword, english_keywords in keyword_mapping.items():
            if chinese_keyword in product_lower:
                keywords.extend(english_keywords)
        
        return keywords
    
    def auto_map_products(self, products: List[Dict]) -> Dict[str, str]:
        """自动为产品列表创建图片映射"""
        auto_mapping = {}
        
        for product in products:
            product_name = product.get('产品名称', '')
            product_category = product.get('分类', '')
            
            if product_name:
                image_path = self.get_product_image_path(product_name, product_category)
                if image_path:
                    auto_mapping[product_name] = os.path.basename(image_path)
        
        return auto_mapping
    
    def update_mapping_for_new_products(self, products: List[Dict]) -> bool:
        """为新产品更新图片映射配置"""
        try:
            # 生成自动映射
            auto_mapping = self.auto_map_products(products)
            
            # 合并到现有配置
            updated_mapping = {**self.custom_mapping, **auto_mapping}
            
            # 保存更新后的配置
            if self.save_mapping_config(updated_mapping):
                self.custom_mapping = updated_mapping
                print(f"✅ 已更新图片映射配置，新增 {len(auto_mapping)} 个产品映射")
                return True
            
        except Exception as e:
            print(f"❌ 更新图片映射失败: {e}")
        
        return False
    
    def get_products_images(self, products: List[Dict]) -> List[str]:
        """获取多个产品的图片路径列表"""
        image_paths = []
        
        for product in products:
            product_name = product.get('产品名称', '')
            product_category = product.get('分类', '')
            
            if product_name:
                image_path = self.get_product_image_path(product_name, product_category)
                if image_path:
                    image_paths.append(image_path)
        
        return image_paths
    
    def format_product_reply_with_images(self, products: List[Dict]) -> Tuple[str, List[str]]:
        """格式化产品回复，包含图片信息"""
        if not products:
            return "抱歉，没有找到相关产品。您可以描述更具体的需求，比如想要什么类型的产品、价格范围等，我会为您推荐合适的产品。", []
        
        # 根据产品数量调整开场白
        if len(products) == 1:
            reply = f"推荐你这款产品：\n\n"
        else:
            reply = f"为您推荐 {len(products)} 款相关产品：\n\n"
        
        image_paths = []
        
        for i, product in enumerate(products, 1):
            name = product.get('产品名称', '')
            desc = product.get('产品描述', '')
            price = product.get('价格', '')
            details = product.get('详细信息', '')
            category = product.get('分类', '')
            
            reply += f"🛍️ {i}. {name}\n"
            reply += f"💰 价格：¥{price}\n"
            reply += f"📝 描述：{desc}\n"
            
            if details:
                # 格式化详细信息
                formatted_details = details.replace('\n\n', '\n').strip()
                reply += f"ℹ️ 详情：{formatted_details}\n"
            
            # 查找对应的图片
            image_path = self.get_product_image_path(name, category)
            if image_path:
                image_paths.append(image_path)
            
            reply += "\n"
        
        # 根据产品数量调整结尾
        if len(products) == 1:
            # 单个产品不需要结尾询问，直接结束
            pass
        else:
            reply += "看看你喜欢哪款～"
        
        return reply, image_paths
    
    def get_mapping_status(self) -> Dict[str, any]:
        """获取图片映射状态"""
        status = {
            "images_dir": self.images_dir,
            "images_dir_exists": os.path.exists(self.images_dir),
            "config_file": self.config_file,
            "config_file_exists": os.path.exists(self.config_file),
            "default_mappings": len(self.default_mapping),
            "custom_mappings": len(self.custom_mapping),
            "category_mappings": len(self.category_mapping),
            "available_images": []
        }
        
        if os.path.exists(self.images_dir):
            for filename in os.listdir(self.images_dir):
                if filename.endswith(('.jpg', '.jpeg', '.png', '.gif')):
                    status["available_images"].append(filename)
        
        return status
