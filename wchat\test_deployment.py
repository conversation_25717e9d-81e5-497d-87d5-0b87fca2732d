#!/usr/bin/env python3
"""
模拟部署测试 - 验证部署包在新环境中的可用性
"""
import os
import sys
import tempfile
import zipfile
import subprocess
from pathlib import Path

def test_deployment():
    """测试部署过程"""
    print("🧪 WChat部署测试")
    print("=" * 60)
    
    # 查找部署包
    dist_dir = Path(__file__).parent / "dist"
    zip_files = list(dist_dir.glob("wchat_deploy_*.zip"))
    
    if not zip_files:
        print("❌ 未找到部署包")
        return False
    
    latest_zip = max(zip_files, key=lambda x: x.stat().st_mtime)
    print(f"📦 测试包: {latest_zip.name}")
    
    # 创建临时测试目录
    with tempfile.TemporaryDirectory() as temp_dir:
        test_dir = Path(temp_dir) / "wchat_test"
        test_dir.mkdir()
        
        print(f"📁 测试目录: {test_dir}")
        
        # 解压部署包
        print(f"📦 解压部署包...")
        try:
            with zipfile.ZipFile(latest_zip, 'r') as zipf:
                zipf.extractall(test_dir)
            print(f"  ✅ 解压成功")
        except Exception as e:
            print(f"  ❌ 解压失败: {e}")
            return False
        
        # 查找解压后的目录
        extracted_dirs = [d for d in test_dir.iterdir() if d.is_dir()]
        if not extracted_dirs:
            print(f"❌ 未找到解压目录")
            return False
        
        wchat_dir = extracted_dirs[0]
        print(f"📂 WChat目录: {wchat_dir.name}")
        
        # 测试文件完整性
        print(f"\n🔍 检查文件完整性...")
        required_files = [
            "quick_start.py",
            "requirements.txt",
            "config/config.json",
            "data/faq.xlsx",
            "data/products.xlsx"
        ]
        
        for req_file in required_files:
            file_path = wchat_dir / req_file
            if file_path.exists():
                print(f"  ✅ {req_file}")
            else:
                print(f"  ❌ {req_file}")
                return False
        
        # 测试Python导入
        print(f"\n🐍 测试Python模块导入...")
        
        # 切换到wchat目录
        original_cwd = os.getcwd()
        os.chdir(wchat_dir)
        
        try:
            # 添加到Python路径
            sys.path.insert(0, str(wchat_dir))
            
            # 测试核心模块导入
            test_imports = [
                "config",
                "src.ai.llm_service",
                "src.bot.enhanced_reply_engine_with_images",
                "src.database.faq_manager",
                "src.database.product_manager"
            ]
            
            for module in test_imports:
                try:
                    __import__(module)
                    print(f"  ✅ {module}")
                except ImportError as e:
                    print(f"  ⚠️ {module} - {e}")
                except Exception as e:
                    print(f"  ❌ {module} - {e}")
            
        finally:
            os.chdir(original_cwd)
            sys.path.remove(str(wchat_dir))
        
        # 测试配置文件
        print(f"\n⚙️ 测试配置文件...")
        config_file = wchat_dir / "config" / "config.json"
        
        try:
            import json
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            # 检查配置结构
            required_sections = ['wechat', 'ai', 'database', 'reply', 'web']
            for section in required_sections:
                if section in config:
                    print(f"  ✅ 配置节: {section}")
                else:
                    print(f"  ❌ 缺少配置节: {section}")
            
            # 检查API密钥是否为空
            api_key = config.get('ai', {}).get('api_key', '')
            if not api_key:
                print(f"  ✅ API密钥已清理 (需要用户配置)")
            else:
                print(f"  ⚠️ API密钥未清理")
                
        except Exception as e:
            print(f"  ❌ 配置文件测试失败: {e}")
            return False
        
        # 测试数据文件
        print(f"\n📊 测试数据文件...")
        
        # 测试FAQ文件
        faq_file = wchat_dir / "data" / "faq.xlsx"
        try:
            import pandas as pd
            df = pd.read_excel(faq_file)
            print(f"  ✅ FAQ文件: {len(df)} 条记录")
        except Exception as e:
            print(f"  ❌ FAQ文件测试失败: {e}")
        
        # 测试产品文件
        products_file = wchat_dir / "data" / "products.xlsx"
        try:
            import pandas as pd
            df = pd.read_excel(products_file)
            print(f"  ✅ 产品文件: {len(df)} 条记录")
        except Exception as e:
            print(f"  ❌ 产品文件测试失败: {e}")
        
        # 测试图片文件
        images_dir = wchat_dir / "data" / "images"
        if images_dir.exists():
            image_files = list(images_dir.glob("*.jpg")) + list(images_dir.glob("*.png"))
            print(f"  ✅ 产品图片: {len(image_files)} 张")
        else:
            print(f"  ⚠️ 图片目录不存在")
        
        # 测试启动脚本语法
        print(f"\n🚀 测试启动脚本...")
        
        start_scripts = [
            "quick_start.py",
            "install_deps.py"
        ]
        
        for script in start_scripts:
            script_path = wchat_dir / script
            if script_path.exists():
                try:
                    # 检查Python语法
                    with open(script_path, 'r', encoding='utf-8') as f:
                        code = f.read()
                    compile(code, script_path, 'exec')
                    print(f"  ✅ {script} (语法正确)")
                except SyntaxError as e:
                    print(f"  ❌ {script} (语法错误: {e})")
                except Exception as e:
                    print(f"  ⚠️ {script} (检查失败: {e})")
            else:
                print(f"  ❌ {script} (文件不存在)")
        
        print(f"\n✅ 部署测试完成")
        return True

def create_deployment_checklist():
    """创建部署检查清单"""
    print(f"\n📋 创建部署检查清单...")
    
    checklist = """# WChat机器人部署检查清单

## 📦 部署前准备
- [ ] 确认目标电脑Python版本 >= 3.8
- [ ] 准备硅基流动API密钥
- [ ] 确保微信PC版已安装
- [ ] 检查网络连接稳定性

## 🚀 部署步骤
- [ ] 复制部署包到目标电脑
- [ ] 解压到合适目录 (如 C:\\wchat\\)
- [ ] 运行 install.bat (Windows) 或 install.sh (Linux)
- [ ] 等待依赖安装完成
- [ ] 编辑 config/config.json 配置API密钥

## ⚙️ 配置检查
- [ ] API密钥已正确填入
- [ ] 微信自动回复设置为 true
- [ ] 语音转文字功能已启用
- [ ] Web界面端口未被占用 (默认5000)

## 🧪 功能测试
- [ ] 运行 python quick_start.py 启动
- [ ] 访问 http://localhost:5000 Web界面
- [ ] 发送"你好"测试AI回复
- [ ] 发送"推荐手机"测试产品功能
- [ ] 发送语音消息测试语音功能
- [ ] 检查产品图片是否正常发送

## 🔧 故障排除
- [ ] 检查Python和依赖是否正确安装
- [ ] 验证API密钥有效性
- [ ] 确认微信PC版正常运行
- [ ] 查看日志文件排查错误
- [ ] 检查防火墙和网络设置

## 📊 性能监控
- [ ] 观察内存使用情况
- [ ] 监控回复响应时间
- [ ] 检查日志文件大小
- [ ] 验证语音转文字准确性

## 🎉 部署完成
- [ ] 所有功能测试通过
- [ ] Web界面可正常访问
- [ ] 机器人回复正常
- [ ] 用户可以正常使用

**完成以上检查后，您的WChat机器人即可正式投入使用！**
"""
    
    checklist_file = Path(__file__).parent / "dist" / "部署检查清单.md"
    with open(checklist_file, 'w', encoding='utf-8') as f:
        f.write(checklist)
    
    print(f"  ✅ 部署检查清单.md")

def main():
    """主函数"""
    try:
        success = test_deployment()
        
        if success:
            create_deployment_checklist()
            
            print(f"\n" + "=" * 60)
            print(f"          部署测试完成！")
            print(f"=" * 60)
            print(f"✅ 部署包完整性验证通过")
            print(f"✅ 模块导入测试通过")
            print(f"✅ 配置文件格式正确")
            print(f"✅ 数据文件可正常读取")
            print(f"✅ 启动脚本语法正确")
            print(f"\n🎉 部署包已准备就绪，可以在其他电脑上使用！")
            print(f"\n📋 部署文件位置:")
            
            dist_dir = Path(__file__).parent / "dist"
            zip_files = list(dist_dir.glob("wchat_deploy_*.zip"))
            if zip_files:
                latest_zip = max(zip_files, key=lambda x: x.stat().st_mtime)
                print(f"   📦 {latest_zip.absolute()}")
                print(f"   📁 大小: {latest_zip.stat().st_size / (1024*1024):.1f} MB")
            
        else:
            print(f"\n❌ 部署测试失败，请检查打包过程")
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
