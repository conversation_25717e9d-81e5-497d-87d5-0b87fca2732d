#!/usr/bin/env python3
"""
测试简化配置页面
"""
import requests

def test_simple_config():
    """测试简化配置页面"""
    try:
        session = requests.Session()
        
        # 登录
        login_data = {'password': 'admin123'}
        login_response = session.post('http://localhost:5000/login', data=login_data)
        print(f"登录状态: {login_response.status_code}")
        
        if login_response.status_code == 200:
            # 访问简化配置页面
            config_response = session.get('http://localhost:5000/config_test')
            print(f"简化配置页面状态: {config_response.status_code}")
            
            if config_response.status_code == 200:
                print("✅ 简化配置页面访问成功")
                if "监听模式" in config_response.text:
                    print("✅ 监听模式功能正常")
                if "调试信息" in config_response.text:
                    print("✅ 调试信息显示正常")
                    # 提取调试信息
                    start = config_response.text.find("<pre>")
                    end = config_response.text.find("</pre>")
                    if start != -1 and end != -1:
                        debug_info = config_response.text[start+5:end]
                        print("调试信息:")
                        print(debug_info)
            else:
                print(f"❌ 简化配置页面返回状态码: {config_response.status_code}")
                print("响应内容:")
                print(config_response.text[:500])
        else:
            print("❌ 登录失败")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")

if __name__ == "__main__":
    test_simple_config()
