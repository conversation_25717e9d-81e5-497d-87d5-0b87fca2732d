@echo off
cd /d "%~dp0"

echo ========================================
echo WeChat Customer Service Bot Launcher
echo ========================================
echo Current Directory: %CD%
echo.

:menu
echo Please select an option:
echo [1] Install Dependencies
echo [2] Start Web Config
echo [3] Start Bot
echo [4] Test Basic Functions
echo [5] Open Demo Page
echo [0] Exit
echo.
set /p choice=Enter your choice (0-5): 

if "%choice%"=="1" goto install
if "%choice%"=="2" goto webconfig
if "%choice%"=="3" goto runbot
if "%choice%"=="4" goto test
if "%choice%"=="5" goto demo
if "%choice%"=="0" goto exit
echo Invalid choice, please try again.
goto menu

:install
echo.
echo Installing dependencies...
python install_deps.py
echo.
echo Installation completed!
pause
goto menu

:webconfig
echo.
echo Starting Web Config Interface...
echo Access URL: http://127.0.0.1:5000
echo Default Password: admin123
echo.
python web_config.py
pause
goto menu

:runbot
echo.
echo Starting WeChat Bot...
echo Make sure WeChat PC is logged in!
echo.
python run.py
pause
goto menu

:test
echo.
echo Running basic tests...
python test_basic.py
echo.
echo Test completed!
pause
goto menu

:demo
echo.
echo Opening demo page...
start "" "%CD%\demo.html"
echo Demo page opened in browser.
pause
goto menu

:exit
echo.
echo Thank you for using WeChat Customer Service Bot!
pause
exit
