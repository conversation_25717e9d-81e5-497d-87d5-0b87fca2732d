#!/usr/bin/env python3
"""
测试AI状态修复
"""
import sys
import os
import requests
from pathlib import Path

# 添加项目根目录到Python路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def test_enhanced_reply_engine_stats():
    """测试增强回复引擎统计信息"""
    print("🔍 测试增强回复引擎统计信息...")
    print("=" * 60)
    
    try:
        from src.bot.enhanced_reply_engine import EnhancedReplyEngine
        
        engine = EnhancedReplyEngine()
        stats = engine.get_statistics()
        
        print("✅ 增强回复引擎统计信息:")
        print(f"   FAQ数量: {stats.get('faq_count', 0)}")
        print(f"   产品数量: {stats.get('product_count', 0)}")
        print(f"   产品分类: {stats.get('product_categories', 0)}")
        print(f"   FAQ分类: {stats.get('faq_categories', [])}")
        print(f"   AI可用: {stats.get('ai_available', False)}")
        print(f"   状态: {stats.get('status', 'unknown')}")
        
        if stats.get('ai_available'):
            print("✅ AI服务状态：可用")
        else:
            print("❌ AI服务状态：不可用")
            
        return stats.get('ai_available', False)
        
    except Exception as e:
        print(f"❌ 测试增强回复引擎失败: {e}")
        return False

def test_llm_service_directly():
    """直接测试LLM服务"""
    print("\n🔍 直接测试LLM服务...")
    print("=" * 60)
    
    try:
        from src.ai.llm_service import LLMService
        
        llm_service = LLMService()
        is_available = llm_service.is_available()
        
        print(f"LLM服务可用性: {is_available}")
        
        if is_available:
            print("✅ LLM服务：可用")
            
            # 测试连接
            try:
                test_result = llm_service.test_connection()
                print(f"连接测试结果: {test_result}")
            except Exception as e:
                print(f"连接测试失败: {e}")
        else:
            print("❌ LLM服务：不可用")
            
        return is_available
        
    except Exception as e:
        print(f"❌ 测试LLM服务失败: {e}")
        return False

def test_web_api_stats():
    """测试Web API统计信息"""
    print("\n🌐 测试Web API统计信息...")
    print("=" * 60)
    
    try:
        session = requests.Session()
        
        # 登录
        login_data = {'password': 'admin123'}
        login_response = session.post('http://localhost:5000/login', data=login_data)
        print(f"登录状态: {login_response.status_code}")
        
        if login_response.status_code == 200:
            # 获取统计信息
            stats_response = session.get('http://localhost:5000/api/stats')
            print(f"统计信息API状态: {stats_response.status_code}")
            
            if stats_response.status_code == 200:
                stats = stats_response.json()
                print("✅ Web API统计信息:")
                print(f"   FAQ数量: {stats.get('faq_count', 0)}")
                print(f"   产品数量: {stats.get('product_count', 0)}")
                print(f"   AI可用: {stats.get('ai_available', False)}")
                print(f"   增强模式: {stats.get('enhanced_mode', False)}")
                print(f"   状态: {stats.get('status', 'unknown')}")
                
                if stats.get('ai_available'):
                    print("✅ Web API显示AI状态：可用")
                else:
                    print("❌ Web API显示AI状态：不可用")
                    
                return stats.get('ai_available', False)
            else:
                print(f"❌ 获取统计信息失败: {stats_response.status_code}")
                if stats_response.status_code != 200:
                    print(f"   响应内容: {stats_response.text}")
                return False
        else:
            print("❌ 登录失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试Web API失败: {e}")
        return False

def test_dashboard_display():
    """测试仪表板显示"""
    print("\n🖥️ 测试仪表板显示...")
    print("=" * 60)
    
    try:
        session = requests.Session()
        
        # 登录
        login_data = {'password': 'admin123'}
        login_response = session.post('http://localhost:5000/login', data=login_data)
        
        if login_response.status_code == 200:
            # 访问仪表板
            dashboard_response = session.get('http://localhost:5000/dashboard')
            print(f"仪表板页面状态: {dashboard_response.status_code}")
            
            if dashboard_response.status_code == 200:
                content = dashboard_response.text
                
                # 检查AI状态显示
                if "AI状态" in content:
                    print("✅ 找到AI状态显示区域")
                    
                    if "可用" in content and "text-success" in content:
                        print("✅ 仪表板显示AI状态：可用")
                        return True
                    elif "不可用" in content and "text-danger" in content:
                        print("❌ 仪表板显示AI状态：不可用")
                        return False
                    else:
                        print("❓ AI状态显示不明确")
                        return False
                else:
                    print("❌ 未找到AI状态显示区域")
                    return False
            else:
                print(f"❌ 仪表板页面访问失败: {dashboard_response.status_code}")
                return False
        else:
            print("❌ 登录失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试仪表板显示失败: {e}")
        return False

def main():
    """主函数"""
    print("🔧 AI状态显示修复测试")
    print("=" * 60)
    
    # 测试各个组件
    engine_ai_ok = test_enhanced_reply_engine_stats()
    llm_ai_ok = test_llm_service_directly()
    api_ai_ok = test_web_api_stats()
    dashboard_ai_ok = test_dashboard_display()
    
    print("\n" + "=" * 60)
    print("📊 测试结果总结:")
    print(f"   增强回复引擎AI状态: {'✅ 可用' if engine_ai_ok else '❌ 不可用'}")
    print(f"   LLM服务状态: {'✅ 可用' if llm_ai_ok else '❌ 不可用'}")
    print(f"   Web API AI状态: {'✅ 可用' if api_ai_ok else '❌ 不可用'}")
    print(f"   仪表板显示: {'✅ 可用' if dashboard_ai_ok else '❌ 不可用'}")
    
    if all([engine_ai_ok, llm_ai_ok, api_ai_ok, dashboard_ai_ok]):
        print("\n🎉 所有测试通过！AI状态显示正常。")
    elif any([engine_ai_ok, llm_ai_ok]):
        if not api_ai_ok or not dashboard_ai_ok:
            print("\n⚠️ AI服务可用，但Web界面显示有问题。")
            print("💡 建议：重启Web服务器或刷新浏览器页面。")
        else:
            print("\n✅ AI状态显示已修复！")
    else:
        print("\n❌ AI服务不可用，请检查配置。")
        print("💡 建议：运行 python wchat/test_ai_api.py 检查AI配置。")

if __name__ == "__main__":
    main()
