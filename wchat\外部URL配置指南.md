# 外部URL配置指南

## 🌐 什么是外部URL

外部URL是指从外网访问您的私域自动化系统时使用的完整地址。配置正确的外部URL可以：

- 🌍 让管理员从任何地方远程生成许可证
- 📱 支持移动设备访问
- 🔗 提供统一的访问入口
- 📊 便于分享和管理

## 🔧 配置方式

### 方式一：使用配置工具（推荐）

```bash
cd wchat
python 网络配置工具.py
# 选择 "2. 配置远程访问"
# 在外部URL处输入您的域名或IP
```

### 方式二：手动编辑配置文件

编辑 `wchat/config/config.json` 文件：

```json
{
  "web": {
    "external_url": "http://your-domain.com:5000"
  }
}
```

## 📋 外部URL格式示例

### 1. 使用公网IP
```
http://123.456.789.100:5000
https://123.456.789.100:5000
```

### 2. 使用域名
```
http://yourdomain.com:5000
https://yourdomain.com:5000
```

### 3. 使用动态域名
```
http://yourhost.no-ip.org:5000
http://yourhost.ddns.net:5000
```

### 4. 使用子域名
```
http://automation.yourdomain.com:5000
https://wchat.yourcompany.com:5000
```

### 5. 使用非标准端口
```
http://yourdomain.com:8080
http://yourdomain.com:8888
```

## 🌍 获取外部访问地址的方法

### 方法一：获取公网IP

```bash
# 使用网络配置工具查看
python 网络配置工具.py
# 选择 "1. 查看当前配置"

# 或者手动查询
# 访问 https://www.whatismyip.com/
# 或者 https://ipinfo.io/
```

### 方法二：申请域名

#### 免费域名服务
- **Freenom**: 提供免费.tk, .ml, .ga域名
- **No-IP**: 免费动态域名服务
- **DuckDNS**: 免费动态DNS服务

#### 付费域名服务
- **阿里云**: 域名注册和解析
- **腾讯云**: 域名服务
- **Cloudflare**: 域名和CDN服务

### 方法三：使用动态域名

```bash
# 配置动态域名
python 动态域名配置工具.py

# 支持的服务商：
# - No-IP (no-ip.com)
# - DynDNS (dyn.com)
# - 花生壳 (oray.com)
```

## 🔧 网络配置要求

### 1. 路由器端口映射

登录路由器管理界面（通常是***********），配置端口映射：

```
服务名称: 私域自动化系统
外部端口: 5000
内部端口: 5000
内部IP: ************* (运行系统的电脑IP)
协议: TCP
状态: 启用
```

### 2. 防火墙配置

#### Windows防火墙
```bash
1. Win + R → wf.msc
2. 入站规则 → 新建规则
3. 端口 → TCP → 5000
4. 允许连接
```

#### Linux防火墙
```bash
# Ubuntu
sudo ufw allow 5000/tcp

# CentOS
sudo firewall-cmd --permanent --add-port=5000/tcp
sudo firewall-cmd --reload
```

### 3. 监听地址设置

确保系统监听所有网络接口：

```json
{
  "web": {
    "host": "0.0.0.0"
  }
}
```

## 🌐 实际配置示例

### 示例1：家庭网络 + 公网IP

```json
{
  "web": {
    "host": "0.0.0.0",
    "port": 5000,
    "external_url": "http://123.456.789.100:5000"
  }
}
```

**访问方式**：
- 本地：`http://127.0.0.1:5000`
- 局域网：`http://*************:5000`
- 外网：`http://123.456.789.100:5000`

### 示例2：企业网络 + 域名

```json
{
  "web": {
    "host": "0.0.0.0",
    "port": 5000,
    "external_url": "https://automation.company.com:5000",
    "enable_https": true
  }
}
```

**访问方式**：
- 内网：`http://**************:5000`
- 外网：`https://automation.company.com:5000`

### 示例3：动态IP + 动态域名

```json
{
  "web": {
    "host": "0.0.0.0",
    "port": 5000,
    "external_url": "http://myhost.no-ip.org:5000"
  }
}
```

**配置步骤**：
1. 注册No-IP账户
2. 创建动态域名
3. 配置动态域名工具
4. 设置外部URL

## 🔍 测试外部访问

### 1. 内网测试

```bash
# 测试本机访问
curl http://127.0.0.1:5000

# 测试局域网访问
curl http://*************:5000
```

### 2. 外网测试

```bash
# 使用手机热点或其他网络测试
curl http://your-external-url:5000

# 或在浏览器中访问
http://your-external-url:5000
```

### 3. 使用网络工具测试

```bash
python 网络配置工具.py
# 选择 "4. 测试网络连接"
```

## 🚨 常见问题

### 1. 无法从外网访问

**检查清单**：
- ✓ 路由器是否配置端口映射
- ✓ 防火墙是否开放端口
- ✓ 监听地址是否为0.0.0.0
- ✓ 公网IP是否正确

### 2. 域名无法解析

**解决方案**：
- 检查域名DNS设置
- 确认域名指向正确的IP
- 等待DNS传播（最多24小时）

### 3. 动态IP变化

**解决方案**：
- 使用动态域名服务
- 配置自动更新脚本
- 使用路由器DDNS功能

## 💡 最佳实践

### 1. 安全建议

- ✅ 使用HTTPS加密传输
- ✅ 设置强密码
- ✅ 配置IP白名单
- ✅ 定期更新系统

### 2. 性能优化

- ✅ 使用CDN加速（可选）
- ✅ 配置缓存策略
- ✅ 监控网络延迟
- ✅ 优化带宽使用

### 3. 管理建议

- ✅ 记录配置信息
- ✅ 定期备份配置
- ✅ 监控访问日志
- ✅ 制定应急预案

---

**配置完成后，您就可以从世界任何地方访问和管理您的私域自动化系统了！** 🌍🚀
