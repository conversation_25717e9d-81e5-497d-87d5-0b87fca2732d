#!/usr/bin/env python3
"""
WChat 快速启动程序 - 简化版
"""
import os
import sys
import subprocess
import webbrowser
import time
import threading
from pathlib import Path

def start_bot():
    """启动微信机器人"""
    current_dir = Path(__file__).parent

    try:
        # 检查run.py文件
        bot_file = current_dir / "run.py"
        if not bot_file.exists():
            print("警告: 未找到机器人启动文件")
            return None

        # 设置环境变量
        env = os.environ.copy()
        env['PYTHONPATH'] = str(current_dir)

        # 启动机器人
        process = subprocess.Popen(
            [sys.executable, "run.py"],
            cwd=current_dir,
            env=env
        )

        print("微信机器人启动中...")
        return process

    except Exception as e:
        print(f"启动机器人失败: {e}")
        return None

def main():
    """主函数"""
    print("=" * 60)
    print("          WChat 智能客服系统 - 快速启动")
    print("=" * 60)

    # 设置当前目录
    current_dir = Path(__file__).parent

    # 添加到Python路径
    if str(current_dir) not in sys.path:
        sys.path.insert(0, str(current_dir))

    print("正在启动Web服务器...")

    try:
        # 切换到Web目录
        web_dir = current_dir / "src" / "web"
        if not web_dir.exists():
            print("错误: 未找到Web应用目录")
            input("按回车键退出...")
            return

        original_dir = os.getcwd()
        os.chdir(web_dir)

        # 设置环境变量
        env = os.environ.copy()
        env['PYTHONPATH'] = str(current_dir)

        # 启动Flask应用
        web_process = subprocess.Popen(
            [sys.executable, "app.py"],
            env=env
        )

        print("Web服务器启动中...")
        time.sleep(2)

        # 启动微信机器人
        print("正在启动微信机器人...")
        bot_process = start_bot()
        time.sleep(1)
        
        # 在后台线程中打开浏览器
        def open_browser():
            time.sleep(2)
            try:
                webbrowser.open("http://localhost:5000")
                print("浏览器已打开: http://localhost:5000")
            except:
                print("请手动访问: http://localhost:5000")
        
        browser_thread = threading.Thread(target=open_browser)
        browser_thread.daemon = True
        browser_thread.start()
        
        print("\n" + "=" * 60)
        print("启动成功!")
        print("Web界面: http://localhost:5000")
        print("默认账号: admin")
        print("默认密码: admin123")
        if bot_process:
            print("微信机器人: 已启动")
        else:
            print("微信机器人: 启动失败 (请检查微信PC版是否已登录)")
        print("=" * 60)
        print("按 Ctrl+C 停止系统")
        print("=" * 60)

        # 等待进程
        try:
            processes = [web_process]
            if bot_process:
                processes.append(bot_process)

            # 等待任一进程结束
            while processes:
                for process in processes[:]:
                    if process.poll() is not None:
                        processes.remove(process)
                time.sleep(1)

        except KeyboardInterrupt:
            print("\n正在停止系统...")

            # 停止Web服务器
            if web_process.poll() is None:
                web_process.terminate()
                try:
                    web_process.wait(timeout=5)
                except subprocess.TimeoutExpired:
                    web_process.kill()

            # 停止机器人
            if bot_process and bot_process.poll() is None:
                bot_process.terminate()
                try:
                    bot_process.wait(timeout=5)
                except subprocess.TimeoutExpired:
                    bot_process.kill()

            print("系统已停止")
        
    except Exception as e:
        print(f"启动失败: {e}")
        input("按回车键退出...")
    finally:
        print("感谢使用 WChat 智能客服系统!")

if __name__ == "__main__":
    main()
