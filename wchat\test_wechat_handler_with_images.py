#!/usr/bin/env python3
"""
测试修改后的微信处理器（支持产品图片）
"""
import os
import sys
from pathlib import Path

# 添加项目路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

class MockWeChat:
    """模拟微信API"""
    
    def __init__(self):
        self.sent_messages = []
        self.sent_files = []
        self.current_chat = None
    
    def ChatWith(self, chat_name: str) -> bool:
        """切换到指定聊天"""
        self.current_chat = chat_name
        print(f"📱 切换到聊天: {chat_name}")
        return True
    
    def SendMsg(self, msg: str):
        """发送文本消息"""
        print(f"📤 发送消息: {msg}")
        self.sent_messages.append((self.current_chat, msg))
    
    def SendFiles(self, filepath: str):
        """发送文件"""
        filename = os.path.basename(filepath)
        if os.path.exists(filepath):
            file_size = os.path.getsize(filepath)
            print(f"📷 发送图片: {filename} ({file_size} bytes)")
            self.sent_files.append((self.current_chat, filepath))
        else:
            print(f"❌ 图片发送失败: {filename} 不存在")

def test_wechat_handler_with_images():
    """测试支持图片的微信处理器"""
    print("🔧 测试修改后的微信处理器")
    print("=" * 60)
    
    try:
        from src.bot.wechat_handler import WeChatHandler
        
        # 创建处理器
        handler = WeChatHandler()
        
        # 使用模拟的微信API
        handler.wx = MockWeChat()
        
        # 测试产品查询判断
        print("1. 测试产品查询判断:")
        test_messages = [
            "推荐一款手机",      # 应该是产品查询
            "有什么好耳机",      # 应该是产品查询
            "什么时候发货",      # 不是产品查询
            "你好",             # 不是产品查询
            "笔记本电脑多少钱"   # 应该是产品查询
        ]
        
        for msg in test_messages:
            is_product = handler._is_product_query(msg)
            status = "✅" if is_product else "❌"
            print(f"   {status} '{msg}' → {'产品查询' if is_product else '非产品查询'}")
        
        # 测试回复生成
        print(f"\n2. 测试回复生成:")
        test_scenarios = [
            "推荐一款手机",
            "有什么好的耳机",
            "什么时候发货"
        ]
        
        for scenario in test_scenarios:
            print(f"\n   测试: '{scenario}'")
            reply, image_paths = handler._generate_reply(scenario, "测试用户", "测试用户")
            
            if reply:
                print(f"   回复: {reply[:100]}...")
                print(f"   图片: {len(image_paths)} 张")
                for img_path in image_paths:
                    filename = os.path.basename(img_path)
                    exists = "✅" if os.path.exists(img_path) else "❌"
                    print(f"     - {filename} {exists}")
            else:
                print(f"   回复: (无回复)")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_complete_message_flow():
    """测试完整的消息处理流程"""
    print("\n🔄 测试完整消息处理流程")
    print("=" * 60)
    
    try:
        from src.bot.wechat_handler import WeChatHandler
        
        # 创建处理器
        handler = WeChatHandler()
        
        # 使用模拟的微信API
        mock_wx = MockWeChat()
        handler.wx = mock_wx
        
        # 模拟消息对象
        class MockMessage:
            def __init__(self, sender, content, msg_type='text'):
                self.sender = sender
                self.content = content
                self.type = msg_type
        
        # 测试消息
        test_messages = [
            MockMessage("张三", "推荐一款手机"),
            MockMessage("李四", "有什么好的耳机"),
            MockMessage("王五", "什么时候发货"),
            MockMessage("赵六", "充电器多少钱")
        ]
        
        print("模拟消息处理:")
        for i, msg in enumerate(test_messages, 1):
            print(f"\n场景 {i}: {msg.sender} 发送 '{msg.content}'")
            
            # 模拟消息处理（简化版）
            try:
                # 生成回复
                reply, image_paths = handler._generate_reply(msg.content, msg.sender, msg.sender)
                
                if reply:
                    # 发送文本回复
                    handler._send_reply(reply, msg.sender)
                    
                    # 发送产品图片
                    if image_paths:
                        handler._send_product_images(image_paths, msg.sender)
                    
                    print(f"✅ 处理完成: 文本回复 + {len(image_paths)} 张图片")
                else:
                    print(f"⭕ 无回复")
                    
            except Exception as e:
                print(f"❌ 处理失败: {e}")
        
        # 统计结果
        print(f"\n发送统计:")
        print(f"📝 文本消息: {len(mock_wx.sent_messages)} 条")
        print(f"📷 图片文件: {len(mock_wx.sent_files)} 张")
        
        if mock_wx.sent_files:
            print(f"\n发送的图片:")
            for chat, filepath in mock_wx.sent_files:
                filename = os.path.basename(filepath)
                print(f"   {filename} → {chat}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_error_handling():
    """测试错误处理"""
    print("\n⚠️ 测试错误处理")
    print("=" * 60)
    
    try:
        from src.bot.wechat_handler import WeChatHandler
        
        handler = WeChatHandler()
        handler.wx = MockWeChat()
        
        # 测试不存在的图片
        print("1. 测试不存在的图片:")
        fake_image_paths = ["fake_image1.jpg", "fake_image2.jpg"]
        handler._send_product_images(fake_image_paths, "测试用户")
        
        # 测试空消息
        print(f"\n2. 测试空消息:")
        reply, image_paths = handler._generate_reply("", "测试用户", "测试用户")
        print(f"   空消息回复: {reply}")
        print(f"   图片数量: {len(image_paths)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 错误处理测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 微信处理器图片功能测试")
    print()
    
    # 运行所有测试
    tests = [
        ("基础功能", test_wechat_handler_with_images),
        ("完整流程", test_complete_message_flow),
        ("错误处理", test_error_handling)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n🔍 运行测试: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
            if result:
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 总结
    print(f"\n" + "=" * 60)
    print(f"          测试总结")
    print(f"=" * 60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{status} {test_name}")
    
    print(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 微信处理器图片功能修改成功！")
        print("\n💡 现在您的微信机器人支持:")
        print("   1. 自动识别产品查询")
        print("   2. 发送产品推荐文本")
        print("   3. 自动发送对应的产品图片")
        print("   4. 图片发送间隔控制")
        print("   5. 完整的错误处理")
        print("\n🚀 重启微信机器人即可看到产品图片功能！")
    else:
        print("⚠️ 部分功能存在问题，请检查后再使用。")

if __name__ == "__main__":
    main()
