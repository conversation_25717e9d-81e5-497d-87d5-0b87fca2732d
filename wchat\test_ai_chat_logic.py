#!/usr/bin/env python3
"""
测试AI聊天逻辑
"""
import os
import sys
from pathlib import Path

# 添加项目路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def test_ai_availability():
    """测试AI可用性"""
    print("🤖 测试AI可用性")
    print("=" * 60)
    
    try:
        from config import config
        
        print("AI配置检查:")
        print(f"  API密钥: {'已配置' if config.ai.api_key else '未配置'}")
        print(f"  基础URL: {config.ai.base_url}")
        print(f"  模型: {config.ai.model}")
        print(f"  AI启用: {config.ai.enabled}")
        print(f"  AI回退: {config.reply.use_ai_fallback}")
        
        # 检查AI是否可用
        ai_available = (config.ai.api_key and 
                       config.ai.enabled and 
                       config.reply.use_ai_fallback)
        
        if ai_available:
            print("✅ AI配置完整，可以处理聊天")
        else:
            print("❌ AI配置不完整，无法处理聊天")
            
            if not config.ai.api_key:
                print("  - 缺少API密钥")
            if not config.ai.enabled:
                print("  - AI未启用")
            if not config.reply.use_ai_fallback:
                print("  - AI回退未启用")
        
        return ai_available
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

def create_mock_ai_service():
    """创建模拟AI服务"""
    print("\n🎭 创建模拟AI服务")
    print("=" * 60)
    
    class MockLLMService:
        """模拟LLM服务"""
        
        def __init__(self, **kwargs):
            self.system_prompt = kwargs.get('system_prompt', '')
            print(f"✅ 模拟AI服务已创建")
            print(f"系统提示词: {self.system_prompt[:50]}...")
        
        def generate_reply(self, message: str, sender: str = "") -> str:
            """生成模拟回复"""
            message = message.lower().strip()
            
            # 基本问候
            if message in ['你好', '您好', 'hi', 'hello']:
                return "你好！有什么可以帮你的吗？"
            
            # 时间问候
            elif message in ['早上好', '上午好']:
                return "早上好！今天想看看什么产品？"
            elif message in ['下午好']:
                return "下午好！有什么需要了解的吗？"
            elif message in ['晚上好']:
                return "晚上好！有什么可以为你介绍的？"
            
            # 询问身份
            elif '你是谁' in message or '你是什么' in message:
                return "我是这里的销售顾问，对各类产品都比较了解，有什么想看的产品吗？"
            
            # 感谢
            elif '谢谢' in message or '感谢' in message:
                return "不客气！还有什么其他需要了解的吗？"
            
            # 再见
            elif '再见' in message or '拜拜' in message or 'bye' in message:
                return "再见！有需要随时联系我～"
            
            # 闲聊
            elif '聊聊' in message or '聊天' in message:
                return "好啊，你想聊什么？我对产品比较熟悉，可以给你介绍介绍～"
            
            # 询问推荐
            elif '推荐' in message and ('什么' in message or '有什么' in message):
                return "我们有很多不错的产品，你比较关注哪个方面？比如手机、电脑、耳机这些？"
            
            # 默认友好回复
            else:
                return "嗯嗯，我了解了。有什么具体想了解的产品吗？"
    
    return MockLLMService

def test_chat_scenarios():
    """测试聊天场景"""
    print("\n💬 测试聊天场景")
    print("=" * 60)
    
    try:
        # 创建模拟AI服务
        MockLLMService = create_mock_ai_service()
        
        # 测试场景
        chat_scenarios = [
            "你好",
            "您好", 
            "hi",
            "早上好",
            "你是谁",
            "谢谢",
            "再见",
            "随便聊聊",
            "有什么推荐的吗",
            "今天天气不错",
            "我想买个东西"
        ]
        
        # 创建模拟服务实例
        mock_ai = MockLLMService(system_prompt="你是一个热情友好的销售顾问")
        
        print("聊天测试结果:")
        print("-" * 40)
        
        for scenario in chat_scenarios:
            reply = mock_ai.generate_reply(scenario)
            print(f"\n用户: {scenario}")
            print(f"AI: {reply}")
            
            # 分析回复质量
            analysis = []
            
            # 检查是否有回复
            if reply and reply.strip():
                analysis.append("✅ 有回复")
            else:
                analysis.append("❌ 无回复")
            
            # 检查是否自然
            natural_words = ["你好", "嗯嗯", "好啊", "不客气", "随时"]
            if any(word in reply for word in natural_words):
                analysis.append("✅ 语言自然")
            
            # 检查是否避免AI身份
            ai_words = ["AI", "机器人", "智能客服"]
            if not any(word in reply for word in ai_words):
                analysis.append("✅ 未暴露AI身份")
            
            # 检查是否避免客服
            service_words = ["联系客服", "人工客服"]
            if not any(word in reply for word in service_words):
                analysis.append("✅ 未提及客服")
            
            print(f"分析: {' | '.join(analysis)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 聊天测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_reply_engine_with_mock_ai():
    """测试集成模拟AI的回复引擎"""
    print("\n🔗 测试集成模拟AI的回复引擎")
    print("=" * 60)
    
    try:
        # 临时修改AI配置以启用模拟AI
        from config import config
        
        # 保存原始配置
        original_api_key = config.ai.api_key
        original_enabled = config.ai.enabled
        original_use_ai = config.reply.use_ai_fallback
        
        # 设置为启用AI（即使没有真实API密钥）
        config.ai.api_key = "mock_key"  # 临时设置
        config.ai.enabled = True
        config.reply.use_ai_fallback = True
        
        print("✅ 临时启用AI配置")
        
        # 测试不同类型的消息
        test_cases = [
            {
                "message": "你好",
                "type": "基本问候",
                "expected": "应该有友好回复"
            },
            {
                "message": "手机推荐",
                "type": "产品查询", 
                "expected": "应该推荐产品"
            },
            {
                "message": "你是谁",
                "type": "身份询问",
                "expected": "应该有AI回复"
            },
            {
                "message": "随便聊聊",
                "type": "闲聊",
                "expected": "应该有AI回复"
            }
        ]
        
        from src.bot.enhanced_reply_engine_with_images import EnhancedReplyEngineWithImages
        reply_engine = EnhancedReplyEngineWithImages()
        
        print("\n测试结果:")
        print("-" * 40)
        
        for case in test_cases:
            print(f"\n测试: {case['message']} ({case['type']})")
            print(f"期望: {case['expected']}")
            
            try:
                reply, image_paths = reply_engine.generate_reply_with_images(case['message'])
                
                if reply and reply.strip():
                    print(f"回复: {reply[:100]}...")
                    print(f"图片: {len(image_paths)} 张")
                    
                    if case['type'] == "产品查询":
                        if "推荐" in reply and len(image_paths) > 0:
                            print("✅ 产品推荐正常")
                        else:
                            print("⚠️ 产品推荐可能有问题")
                    else:
                        print("✅ AI回复正常")
                else:
                    print("回复: (无回复)")
                    if case['type'] in ["基本问候", "身份询问", "闲聊"]:
                        print("❌ 应该有AI回复但没有")
                    else:
                        print("✅ 沉默处理")
                        
            except Exception as e:
                print(f"回复: (处理失败: {e})")
        
        # 恢复原始配置
        config.ai.api_key = original_api_key
        config.ai.enabled = original_enabled
        config.reply.use_ai_fallback = original_use_ai
        
        print(f"\n✅ 已恢复原始AI配置")
        
        return True
        
    except Exception as e:
        print(f"❌ 集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 AI聊天逻辑测试")
    print()
    
    # 运行测试
    tests = [
        ("AI可用性", test_ai_availability),
        ("聊天场景", test_chat_scenarios),
        ("回复引擎集成", test_reply_engine_with_mock_ai)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n🔍 运行测试: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
            if result:
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 总结
    print(f"\n" + "=" * 60)
    print(f"          AI聊天逻辑测试总结")
    print(f"=" * 60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{status} {test_name}")
    
    print(f"\n总计: {passed}/{total} 个测试通过")
    
    print("\n💡 建议:")
    print("1. ✅ 配置AI API密钥以启用真实AI回复")
    print("2. ✅ 确保AI配置项都已启用")
    print("3. ✅ 基本聊天应该由AI处理，不是FAQ")
    print("4. ✅ FAQ只处理具体业务问题")
    print("5. ✅ 产品查询优先匹配产品库")
    
    print(f"\n🎯 正确的回复逻辑:")
    print(f"   FAQ匹配 → 产品匹配 → AI聊天 → 保持沉默")

if __name__ == "__main__":
    main()
