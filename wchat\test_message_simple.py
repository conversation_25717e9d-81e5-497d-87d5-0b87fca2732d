"""
简单的消息测试脚本
直接测试wxauto消息对象的属性
"""
import sys
import time

def test_wxauto_messages():
    """测试wxauto消息"""
    try:
        from wxauto import WeChat
        
        wx = WeChat()
        print(f"微信连接成功，用户: {wx.nickname}")
        
        # 发送测试消息
        if wx.<PERSON>t<PERSON>ith("文件传输助手"):
            test_msg = "测试消息内容解析"
            wx.SendMsg(test_msg)
            print(f"已发送测试消息: {test_msg}")
            
            # 等待并获取消息
            time.sleep(3)
            msgs = wx.GetNewMessage()
            
            if msgs:
                print(f"\n获取到 {len(msgs)} 条消息:")
                for i, msg in enumerate(msgs):
                    print(f"\n--- 消息 {i+1} ---")
                    print(f"类型: {type(msg)}")
                    print(f"类名: {type(msg).__name__}")
                    print(f"模块: {type(msg).__module__}")
                    
                    # 列出所有属性
                    attrs = [attr for attr in dir(msg) if not attr.startswith('_')]
                    print(f"属性: {attrs}")
                    
                    # 检查常见属性的值
                    for attr in ['sender', 'content', 'msg', 'text', 'who', 'from_user']:
                        if hasattr(msg, attr):
                            try:
                                value = getattr(msg, attr)
                                print(f"{attr}: {value} (类型: {type(value)})")
                            except Exception as e:
                                print(f"{attr}: 获取失败 - {e}")
                    
                    # 尝试直接打印消息对象
                    try:
                        print(f"str(msg): {str(msg)}")
                    except:
                        print("str(msg): 无法转换为字符串")
                        
                    # 尝试repr
                    try:
                        print(f"repr(msg): {repr(msg)}")
                    except:
                        print("repr(msg): 无法获取repr")
            else:
                print("未获取到新消息")
        else:
            print("无法切换到文件传输助手")
            
    except ImportError:
        print("wxauto模块未安装")
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    test_wxauto_messages()
