#!/usr/bin/env python3
"""
重启微信PC端工具
"""
import os
import time
import subprocess
import psutil

def kill_wechat_processes():
    """强制关闭所有微信进程"""
    print("正在关闭微信进程...")
    
    wechat_processes = []
    for proc in psutil.process_iter(['pid', 'name']):
        try:
            if 'wechat' in proc.info['name'].lower() or 'weixin' in proc.info['name'].lower():
                wechat_processes.append(proc)
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            continue
    
    if wechat_processes:
        print(f"发现 {len(wechat_processes)} 个微信进程:")
        for proc in wechat_processes:
            try:
                print(f"  - {proc.info['name']} (PID: {proc.info['pid']})")
                proc.terminate()
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
        
        # 等待进程结束
        time.sleep(3)
        
        # 强制杀死仍在运行的进程
        for proc in wechat_processes:
            try:
                if proc.is_running():
                    print(f"强制结束进程: {proc.info['name']}")
                    proc.kill()
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
        
        print("✅ 微信进程已关闭")
    else:
        print("未发现运行中的微信进程")

def start_wechat():
    """启动微信PC端"""
    print("正在启动微信PC端...")
    
    # 常见的微信安装路径
    wechat_paths = [
        r"C:\Program Files\Tencent\WeChat\WeChat.exe",
        r"C:\Program Files (x86)\Tencent\WeChat\WeChat.exe",
        r"D:\Program Files\Tencent\WeChat\WeChat.exe",
        r"D:\Program Files (x86)\Tencent\WeChat\WeChat.exe",
        r"E:\Program Files\Tencent\WeChat\WeChat.exe",
        r"E:\Program Files (x86)\Tencent\WeChat\WeChat.exe"
    ]
    
    wechat_exe = None
    for path in wechat_paths:
        if os.path.exists(path):
            wechat_exe = path
            break
    
    if wechat_exe:
        try:
            subprocess.Popen([wechat_exe])
            print(f"✅ 微信已启动: {wechat_exe}")
            return True
        except Exception as e:
            print(f"❌ 启动微信失败: {e}")
            return False
    else:
        print("❌ 未找到微信安装路径")
        print("请手动启动微信PC端")
        return False

def wait_for_wechat_login():
    """等待微信登录"""
    print("请在微信PC端完成登录...")
    print("登录完成后按回车键继续...")
    input()

def main():
    """主函数"""
    print("=" * 60)
    print("          微信PC端重启工具")
    print("=" * 60)
    
    print("1. 关闭微信进程")
    kill_wechat_processes()
    
    print("\n2. 启动微信PC端")
    if start_wechat():
        print("\n3. 等待登录")
        wait_for_wechat_login()
        
        print("\n4. 验证微信状态")
        try:
            import wxauto
            wx = wxauto.WeChat()
            if wx:
                print("✅ 微信连接成功")
                print(f"当前用户: {wx.CurrentChat()}")
            else:
                print("❌ 微信连接失败")
        except Exception as e:
            print(f"❌ 微信验证失败: {e}")
    
    print("\n" + "=" * 60)
    print("微信重启完成！")
    print("=" * 60)
    
    print("\n接下来可以:")
    print("1. 重新启动WChat机器人")
    print("2. 运行: python wchat\\quick_start.py")

if __name__ == "__main__":
    main()
