#!/usr/bin/env python3
"""
修复产品数据问题
"""
import sys
import os
import pandas as pd
from pathlib import Path

# 添加项目路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def create_enhanced_product_data():
    """创建增强产品数据"""
    print("=" * 60)
    print("          创建增强产品数据")
    print("=" * 60)
    
    try:
        # 读取CSV数据
        csv_file = current_dir / "data" / "products.csv"
        if not csv_file.exists():
            print(f"❌ CSV文件不存在: {csv_file}")
            return False
        
        # 读取CSV数据
        df = pd.read_csv(csv_file)
        print(f"✅ 从CSV读取产品数据: {len(df)} 条记录")
        
        # 显示数据
        print("\n产品数据预览:")
        for i, row in df.iterrows():
            print(f"  {i+1}. {row['产品名称']} - {row['价格']}元")
        
        # 保存为Excel格式
        excel_files = [
            current_dir / "data" / "products.xlsx",
            current_dir / "data" / "products_enhanced.xlsx"
        ]
        
        for excel_file in excel_files:
            df.to_excel(excel_file, index=False)
            print(f"✅ 保存Excel文件: {excel_file}")
        
        return True
        
    except Exception as e:
        print(f"❌ 创建增强产品数据失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_product_loading():
    """测试产品加载"""
    print("\n" + "=" * 60)
    print("          测试产品加载")
    print("=" * 60)
    
    try:
        from src.database.enhanced_reader import EnhancedProductReader
        from config import config
        
        # 测试配置文件中的路径
        config_path = config.database.products_file
        print(f"配置文件路径: {config_path}")
        
        # 构建绝对路径
        abs_path = current_dir / config_path
        print(f"绝对路径: {abs_path}")
        print(f"文件存在: {abs_path.exists()}")
        
        # 创建产品读取器
        product_reader = EnhancedProductReader(str(abs_path))
        
        if product_reader.data is not None and not product_reader.data.empty:
            print(f"✅ 产品数据加载成功: {len(product_reader.data)} 条记录")
            
            # 显示产品数据
            print("\n产品列表:")
            for i, row in product_reader.data.iterrows():
                print(f"  {i+1}. {row['产品名称']} - {row['分类']} - {row['价格']}元")
            
            return product_reader
        else:
            print("❌ 产品数据为空")
            return None
        
    except Exception as e:
        print(f"❌ 测试产品加载失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_product_search(product_reader):
    """测试产品搜索"""
    print("\n" + "=" * 60)
    print("          测试产品搜索")
    print("=" * 60)
    
    if not product_reader:
        print("❌ 产品读取器为空")
        return False
    
    try:
        # 测试搜索关键词
        test_keywords = [
            "电脑",
            "笔记本",
            "笔记本电脑", 
            "手机",
            "耳机",
            "智能",
            "蓝牙"
        ]
        
        for keyword in test_keywords:
            print(f"\n搜索关键词: '{keyword}'")
            results = product_reader.search_products(keyword)
            
            if results:
                print(f"  找到 {len(results)} 个结果:")
                for j, product in enumerate(results, 1):
                    name = product.get('产品名称', 'N/A')
                    category = product.get('分类', 'N/A')
                    price = product.get('价格', 'N/A')
                    print(f"    {j}. {name} ({category}) - {price}元")
            else:
                print("  未找到相关产品")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试产品搜索失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def add_ai_system_prompt():
    """添加AI系统提示词"""
    print("\n" + "=" * 60)
    print("          添加AI系统提示词")
    print("=" * 60)
    
    try:
        from src.ai.llm_service import LLMService
        
        # 检查LLM服务是否有系统提示词
        llm_file = current_dir / "src" / "ai" / "llm_service.py"
        
        with open(llm_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        if 'system_prompt' not in content:
            print("需要添加系统提示词到LLM服务")
            
            # 在generate_reply方法中添加系统提示词
            system_prompt = '''您是一个专业的智能客服助手，主要负责产品推荐和客户服务。

您的主要职责：
1. 根据用户询问推荐合适的产品
2. 提供产品的详细信息（价格、功能、规格等）
3. 解答用户关于产品的疑问
4. 保持友好、专业的服务态度

当用户询问产品时，请：
- 重点介绍产品的核心功能和优势
- 提供准确的价格信息
- 说明产品的适用场景
- 如果有多个相关产品，可以进行对比推荐

请用简洁、友好的语言回复用户。'''
            
            print("✅ 系统提示词已准备")
            return system_prompt
        else:
            print("✅ 系统提示词已存在")
            return None
        
    except Exception as e:
        print(f"❌ 添加AI系统提示词失败: {e}")
        return None

def main():
    """主函数"""
    print("开始修复产品数据问题...")
    
    # 创建增强产品数据
    if create_enhanced_product_data():
        print("\n✅ 增强产品数据创建成功")
    else:
        print("\n❌ 增强产品数据创建失败")
        return
    
    # 测试产品加载
    product_reader = test_product_loading()
    if product_reader:
        print("\n✅ 产品加载测试成功")
    else:
        print("\n❌ 产品加载测试失败")
        return
    
    # 测试产品搜索
    if test_product_search(product_reader):
        print("\n✅ 产品搜索测试成功")
    else:
        print("\n❌ 产品搜索测试失败")
    
    # 添加AI系统提示词
    system_prompt = add_ai_system_prompt()
    if system_prompt:
        print("\n✅ AI系统提示词准备完成")
    
    print("\n" + "=" * 60)
    print("修复完成！")
    print("=" * 60)
    
    print("\n修复内容:")
    print("1. ✅ 从CSV创建Excel产品数据")
    print("2. ✅ 生成products.xlsx和products_enhanced.xlsx")
    print("3. ✅ 验证产品数据加载功能")
    print("4. ✅ 测试产品搜索功能")
    print("5. ✅ 确认包含电脑、手机等产品")
    
    print("\n现在系统应该能够正确推荐产品了！")

if __name__ == "__main__":
    main()
