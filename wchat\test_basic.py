"""
基础功能测试脚本
测试项目的核心组件是否正常工作
"""
import os
import sys

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def test_imports():
    """测试模块导入"""
    print("=" * 50)
    print("测试模块导入...")
    
    try:
        # 测试配置模块
        from config import config
        print("✅ 配置模块导入成功")
        print(f"   FAQ文件: {config.database.faq_file}")
        print(f"   产品文件: {config.database.products_file}")
    except Exception as e:
        print(f"❌ 配置模块导入失败: {e}")
    
    try:
        # 测试数据库模块
        from src.database.excel_reader import FAQReader, ProductReader
        print("✅ Excel读取器导入成功")
    except Exception as e:
        print(f"❌ Excel读取器导入失败: {e}")
    
    try:
        # 测试CSV读取器
        from src.database.csv_reader import FAQCSVReader, ProductCSVReader
        print("✅ CSV读取器导入成功")
    except Exception as e:
        print(f"❌ CSV读取器导入失败: {e}")
    
    try:
        # 测试匹配器
        from src.database.matcher import ContentMatcher
        print("✅ 内容匹配器导入成功")
    except Exception as e:
        print(f"❌ 内容匹配器导入失败: {e}")
    
    try:
        # 测试AI服务
        from src.ai.llm_service import LLMService
        print("✅ AI服务模块导入成功")
    except Exception as e:
        print(f"❌ AI服务模块导入失败: {e}")
    
    try:
        # 测试回复引擎
        from src.bot.reply_engine import ReplyEngine
        print("✅ 回复引擎导入成功")
    except Exception as e:
        print(f"❌ 回复引擎导入失败: {e}")


def test_data_files():
    """测试数据文件"""
    print("\n" + "=" * 50)
    print("测试数据文件...")
    
    from config import config
    
    # 检查FAQ文件
    faq_path = os.path.join(current_dir, config.database.faq_file)
    if os.path.exists(faq_path):
        print(f"✅ FAQ文件存在: {faq_path}")
        
        # 测试读取
        try:
            from src.database.excel_reader import FAQReader
            reader = FAQReader(faq_path)
            if reader.load_data():
                data = reader.get_data()
                print(f"   FAQ记录数: {len(data)}")
                categories = reader.get_categories()
                print(f"   FAQ分类: {categories}")
            else:
                print("   ❌ FAQ文件读取失败")
        except Exception as e:
            print(f"   ❌ FAQ文件测试失败: {e}")
    else:
        print(f"❌ FAQ文件不存在: {faq_path}")
    
    # 检查产品文件
    products_path = os.path.join(current_dir, config.database.products_file)
    if os.path.exists(products_path):
        print(f"✅ 产品文件存在: {products_path}")
        
        # 测试读取
        try:
            from src.database.excel_reader import ProductReader
            reader = ProductReader(products_path)
            if reader.load_data():
                data = reader.get_data()
                print(f"   产品记录数: {len(data)}")
                categories = reader.get_categories()
                print(f"   产品分类: {categories}")
            else:
                print("   ❌ 产品文件读取失败")
        except Exception as e:
            print(f"   ❌ 产品文件测试失败: {e}")
    else:
        print(f"❌ 产品文件不存在: {products_path}")


def test_reply_engine():
    """测试回复引擎"""
    print("\n" + "=" * 50)
    print("测试回复引擎...")
    
    try:
        from src.bot.reply_engine import ReplyEngine
        
        # 创建回复引擎实例
        engine = ReplyEngine()
        print("✅ 回复引擎创建成功")
        
        # 获取统计信息
        stats = engine.get_statistics()
        print(f"   FAQ数量: {stats.get('faq_count', 0)}")
        print(f"   产品数量: {stats.get('product_count', 0)}")
        print(f"   AI可用: {stats.get('ai_available', False)}")
        
        # 测试FAQ回复
        test_questions = [
            "如何退货",
            "什么时候发货",
            "有什么优惠",
            "产品质量怎么样"
        ]
        
        print("\n测试FAQ回复:")
        for question in test_questions:
            try:
                reply = engine.generate_reply(question)
                print(f"   问题: {question}")
                print(f"   回复: {reply[:50]}...")
            except Exception as e:
                print(f"   问题: {question} - 回复失败: {e}")
        
    except Exception as e:
        print(f"❌ 回复引擎测试失败: {e}")


def test_content_matcher():
    """测试内容匹配器"""
    print("\n" + "=" * 50)
    print("测试内容匹配器...")
    
    try:
        from src.database.matcher import ContentMatcher
        
        matcher = ContentMatcher(similarity_threshold=0.7)
        print("✅ 内容匹配器创建成功")
        
        # 测试相似度计算
        test_pairs = [
            ("退货", "退款"),
            ("发货时间", "什么时候发货"),
            ("产品质量", "质量怎么样"),
            ("客服电话", "联系方式")
        ]
        
        print("\n测试相似度计算:")
        for text1, text2 in test_pairs:
            similarity = matcher.calculate_similarity(text1, text2)
            print(f"   '{text1}' vs '{text2}': {similarity:.2f}")
        
        # 测试关键词提取
        test_text = "我想了解一下你们的产品质量和售后服务"
        keywords = matcher.extract_keywords(test_text)
        print(f"\n关键词提取测试:")
        print(f"   文本: {test_text}")
        print(f"   关键词: {keywords}")
        
    except Exception as e:
        print(f"❌ 内容匹配器测试失败: {e}")


def main():
    """主测试函数"""
    print("微信客服机器人 - 基础功能测试")
    print("=" * 50)
    
    # 运行各项测试
    test_imports()
    test_data_files()
    test_reply_engine()
    test_content_matcher()
    
    print("\n" + "=" * 50)
    print("测试完成！")
    print("\n如果所有测试都通过，可以尝试:")
    print("1. python web_config.py - 启动Web配置界面")
    print("2. python run.py - 启动机器人")


if __name__ == "__main__":
    main()
