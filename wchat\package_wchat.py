#!/usr/bin/env python3
"""
WChat机器人项目打包工具
将项目打包成可在其他电脑上使用的部署包
"""
import os
import sys
import shutil
import zipfile
import json
from pathlib import Path
from datetime import datetime

def create_package():
    """创建部署包"""
    print("🚀 WChat机器人项目打包工具")
    print("=" * 60)
    
    # 项目根目录
    project_root = Path(__file__).parent
    
    # 创建打包目录
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    package_name = f"wchat_deploy_{timestamp}"
    package_dir = project_root / "dist" / package_name
    
    print(f"📦 创建打包目录: {package_dir}")
    package_dir.mkdir(parents=True, exist_ok=True)
    
    # 定义需要包含的文件和目录
    include_items = [
        # 核心源码
        "src/",
        "config/",
        
        # 数据文件
        "data/faq.xlsx",
        "data/products.xlsx", 
        "data/images/",
        
        # 配置文件
        "requirements.txt",
        
        # 启动脚本
        "run.py",
        "start_wchat.py",
        "quick_start.py",
        
        # 批处理文件
        "start.bat",
        "启动WChat.bat",
        "快速启动.bat",
        
        # 工具脚本
        "install_deps.py",
        "web_config.py",
        "data_manager.py",
        
        # 文档
        "README.md",
        "README_启动说明.md",
        "语音消息功能使用指南.md",
        "通用产品匹配器使用指南.md"
    ]
    
    # 排除的文件和目录
    exclude_patterns = [
        "__pycache__",
        "*.pyc",
        "*.pyo", 
        ".git",
        ".gitignore",
        "test_*.py",
        "debug_*.py",
        "temp/",
        "wxauto_logs/",
        "data/logs/",
        "data/backups/",
        "dist/",
        "*.log"
    ]
    
    print(f"📋 复制项目文件...")
    
    # 复制文件
    copied_files = 0
    for item in include_items:
        source_path = project_root / item
        
        if source_path.exists():
            if source_path.is_file():
                # 复制文件
                dest_path = package_dir / item
                dest_path.parent.mkdir(parents=True, exist_ok=True)
                shutil.copy2(source_path, dest_path)
                copied_files += 1
                print(f"  ✅ {item}")
            elif source_path.is_dir():
                # 复制目录
                dest_path = package_dir / item
                shutil.copytree(source_path, dest_path, 
                              ignore=shutil.ignore_patterns(*exclude_patterns))
                copied_files += count_files(dest_path)
                print(f"  ✅ {item} (目录)")
        else:
            print(f"  ⚠️ {item} (不存在)")
    
    print(f"📁 总计复制文件: {copied_files} 个")
    
    # 创建部署配置
    create_deploy_config(package_dir)
    
    # 创建安装脚本
    create_install_script(package_dir)
    
    # 创建使用说明
    create_usage_guide(package_dir)
    
    # 清理配置文件中的敏感信息
    clean_config_file(package_dir)
    
    # 创建ZIP压缩包
    zip_path = create_zip_package(package_dir, package_name)
    
    print(f"\n🎉 打包完成！")
    print(f"📦 部署包位置: {zip_path}")
    print(f"📁 解压目录: {package_dir}")
    
    return zip_path

def count_files(directory):
    """统计目录中的文件数量"""
    count = 0
    for root, dirs, files in os.walk(directory):
        count += len(files)
    return count

def create_deploy_config(package_dir):
    """创建部署配置文件"""
    print(f"⚙️ 创建部署配置...")
    
    deploy_config = {
        "name": "WChat微信客服机器人",
        "version": "1.0.0",
        "description": "智能微信客服机器人，支持AI聊天、产品推荐、语音消息等功能",
        "author": "WChat Team",
        "created": datetime.now().isoformat(),
        "requirements": {
            "python": ">=3.8",
            "os": ["Windows", "Linux", "macOS"],
            "dependencies": "见requirements.txt"
        },
        "features": [
            "AI智能聊天",
            "产品推荐与图片发送", 
            "FAQ自动回复",
            "语音消息转文字",
            "Web配置界面",
            "实时监控面板"
        ],
        "quick_start": [
            "1. 运行 install_deps.py 安装依赖",
            "2. 配置 config/config.json 中的API密钥",
            "3. 运行 quick_start.py 启动机器人",
            "4. 访问 http://localhost:5000 进行配置"
        ]
    }
    
    config_file = package_dir / "deploy_info.json"
    with open(config_file, 'w', encoding='utf-8') as f:
        json.dump(deploy_config, f, ensure_ascii=False, indent=2)
    
    print(f"  ✅ deploy_info.json")

def create_install_script(package_dir):
    """创建安装脚本"""
    print(f"🔧 创建安装脚本...")
    
    # Windows安装脚本
    install_bat = """@echo off
chcp 65001 >nul
echo ========================================
echo   WChat微信客服机器人 - 自动安装
echo ========================================
echo.

echo 正在检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] 未检测到Python环境
    echo 请先安装Python 3.8或更高版本
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo [OK] Python环境检测成功

echo.
echo 正在安装依赖包...
python install_deps.py

echo.
echo 正在创建桌面快捷方式...
python -c "
import os
from pathlib import Path
desktop = Path.home() / 'Desktop'
shortcut_path = desktop / 'WChat机器人.bat'
current_dir = Path.cwd()
content = f'''@echo off
cd /d \"{current_dir}\"
python quick_start.py
pause'''
with open(shortcut_path, 'w', encoding='gbk') as f:
    f.write(content)
print(f'[OK] 桌面快捷方式已创建: {shortcut_path}')
"

echo.
echo ========================================
echo   安装完成！
echo ========================================
echo.
echo 下一步操作:
echo 1. 配置API密钥 (编辑 config/config.json)
echo 2. 双击桌面上的 "WChat机器人" 启动
echo 3. 或运行: python quick_start.py
echo.
pause
"""
    
    install_file = package_dir / "install.bat"
    with open(install_file, 'w', encoding='utf-8') as f:
        f.write(install_bat)
    
    # Linux/macOS安装脚本
    install_sh = """#!/bin/bash
echo "========================================"
echo "  WChat微信客服机器人 - 自动安装"
echo "========================================"
echo

echo "正在检查Python环境..."
if ! command -v python3 &> /dev/null; then
    echo "[ERROR] 未检测到Python3环境"
    echo "请先安装Python 3.8或更高版本"
    exit 1
fi

echo "[OK] Python环境检测成功"

echo
echo "正在安装依赖包..."
python3 install_deps.py

echo
echo "========================================"
echo "  安装完成！"
echo "========================================"
echo
echo "下一步操作:"
echo "1. 配置API密钥 (编辑 config/config.json)"
echo "2. 运行: python3 quick_start.py"
echo
"""
    
    install_sh_file = package_dir / "install.sh"
    with open(install_sh_file, 'w', encoding='utf-8') as f:
        f.write(install_sh)
    
    # 设置执行权限
    try:
        os.chmod(install_sh_file, 0o755)
    except:
        pass
    
    print(f"  ✅ install.bat (Windows)")
    print(f"  ✅ install.sh (Linux/macOS)")

def create_usage_guide(package_dir):
    """创建使用说明"""
    print(f"📖 创建使用说明...")
    
    usage_guide = """# WChat微信客服机器人 - 部署使用指南

## 🚀 快速开始

### 1. 安装依赖
```bash
# Windows
install.bat

# Linux/macOS  
chmod +x install.sh
./install.sh
```

### 2. 配置API密钥
编辑 `config/config.json` 文件，设置您的AI API密钥：
```json
{
  "ai": {
    "api_key": "您的硅基流动API密钥",
    "base_url": "https://api.siliconflow.cn/v1",
    "model": "Qwen/Qwen2.5-7B-Instruct"
  }
}
```

### 3. 启动机器人
```bash
# 方式1: 快速启动
python quick_start.py

# 方式2: 完整启动
python start_wchat.py

# 方式3: Windows批处理
双击 "快速启动.bat"
```

### 4. Web配置界面
启动后访问: http://localhost:5000
- 实时监控机器人状态
- 在线配置参数
- 管理FAQ和产品数据

## 📋 功能特点

### 🤖 AI智能聊天
- 支持自然语言对话
- 拟人化回复，不暴露AI身份
- 基于硅基流动API

### 📦 产品推荐
- 智能产品匹配
- 自动发送产品图片
- 支持Excel产品库管理

### 🎤 语音消息
- 语音转文字功能
- 支持语音产品查询
- 自动语音回复

### 💬 FAQ自动回复
- Excel格式FAQ管理
- 智能问题匹配
- 优先级回复逻辑

## ⚙️ 配置说明

### 基础配置
```json
{
  "wechat": {
    "auto_reply": true,           // 自动回复
    "reply_delay": 2,             // 回复延迟(秒)
    "listen_all": true,           // 监听所有消息
    "voice_to_text": true         // 语音转文字
  }
}
```

### AI配置
```json
{
  "ai": {
    "enabled": true,              // 启用AI
    "api_key": "your_api_key",    // API密钥
    "model": "Qwen/Qwen2.5-7B-Instruct"
  }
}
```

## 📁 目录结构

```
wchat/
├── src/                 # 源代码
│   ├── ai/             # AI服务
│   ├── bot/            # 机器人核心
│   ├── database/       # 数据处理
│   └── web/            # Web界面
├── config/             # 配置文件
├── data/               # 数据文件
│   ├── faq.xlsx        # FAQ数据
│   ├── products.xlsx   # 产品数据
│   └── images/         # 产品图片
├── quick_start.py      # 快速启动
└── requirements.txt    # 依赖列表
```

## 🔧 常见问题

### Q: 如何获取API密钥？
A: 访问 https://siliconflow.cn 注册并获取API密钥

### Q: 微信无法连接？
A: 确保微信PC版已登录，并允许程序访问

### Q: 语音转文字不工作？
A: 检查微信版本是否支持语音转文字API

### Q: 产品图片不显示？
A: 确保图片文件存在于 data/images/ 目录中

## 📞 技术支持

如有问题，请检查：
1. Python版本 >= 3.8
2. 依赖包是否正确安装
3. 配置文件格式是否正确
4. 微信PC版是否正常运行

## 🎉 开始使用

1. 完成上述配置后启动机器人
2. 向机器人发送"你好"测试基本功能
3. 发送"推荐手机"测试产品功能
4. 发送语音消息测试语音功能

祝您使用愉快！
"""
    
    guide_file = package_dir / "使用说明.md"
    with open(guide_file, 'w', encoding='utf-8') as f:
        f.write(usage_guide)
    
    print(f"  ✅ 使用说明.md")

def clean_config_file(package_dir):
    """清理配置文件中的敏感信息"""
    print(f"🧹 清理配置文件...")
    
    config_file = package_dir / "config" / "config.json"
    if config_file.exists():
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # 清除API密钥
        if 'ai' in config and 'api_key' in config['ai']:
            config['ai']['api_key'] = ""
        
        # 清除监听列表
        if 'wechat' in config and 'listen_list' in config['wechat']:
            config['wechat']['listen_list'] = []
        
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=2)
        
        print(f"  ✅ 已清理敏感信息")

def create_zip_package(package_dir, package_name):
    """创建ZIP压缩包"""
    print(f"📦 创建ZIP压缩包...")
    
    zip_path = package_dir.parent / f"{package_name}.zip"
    
    with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
        for root, dirs, files in os.walk(package_dir):
            for file in files:
                file_path = Path(root) / file
                arc_path = file_path.relative_to(package_dir.parent)
                zipf.write(file_path, arc_path)
    
    # 获取压缩包大小
    size_mb = zip_path.stat().st_size / (1024 * 1024)
    print(f"  ✅ {zip_path.name} ({size_mb:.1f} MB)")
    
    return zip_path

def main():
    """主函数"""
    try:
        zip_path = create_package()
        
        print(f"\n" + "=" * 60)
        print(f"          打包完成！")
        print(f"=" * 60)
        print(f"📦 压缩包: {zip_path}")
        print(f"📁 大小: {zip_path.stat().st_size / (1024*1024):.1f} MB")
        print(f"\n💡 部署步骤:")
        print(f"   1. 将压缩包复制到目标电脑")
        print(f"   2. 解压到任意目录")
        print(f"   3. 运行 install.bat (Windows) 或 install.sh (Linux)")
        print(f"   4. 配置API密钥")
        print(f"   5. 启动机器人")
        print(f"\n🚀 开始部署您的WChat机器人！")
        
    except Exception as e:
        print(f"❌ 打包失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
