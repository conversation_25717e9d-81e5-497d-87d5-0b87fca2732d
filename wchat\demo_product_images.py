#!/usr/bin/env python3
"""
产品图片功能演示
展示完整的产品推荐流程，包括文本回复和图片发送
"""
import os
import sys
import time
from pathlib import Path

# 添加项目路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def demo_product_images():
    """演示产品图片功能"""
    print("🎬 产品图片功能演示")
    print("=" * 60)
    
    try:
        from src.database.enhanced_reader import EnhancedProductReader
        from src.bot.product_image_handler import ProductImageHandler
        from config import config
        
        # 初始化组件
        product_reader = EnhancedProductReader(config.database.products_file)
        image_handler = ProductImageHandler()
        
        print(f"✅ 系统初始化完成")
        print(f"📦 产品数据: {len(product_reader.data)} 条")
        print(f"📷 图片文件: {len(image_handler.name_mapping)} 张")
        print()
        
        # 演示场景
        demo_scenarios = [
            {
                "title": "🔍 场景1: 用户询问手机推荐",
                "user_query": "我想买个手机，有什么推荐的吗？",
                "expected": "应该推荐智能手机A1并发送产品图片"
            },
            {
                "title": "🎧 场景2: 用户询问耳机",
                "user_query": "有什么好的蓝牙耳机",
                "expected": "应该推荐无线蓝牙耳机B2并发送产品图片"
            },
            {
                "title": "💻 场景3: 用户询问笔记本",
                "user_query": "笔记本电脑价格怎么样",
                "expected": "应该推荐笔记本电脑D4并发送产品图片"
            }
        ]
        
        for i, scenario in enumerate(demo_scenarios, 1):
            print(f"{scenario['title']}")
            print(f"👤 用户: {scenario['user_query']}")
            print(f"💭 预期: {scenario['expected']}")
            print()
            
            # 搜索产品
            products = product_reader.search_products(scenario['user_query'])
            
            if products:
                # 只推荐第一个产品（避免演示过长）
                top_product = products[:1]
                
                # 生成回复和图片
                reply, image_paths = image_handler.format_product_reply_with_images(top_product)
                
                print("🤖 机器人回复:")
                print("-" * 40)
                print(reply)
                print("-" * 40)
                
                if image_paths:
                    print(f"\n📷 发送产品图片:")
                    for img_path in image_paths:
                        filename = os.path.basename(img_path)
                        if os.path.exists(img_path):
                            file_size = os.path.getsize(img_path)
                            print(f"   ✅ {filename} ({file_size} bytes)")
                        else:
                            print(f"   ❌ {filename} (文件不存在)")
                else:
                    print(f"\n📷 无图片发送")
            else:
                print("🤖 机器人回复:")
                print("-" * 40)
                print("抱歉，没有找到相关产品。")
                print("-" * 40)
            
            print(f"\n✅ 场景 {i} 演示完成")
            print("=" * 60)
            print()
        
        return True
        
    except Exception as e:
        print(f"❌ 演示失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def show_product_catalog():
    """展示产品目录"""
    print("📋 产品目录")
    print("=" * 60)
    
    try:
        from src.database.enhanced_reader import EnhancedProductReader
        from src.bot.product_image_handler import ProductImageHandler
        from config import config
        
        product_reader = EnhancedProductReader(config.database.products_file)
        image_handler = ProductImageHandler()
        
        print("当前可用产品:")
        print()
        
        for i, (_, product) in enumerate(product_reader.data.iterrows(), 1):
            name = product['产品名称']
            price = product['价格']
            desc = product['产品描述']
            
            print(f"{i}. {name}")
            print(f"   💰 价格: ¥{price}")
            print(f"   📝 描述: {desc}")
            
            # 检查图片
            img_path = image_handler.get_product_image_path(name)
            if img_path and os.path.exists(img_path):
                filename = os.path.basename(img_path)
                file_size = os.path.getsize(img_path)
                print(f"   📷 图片: {filename} ({file_size} bytes)")
            else:
                print(f"   📷 图片: 暂无")
            
            print()
        
        return True
        
    except Exception as e:
        print(f"❌ 展示失败: {e}")
        return False

def interactive_demo():
    """交互式演示"""
    print("🎮 交互式产品查询演示")
    print("=" * 60)
    print("输入产品关键词来查询产品，输入 'quit' 退出")
    print()
    
    try:
        from src.database.enhanced_reader import EnhancedProductReader
        from src.bot.product_image_handler import ProductImageHandler
        from config import config
        
        product_reader = EnhancedProductReader(config.database.products_file)
        image_handler = ProductImageHandler()
        
        while True:
            try:
                user_input = input("👤 您: ").strip()
                
                if user_input.lower() in ['quit', 'exit', '退出', 'q']:
                    print("👋 再见！")
                    break
                
                if not user_input:
                    continue
                
                print()
                
                # 搜索产品
                products = product_reader.search_products(user_input)
                
                if products:
                    # 限制显示数量
                    display_products = products[:2]
                    
                    # 生成回复
                    reply, image_paths = image_handler.format_product_reply_with_images(display_products)
                    
                    print("🤖 机器人:")
                    print(reply)
                    
                    if image_paths:
                        print(f"\n📷 图片文件:")
                        for img_path in image_paths:
                            filename = os.path.basename(img_path)
                            print(f"   - {filename}")
                else:
                    print("🤖 机器人: 抱歉，没有找到相关产品。您可以尝试搜索：手机、耳机、笔记本、充电器、鼠标、手表")
                
                print()
                
            except KeyboardInterrupt:
                print("\n👋 再见！")
                break
            except EOFError:
                print("\n👋 再见！")
                break
        
        return True
        
    except Exception as e:
        print(f"❌ 交互演示失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 产品图片功能完整演示")
    print()
    
    # 选择演示模式
    print("请选择演示模式:")
    print("1. 自动演示 - 展示预设场景")
    print("2. 产品目录 - 查看所有产品")
    print("3. 交互模式 - 手动输入查询")
    print("4. 全部运行")
    print()
    
    try:
        choice = input("请输入选择 (1-4): ").strip()
        print()
        
        if choice == '1':
            demo_product_images()
        elif choice == '2':
            show_product_catalog()
        elif choice == '3':
            interactive_demo()
        elif choice == '4':
            print("🎬 运行全部演示")
            print()
            
            print("1️⃣ 产品目录展示")
            show_product_catalog()
            
            print("\n2️⃣ 自动场景演示")
            demo_product_images()
            
            print("\n3️⃣ 交互式演示")
            interactive_demo()
        else:
            print("❌ 无效选择，运行默认演示")
            demo_product_images()
    
    except KeyboardInterrupt:
        print("\n👋 演示结束")
    except EOFError:
        print("\n👋 演示结束")

if __name__ == "__main__":
    main()
