"""
查看当前数据库内容
"""
import os
import sys
import pandas as pd

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def view_faq_data():
    """查看FAQ数据"""
    print("=" * 50)
    print("FAQ数据库内容")
    print("=" * 50)
    
    try:
        faq_file = os.path.join(current_dir, 'data', 'faq.xlsx')
        df = pd.read_excel(faq_file)
        
        print(f"总共 {len(df)} 条FAQ记录")
        print("\n数据结构:")
        print(df.columns.tolist())
        
        print("\n前5条记录:")
        for i, row in df.head().iterrows():
            print(f"\n{i+1}. 问题: {row.iloc[0]}")
            print(f"   答案: {row.iloc[1]}")
            
    except Exception as e:
        print(f"读取FAQ数据失败: {e}")

def view_products_data():
    """查看产品数据"""
    print("\n" + "=" * 50)
    print("产品数据库内容")
    print("=" * 50)
    
    try:
        products_file = os.path.join(current_dir, 'data', 'products.xlsx')
        df = pd.read_excel(products_file)
        
        print(f"总共 {len(df)} 条产品记录")
        print("\n数据结构:")
        print(df.columns.tolist())
        
        print("\n前5条记录:")
        for i, row in df.head().iterrows():
            print(f"\n{i+1}. 产品: {row.iloc[0]}")
            if len(row) > 1:
                print(f"   描述: {row.iloc[1]}")
            if len(row) > 2:
                print(f"   其他: {row.iloc[2]}")
                
    except Exception as e:
        print(f"读取产品数据失败: {e}")

def main():
    """主函数"""
    print("数据库内容查看工具")
    
    view_faq_data()
    view_products_data()

if __name__ == "__main__":
    main()
