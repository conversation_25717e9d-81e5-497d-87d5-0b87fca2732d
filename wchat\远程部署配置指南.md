# 私域自动化系统 - 远程部署配置指南

## 🌐 概述

本指南将帮助您配置私域自动化系统的远程访问功能，实现真正的网络远程管理。通过本指南，您可以：

- 🌍 **外网访问**：从任何地方访问系统
- 🔐 **安全防护**：IP白名单、访问频率限制
- 🛡️ **HTTPS支持**：加密传输保护数据安全
- 📱 **多设备支持**：手机、平板、电脑都能访问

## 🚀 快速部署

### 方式一：使用配置工具（推荐）

```bash
# 1. 运行网络配置工具
cd wchat
python 网络配置工具.py

# 2. 选择"配置远程访问"
# 3. 按提示配置监听地址、端口等
# 4. 选择"配置安全设置"设置IP白名单
```

### 方式二：手动配置

编辑 `wchat/config/config.json` 文件：

```json
{
  "web": {
    "host": "0.0.0.0",
    "port": 5000,
    "external_url": "http://your-domain.com:5000",
    "allowed_ips": ["***********/24", "**********"],
    "enable_https": false,
    "password": "your-password",
    "admin_password": "your-admin-password"
  }
}
```

## 📋 详细配置步骤

### 1. 基础网络配置

#### 监听地址设置
```json
"host": "0.0.0.0"  // 允许所有网络接口访问
```

- `127.0.0.1` - 仅本机访问
- `0.0.0.0` - 允许所有网络接口访问（推荐）
- `*************` - 仅指定IP访问

#### 端口配置
```json
"port": 5000  // 默认端口，可修改为其他端口
```

常用端口建议：
- `5000` - 默认端口
- `8080` - 常用Web端口
- `8888` - 备用端口

### 2. 防火墙配置

#### Windows防火墙

1. **打开防火墙设置**
   - Win + R → `wf.msc` → 回车

2. **创建入站规则**
   - 右键"入站规则" → "新建规则"
   - 选择"端口" → "下一步"
   - 选择"TCP"，输入端口号（如5000）
   - 选择"允许连接"
   - 应用到所有配置文件
   - 命名为"私域自动化系统"

#### Linux防火墙（Ubuntu/CentOS）

```bash
# Ubuntu (ufw)
sudo ufw allow 5000/tcp
sudo ufw reload

# CentOS (firewalld)
sudo firewall-cmd --permanent --add-port=5000/tcp
sudo firewall-cmd --reload
```

### 3. 路由器端口映射

#### 配置步骤

1. **登录路由器管理界面**
   - 通常是 `***********` 或 `***********`

2. **找到端口映射设置**
   - 可能叫"虚拟服务器"、"端口转发"、"NAT"

3. **添加映射规则**
   ```
   服务名称: 私域自动化系统
   外部端口: 5000
   内部端口: 5000
   内部IP: ************* (运行系统的电脑IP)
   协议: TCP
   ```

4. **保存并重启路由器**

### 4. 安全配置

#### IP白名单设置

```json
"allowed_ips": [
  "***********/24",     // 整个局域网
  "**********",         // 单个IP
  "***********/24"      // 公网网段
]
```

#### 密码安全

```json
"password": "strong-password-123",
"admin_password": "admin-strong-password-456"
```

建议：
- 使用强密码（8位以上，包含字母数字符号）
- 定期更换密码
- 管理员密码与普通密码不同

### 5. HTTPS配置（可选）

#### 生成SSL证书

```bash
# 使用OpenSSL生成自签名证书
openssl req -x509 -newkey rsa:4096 -keyout key.pem -out cert.pem -days 365 -nodes

# 或使用Let's Encrypt免费证书
certbot certonly --standalone -d yourdomain.com
```

#### 配置HTTPS

```json
"enable_https": true,
"ssl_cert": "/path/to/cert.pem",
"ssl_key": "/path/to/key.pem"
```

## 🌍 访问方式

### 本地访问
```
http://127.0.0.1:5000
http://localhost:5000
```

### 局域网访问
```
http://*************:5000  (替换为实际IP)
```

### 公网访问
```
http://your-public-ip:5000
http://your-domain.com:5000
```

### HTTPS访问
```
https://your-domain.com:5000
```

## 🚀 启动远程服务

### 方式一：使用远程启动脚本

```bash
# Windows
双击运行 "启动远程服务.bat"

# Linux/Mac
chmod +x start_remote.sh
./start_remote.sh
```

### 方式二：手动启动

```bash
cd wchat
python web_config.py
```

## 📱 多设备访问

### 手机访问

1. 确保手机与服务器在同一网络
2. 打开手机浏览器
3. 输入服务器IP地址：`http://*************:5000`
4. 使用密码登录

### 平板/笔记本访问

同手机访问方式，支持响应式界面。

## 🔧 故障排除

### 常见问题

#### 1. 无法访问服务
```
检查项目：
✓ 防火墙是否开放端口
✓ 路由器是否配置端口映射
✓ IP白名单是否包含访问IP
✓ 服务是否正常启动
```

#### 2. 访问被拒绝
```
可能原因：
- IP不在白名单中
- 访问频率过高被限制
- 密码错误次数过多
```

#### 3. HTTPS证书错误
```
解决方案：
- 检查证书文件路径
- 确认证书有效期
- 使用受信任的CA证书
```

### 网络测试工具

```bash
# 测试端口连通性
telnet your-server-ip 5000

# 测试HTTP访问
curl http://your-server-ip:5000

# 查看网络配置
python 网络配置工具.py
```

## 🛡️ 安全最佳实践

### 1. 网络安全

- ✅ 使用强密码
- ✅ 设置IP白名单
- ✅ 启用HTTPS
- ✅ 定期更新系统
- ✅ 监控访问日志

### 2. 服务器安全

- ✅ 关闭不必要的端口
- ✅ 使用防火墙
- ✅ 定期备份数据
- ✅ 更新操作系统补丁

### 3. 访问控制

- ✅ 限制管理员账户数量
- ✅ 定期审查访问权限
- ✅ 记录操作日志
- ✅ 设置会话超时

## 📞 技术支持

### 获取帮助

1. **查看日志文件**
   - 位置：`wchat/logs/`
   - 包含详细错误信息

2. **使用网络配置工具**
   - 运行：`python 网络配置工具.py`
   - 选择"测试网络连接"

3. **联系技术支持**
   - 提供错误日志
   - 描述网络环境
   - 说明配置步骤

### 常用命令

```bash
# 查看端口占用
netstat -an | findstr :5000

# 查看网络连接
ipconfig /all

# 测试DNS解析
nslookup your-domain.com

# 查看路由表
route print
```

---

**私域自动化系统 - 远程部署配置**  
**安全可靠 · 全球访问 · 专业部署** 🌐🔐✨
