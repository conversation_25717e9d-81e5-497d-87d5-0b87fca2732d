#!/usr/bin/env python3
"""
测试AI API修复
"""
import sys
from pathlib import Path

# 添加项目路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def test_ai_config():
    """测试AI配置"""
    print("=" * 60)
    print("          测试AI配置")
    print("=" * 60)
    
    try:
        from config import config
        
        print("修复前后对比:")
        print("修复前: https://api.siliconflow.cn/v1/chat/completions")
        print("修复后: https://api.siliconflow.cn/v1")
        print()
        
        print("当前AI配置:")
        print(f"  API密钥: {config.ai.api_key[:20]}...{config.ai.api_key[-10:]}")
        print(f"  基础URL: {config.ai.base_url}")
        print(f"  模型: {config.ai.model}")
        print(f"  最大令牌: {config.ai.max_tokens}")
        print(f"  温度: {config.ai.temperature}")
        print(f"  启用状态: {config.ai.enabled}")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置加载失败: {e}")
        return False

def test_direct_api():
    """直接测试API"""
    print("\n" + "=" * 60)
    print("          直接测试API")
    print("=" * 60)
    
    try:
        from openai import OpenAI
        from config import config
        
        print("正在创建OpenAI客户端...")
        client = OpenAI(
            api_key=config.ai.api_key,
            base_url=config.ai.base_url
        )
        print("✅ OpenAI客户端创建成功")
        
        print("正在测试API调用...")
        response = client.chat.completions.create(
            model=config.ai.model,
            messages=[
                {"role": "user", "content": "你好，请简单回复一下"}
            ],
            max_tokens=50,
            temperature=0.7
        )
        
        if response and response.choices:
            content = response.choices[0].message.content
            print(f"✅ API调用成功！")
            print(f"AI回复: {content}")
            return True
        else:
            print("❌ API调用返回空结果")
            return False
            
    except Exception as e:
        print(f"❌ API调用失败: {e}")
        print(f"错误类型: {type(e).__name__}")
        return False

def test_llm_service():
    """测试LLM服务"""
    print("\n" + "=" * 60)
    print("          测试LLM服务")
    print("=" * 60)
    
    try:
        from src.ai.llm_service import LLMService
        from config import config
        
        print("正在创建LLM服务...")
        llm = LLMService(
            api_key=config.ai.api_key,
            base_url=config.ai.base_url,
            model=config.ai.model,
            max_tokens=config.ai.max_tokens,
            temperature=config.ai.temperature
        )
        print("✅ LLM服务创建成功")
        
        print("正在初始化客户端...")
        llm.initialize_client()
        
        if llm.client:
            print("✅ LLM客户端初始化成功")
        else:
            print("❌ LLM客户端初始化失败")
            return False
        
        print("正在测试生成回复...")
        response = llm.generate_response("你好，请问如何退货？")
        
        if response:
            print(f"✅ LLM服务正常！")
            print(f"AI回复: {response}")
            return True
        else:
            print("❌ LLM服务返回空回复")
            return False
        
    except Exception as e:
        print(f"❌ LLM服务测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("开始测试AI API修复...")
    
    # 测试配置
    if not test_ai_config():
        print("\n❌ 配置测试失败")
        return
    
    print("\n✅ 配置测试通过")
    
    # 测试直接API调用
    if test_direct_api():
        print("\n✅ 直接API测试通过")
    else:
        print("\n❌ 直接API测试失败")
        return
    
    # 测试LLM服务
    if test_llm_service():
        print("\n✅ LLM服务测试通过")
    else:
        print("\n❌ LLM服务测试失败")
        return
    
    print("\n" + "=" * 60)
    print("🎉 AI API修复成功！")
    print("=" * 60)
    
    print("\n修复内容:")
    print("1. ✅ 修正了base_url配置")
    print("   - 修复前: https://api.siliconflow.cn/v1/chat/completions")
    print("   - 修复后: https://api.siliconflow.cn/v1")
    print("2. ✅ API调用正常工作")
    print("3. ✅ LLM服务正常工作")
    print("4. ✅ AI回复功能恢复")
    
    print("\n现在您的WChat系统的AI功能应该正常工作了！")

if __name__ == "__main__":
    main()
