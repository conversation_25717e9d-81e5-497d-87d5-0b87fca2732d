#!/usr/bin/env python3
"""
私域自动化系统 - 快速启动
"""

import os
import sys
import socket
import webbrowser
import time
from pathlib import Path

def get_local_ip():
    """获取本机IP"""
    try:
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        ip = s.getsockname()[0]
        s.close()
        return ip
    except:
        return "127.0.0.1"

def main():
    print("=" * 60)
    print("           私域自动化系统 - 快速启动")
    print("=" * 60)
    print()
    
    # 显示网络信息
    local_ip = get_local_ip()
    print("🌐 访问地址:")
    print(f"   本地访问: http://127.0.0.1:5000")
    print(f"   局域网访问: http://{local_ip}:5000")
    print()
    
    print("🔐 登录密码:")
    print("   普通用户: admin123")
    print("   管理员: admin888 (可生成许可证)")
    print()
    
    print("🚀 正在启动Web服务器...")
    print("   启动后会自动打开浏览器")
    print("   按 Ctrl+C 停止服务")
    print("=" * 60)
    print()
    
    # 启动Web服务器
    try:
        import subprocess
        
        # 启动服务器进程
        process = subprocess.Popen([sys.executable, "web_config.py"])
        
        # 等待2秒后打开浏览器
        time.sleep(2)
        try:
            webbrowser.open("http://127.0.0.1:5000")
            print("✅ 浏览器已打开，请使用密码登录")
        except:
            print("💡 请手动在浏览器中访问: http://127.0.0.1:5000")
        
        # 等待进程结束
        process.wait()
        
    except KeyboardInterrupt:
        print("\n\n👋 服务已停止")
        try:
            process.terminate()
        except:
            pass
    except Exception as e:
        print(f"\n❌ 启动失败: {e}")
        print("💡 请确保已安装依赖包: pip install -r requirements.txt")

if __name__ == "__main__":
    main()
