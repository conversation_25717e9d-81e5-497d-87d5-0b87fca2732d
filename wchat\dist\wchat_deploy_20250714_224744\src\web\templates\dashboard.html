{% extends "base.html" %}

{% block title %}仪表板 - 微信客服机器人{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-tachometer-alt me-2"></i>
        仪表板
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <button type="button" class="btn btn-outline-primary btn-sm" onclick="refreshStats()">
            <i class="fas fa-sync-alt me-1"></i>刷新数据
        </button>
    </div>
</div>

<!-- 统计卡片 -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            FAQ条目
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="faq-count">
                            {{ stats.faq_count or 0 }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-question-circle fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            产品数量
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="product-count">
                            {{ stats.product_count or 0 }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-box fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            AI状态
                        </div>
                        <div class="h5 mb-0 font-weight-bold" id="ai-status">
                            {% if stats.ai_available %}
                                <span class="text-success">
                                    <i class="fas fa-check-circle me-1"></i>可用
                                </span>
                            {% else %}
                                <span class="text-danger">
                                    <i class="fas fa-times-circle me-1"></i>不可用
                                </span>
                            {% endif %}
                        </div>
                        <div class="text-xs text-muted mt-1" id="ai-model-info">
                            <i class="fas fa-robot me-1"></i>
                            <span id="current-ai-model">{{ config.ai.model }}</span>
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-brain fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            监听对象
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="listen-count">
                            {{ config.wechat.listen_list|length or 0 }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-comments fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 系统状态 -->
<div class="row">
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-cog me-2"></i>系统配置
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-sm-6">
                        <p><strong>自动回复:</strong> 
                            <span class="badge bg-{{ 'success' if config.wechat.auto_reply else 'secondary' }}">
                                {{ '启用' if config.wechat.auto_reply else '禁用' }}
                            </span>
                        </p>
                        <p><strong>回复延迟:</strong> {{ config.wechat.reply_delay }}秒</p>
                        <p><strong>相似度阈值:</strong> {{ config.database.similarity_threshold }}</p>
                    </div>
                    <div class="col-sm-6">
                        <p><strong>优先FAQ:</strong> 
                            <span class="badge bg-{{ 'success' if config.reply.priority_faq else 'secondary' }}">
                                {{ '是' if config.reply.priority_faq else '否' }}
                            </span>
                        </p>
                        <p><strong>优先产品库:</strong> 
                            <span class="badge bg-{{ 'success' if config.reply.priority_products else 'secondary' }}">
                                {{ '是' if config.reply.priority_products else '否' }}
                            </span>
                        </p>
                        <p><strong>AI辅助:</strong> 
                            <span class="badge bg-{{ 'success' if config.reply.use_ai_fallback else 'secondary' }}">
                                {{ '启用' if config.reply.use_ai_fallback else '禁用' }}
                            </span>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-list me-2"></i>数据分类
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-sm-6">
                        <h6>FAQ分类</h6>
                        <ul class="list-unstyled" id="faq-categories">
                            {% for category in stats.faq_categories or [] %}
                                <li><i class="fas fa-tag me-1"></i>{{ category }}</li>
                            {% else %}
                                <li class="text-muted">暂无分类</li>
                            {% endfor %}
                        </ul>
                    </div>
                    <div class="col-sm-6">
                        <h6>产品分类</h6>
                        <ul class="list-unstyled" id="product-categories">
                            {% for category in stats.product_categories or [] %}
                                <li><i class="fas fa-tag me-1"></i>{{ category }}</li>
                            {% else %}
                                <li class="text-muted">暂无分类</li>
                            {% endfor %}
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 快速操作 -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-bolt me-2"></i>快速操作
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-2">
                        <button class="btn btn-outline-primary w-100" onclick="reloadData()">
                            <i class="fas fa-sync-alt me-1"></i>重新加载数据
                        </button>
                    </div>
                    <div class="col-md-3 mb-2">
                        <button class="btn btn-outline-success w-100" onclick="testAI()">
                            <i class="fas fa-brain me-1"></i>测试AI连接
                        </button>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="{{ url_for('config_page') }}" class="btn btn-outline-info w-100">
                            <i class="fas fa-cog me-1"></i>修改配置
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="{{ url_for('data_management') }}" class="btn btn-outline-warning w-100">
                            <i class="fas fa-database me-1"></i>数据管理
                        </a>
                    </div>
                </div>
                <div class="row mt-2">
                    <div class="col-md-4 mb-2">
                        <button class="btn btn-outline-secondary w-100" onclick="testReply()">
                            <i class="fas fa-comment me-1"></i>测试回复
                        </button>
                    </div>
                    <div class="col-md-4 mb-2">
                        <a href="{{ url_for('faq_page') }}" class="btn btn-outline-info w-100">
                            <i class="fas fa-question-circle me-1"></i>FAQ管理
                        </a>
                    </div>
                    <div class="col-md-4 mb-2">
                        <a href="{{ url_for('products_page') }}" class="btn btn-outline-success w-100">
                            <i class="fas fa-box me-1"></i>产品管理
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function refreshStats() {
    $.get('/api/stats')
        .done(function(data) {
            $('#faq-count').text(data.faq_count || 0);
            $('#product-count').text(data.product_count || 0);
            
            // 更新AI状态
            if (data.ai_available) {
                $('#ai-status').html('<span class="text-success"><i class="fas fa-check-circle me-1"></i>可用</span>');
            } else {
                $('#ai-status').html('<span class="text-danger"><i class="fas fa-times-circle me-1"></i>不可用</span>');
            }

            // 更新AI模型信息
            if (data.current_model) {
                $('#current-ai-model').text(data.current_model);
            }
            
            // 更新分类列表
            updateCategories('#faq-categories', data.faq_categories);
            updateCategories('#product-categories', data.product_categories);
            
            showAlert('数据刷新成功', 'success');
        })
        .fail(function() {
            showAlert('数据刷新失败', 'danger');
        });
}

function updateCategories(selector, categories) {
    const container = $(selector);
    container.empty();
    
    if (categories && categories.length > 0) {
        categories.forEach(function(category) {
            container.append('<li><i class="fas fa-tag me-1"></i>' + category + '</li>');
        });
    } else {
        container.append('<li class="text-muted">暂无分类</li>');
    }
}

function reloadData() {
    $.post('/api/reload_data')
        .done(function(data) {
            showAlert(data.message, 'success');
            refreshStats();
        })
        .fail(function(xhr) {
            const error = xhr.responseJSON ? xhr.responseJSON.error : '操作失败';
            showAlert(error, 'danger');
        });
}

function testAI() {
    $.post('/api/test_ai')
        .done(function(data) {
            showAlert(data.message, 'success');
        })
        .fail(function(xhr) {
            const error = xhr.responseJSON ? xhr.responseJSON.error : 'AI测试失败';
            showAlert(error, 'danger');
        });
}

function testReply() {
    const message = prompt('请输入测试消息:');
    if (!message) return;

    $.ajax({
        url: '/api/test_reply',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({message: message}),
        success: function(data) {
            const mode = data.enhanced_mode ? '增强模式' : '标准模式';
            const alertHtml = `
                <div class="alert alert-info alert-dismissible fade show" role="alert">
                    <h6><i class="fas fa-comment me-1"></i>回复测试结果 (${mode})</h6>
                    <p><strong>输入:</strong> ${data.message}</p>
                    <p><strong>回复:</strong> ${data.reply}</p>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
            $('.main-content .pt-3').prepend(alertHtml);
        },
        error: function(xhr) {
            const error = xhr.responseJSON ? xhr.responseJSON.error : '测试失败';
            showAlert(error, 'danger');
        }
    });
}

function showAlert(message, type) {
    const alertHtml = `
        <div class="alert alert-${type} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    $('.main-content .pt-3').prepend(alertHtml);
}
</script>
{% endblock %}
