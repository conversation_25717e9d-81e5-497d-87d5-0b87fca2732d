"""
大语言模型服务
提供AI回复功能，当FAQ和产品库无法匹配时使用
"""
from openai import OpenAI
from typing import Optional, Dict, Any
from src.utils.logger import get_logger

logger = get_logger("llm_service")


class LLMService:
    """大语言模型服务类"""
    
    def __init__(self, api_key: str, base_url: str = "https://api.deepseek.com",
                 model: str = "deepseek-chat", max_tokens: int = 1000,
                 temperature: float = 0.7, system_prompt: str = None):
        self.api_key = api_key
        self.base_url = base_url
        self.model = model
        self.max_tokens = max_tokens
        self.temperature = temperature
        self.system_prompt = system_prompt or self._build_default_system_prompt()
        self.client = None

        if api_key:
            self.initialize_client()
    
    def initialize_client(self):
        """初始化OpenAI客户端"""
        try:
            self.client = OpenAI(
                api_key=self.api_key,
                base_url=self.base_url
            )
            logger.info("LLM客户端初始化成功")
        except Exception as e:
            logger.error(f"LLM客户端初始化失败: {e}")
            self.client = None
    
    def is_available(self) -> bool:
        """检查LLM服务是否可用"""
        return self.client is not None and bool(self.api_key)
    
    def generate_reply(self, user_message: str, context: Optional[str] = None) -> Optional[str]:
        """
        生成AI回复
        
        Args:
            user_message: 用户消息
            context: 上下文信息
            
        Returns:
            Optional[str]: AI生成的回复
        """
        if not self.is_available():
            logger.warning("LLM服务不可用")
            return None
        
        try:
            # 使用配置的系统提示词
            system_prompt = self.system_prompt
            
            # 构建消息列表
            messages = [
                {"role": "system", "content": system_prompt}
            ]
            
            # 添加上下文信息
            if context:
                messages.append({
                    "role": "system", 
                    "content": f"相关信息：{context}"
                })
            
            # 添加用户消息
            messages.append({
                "role": "user", 
                "content": user_message
            })
            
            # 调用API
            response = self.client.chat.completions.create(
                model=self.model,
                messages=messages,
                max_tokens=self.max_tokens,
                temperature=self.temperature
            )
            
            reply = response.choices[0].message.content.strip()
            logger.info(f"AI回复生成成功，长度: {len(reply)}")
            return reply
            
        except Exception as e:
            logger.error(f"生成AI回复失败: {e}")
            return None
    
    def _build_default_system_prompt(self) -> str:
        """构建系统提示词"""
        return """你是一个专业的智能客服助手，专门负责产品推荐和客户服务。

你的主要职责：
1. 根据用户询问推荐合适的产品（手机、电脑、耳机、智能穿戴等）
2. 提供产品的详细信息（价格、功能、规格等）
3. 解答用户关于产品的疑问
4. 协助处理售后服务问题

回复原则：
- 当用户询问产品时，重点介绍产品的核心功能和优势
- 提供准确的价格信息和产品规格
- 说明产品的适用场景和使用建议
- 如果有多个相关产品，可以进行对比推荐
- 保持友好、专业的服务态度
- 回复要简洁明了，控制在150字以内
- 如果无法解答，建议联系人工客服

请用中文回复，语气要友好自然。"""
    
    def generate_product_recommendation(self, user_query: str, products: list) -> Optional[str]:
        """
        基于用户查询和产品信息生成推荐回复
        
        Args:
            user_query: 用户查询
            products: 相关产品列表
            
        Returns:
            Optional[str]: 推荐回复
        """
        if not self.is_available() or not products:
            return None
        
        try:
            # 构建产品信息
            product_info = ""
            for i, product in enumerate(products[:3], 1):  # 最多推荐3个产品
                product_info += f"{i}. {product.get('产品名称', '')}\n"
                product_info += f"   描述：{product.get('产品描述', '')}\n"
                product_info += f"   价格：¥{product.get('价格', '')}\n\n"
            
            system_prompt = """你是一个专业的产品推荐助手。根据用户的需求和提供的产品信息，生成一个友好、专业的推荐回复。

要求：
1. 简要介绍推荐的产品
2. 突出产品的主要特点和优势
3. 语气要友好、专业
4. 回复长度控制在200字以内
5. 可以询问用户是否需要了解更多详情"""
            
            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": f"用户查询：{user_query}\n\n相关产品：\n{product_info}"}
            ]
            
            response = self.client.chat.completions.create(
                model=self.model,
                messages=messages,
                max_tokens=300,
                temperature=0.7
            )
            
            reply = response.choices[0].message.content.strip()
            logger.info("产品推荐回复生成成功")
            return reply
            
        except Exception as e:
            logger.error(f"生成产品推荐失败: {e}")
            return None
    
    def test_connection(self) -> bool:
        """测试API连接"""
        if not self.is_available():
            return False
        
        try:
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[{"role": "user", "content": "测试连接"}],
                max_tokens=10
            )
            logger.info("LLM连接测试成功")
            return True
        except Exception as e:
            logger.error(f"LLM连接测试失败: {e}")
            return False
