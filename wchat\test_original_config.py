#!/usr/bin/env python3
"""
测试原始配置页面
"""
import requests

def test_original_config():
    """测试原始配置页面"""
    try:
        session = requests.Session()
        
        # 登录
        login_data = {'password': 'admin123'}
        login_response = session.post('http://localhost:5000/login', data=login_data)
        print(f"登录状态: {login_response.status_code}")
        
        if login_response.status_code == 200:
            # 访问原始配置页面
            config_response = session.get('http://localhost:5000/config')
            print(f"原始配置页面状态: {config_response.status_code}")
            
            if config_response.status_code == 200:
                print("✅ 原始配置页面访问成功")
                if "监听模式" in config_response.text:
                    print("✅ 监听模式功能正常")
                else:
                    print("⚠️ 监听模式功能可能有问题")
            elif config_response.status_code == 500:
                print("❌ 原始配置页面返回500错误")
                print("响应内容:")
                print(config_response.text[:500])
            else:
                print(f"⚠️ 原始配置页面返回状态码: {config_response.status_code}")
        else:
            print("❌ 登录失败")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")

if __name__ == "__main__":
    test_original_config()
