#!/usr/bin/env python3
"""
测试精确匹配功能
"""
import os
import sys
from pathlib import Path

# 添加项目路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def test_exact_matcher():
    """测试精确匹配器"""
    print("🎯 测试精确匹配器")
    print("=" * 60)
    
    try:
        from src.database.exact_product_matcher import ExactProductMatcher
        
        matcher = ExactProductMatcher()
        
        # 测试产品数据
        test_products = [
            {"产品名称": "智能手机A1", "分类": "数码电子"},
            {"产品名称": "智能手表C3", "分类": "智能穿戴"},
            {"产品名称": "笔记本电脑D4", "分类": "电脑办公"},
            {"产品名称": "游戏鼠标F6", "分类": "电脑配件"},
            {"产品名称": "无线蓝牙耳机B2", "分类": "数码配件"},
        ]
        
        # 测试查询
        test_queries = [
            "手机",    # 应该只匹配智能手机A1
            "手表",    # 应该只匹配智能手表C3
            "电脑",    # 应该只匹配笔记本电脑D4
            "鼠标",    # 应该只匹配游戏鼠标F6
            "耳机",    # 应该只匹配无线蓝牙耳机B2
            "智能",    # 应该匹配智能手机和智能手表
        ]
        
        for query in test_queries:
            print(f"\n查询: '{query}'")
            matches = []
            
            for product in test_products:
                score = matcher.calculate_product_score(query, product)
                if score > 0.7:  # 精确匹配阈值
                    matches.append((product, score))
            
            matches.sort(key=lambda x: x[1], reverse=True)
            
            if matches:
                print(f"  找到 {len(matches)} 个精确匹配:")
                for product, score in matches:
                    print(f"    {product['产品名称']} (分数: {score:.3f})")
            else:
                print(f"  无精确匹配")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_exact_vs_fuzzy():
    """对比精确匹配和模糊匹配"""
    print("\n⚖️ 对比精确匹配 vs 模糊匹配")
    print("=" * 60)
    
    try:
        from src.database.enhanced_reader import EnhancedProductReader
        from config import config
        
        product_reader = EnhancedProductReader(config.database.products_file)
        
        test_queries = ["手表", "手机", "电脑", "鼠标", "耳机"]
        
        for query in test_queries:
            print(f"\n查询: '{query}'")
            
            # 精确匹配
            exact_products = product_reader.search_products_exact(query)
            print(f"  精确匹配 ({len(exact_products)} 个):")
            for product in exact_products:
                print(f"    {product['产品名称']} (分数: {product['score']:.3f})")
            
            # 模糊匹配
            fuzzy_products = product_reader.search_products(query)
            print(f"  模糊匹配 ({len(fuzzy_products)} 个):")
            for product in fuzzy_products:
                print(f"    {product['产品名称']} (分数: {product['score']:.3f})")
        
        return True
        
    except Exception as e:
        print(f"❌ 对比测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_reply_engine_with_exact_matching():
    """测试使用精确匹配的回复引擎"""
    print("\n🤖 测试精确匹配回复引擎")
    print("=" * 60)
    
    try:
        from src.bot.enhanced_reply_engine_with_images import EnhancedReplyEngineWithImages
        
        reply_engine = EnhancedReplyEngineWithImages()
        
        # 测试问题场景
        problem_scenarios = [
            {
                "query": "手表",
                "expected": ["智能手表C3"],
                "not_expected": ["智能手机A1"],
                "description": "用户问手表，不应该推荐手机"
            },
            {
                "query": "手机",
                "expected": ["智能手机A1"],
                "not_expected": ["智能手表C3"],
                "description": "用户问手机，不应该推荐手表"
            },
            {
                "query": "电脑",
                "expected": ["笔记本电脑D4"],
                "not_expected": ["游戏鼠标F6"],
                "description": "用户问电脑，不应该推荐鼠标"
            }
        ]
        
        all_passed = True
        
        for i, scenario in enumerate(problem_scenarios, 1):
            print(f"\n场景 {i}: {scenario['description']}")
            print(f"查询: '{scenario['query']}'")
            
            # 生成回复
            reply, image_paths = reply_engine.generate_reply_with_images(scenario['query'])
            
            if reply:
                print(f"回复: {reply[:100]}...")
                
                # 检查期望的产品
                expected_found = 0
                for expected in scenario['expected']:
                    if expected in reply:
                        expected_found += 1
                        print(f"  ✅ 找到期望产品: {expected}")
                    else:
                        print(f"  ❌ 未找到期望产品: {expected}")
                        all_passed = False
                
                # 检查不期望的产品
                unexpected_found = 0
                for not_expected in scenario['not_expected']:
                    if not_expected in reply:
                        unexpected_found += 1
                        print(f"  ❌ 意外出现产品: {not_expected}")
                        all_passed = False
                    else:
                        print(f"  ✅ 正确排除产品: {not_expected}")
                
                # 检查图片
                print(f"  📷 图片数量: {len(image_paths)}")
                
                # 场景结果
                if expected_found == len(scenario['expected']) and unexpected_found == 0:
                    print(f"  🎉 场景 {i} 通过")
                else:
                    print(f"  ❌ 场景 {i} 失败")
                    all_passed = False
            else:
                print(f"  ❌ 无回复")
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        print(f"❌ 回复引擎测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_edge_cases():
    """测试边缘情况"""
    print("\n🧪 测试边缘情况")
    print("=" * 60)
    
    try:
        from src.database.exact_product_matcher import ExactProductMatcher
        
        matcher = ExactProductMatcher()
        
        # 边缘情况测试
        edge_cases = [
            {
                "query": "智能",
                "products": [
                    {"产品名称": "智能手机A1", "分类": "数码电子"},
                    {"产品名称": "智能手表C3", "分类": "智能穿戴"},
                    {"产品名称": "普通手机", "分类": "数码电子"},
                ],
                "description": "查询通用词汇"
            },
            {
                "query": "A1",
                "products": [
                    {"产品名称": "智能手机A1", "分类": "数码电子"},
                    {"产品名称": "智能手表A1", "分类": "智能穿戴"},
                    {"产品名称": "笔记本电脑B1", "分类": "电脑办公"},
                ],
                "description": "查询产品型号"
            },
            {
                "query": "苹果",
                "products": [
                    {"产品名称": "苹果手机", "分类": "数码电子"},
                    {"产品名称": "新鲜苹果", "分类": "水果"},
                    {"产品名称": "苹果汁", "分类": "饮料"},
                ],
                "description": "查询歧义词汇"
            }
        ]
        
        for case in edge_cases:
            print(f"\n{case['description']}: '{case['query']}'")
            
            matches = []
            for product in case['products']:
                score = matcher.calculate_product_score(case['query'], product)
                if score > 0.7:
                    matches.append((product, score))
            
            matches.sort(key=lambda x: x[1], reverse=True)
            
            if matches:
                for product, score in matches:
                    print(f"  {product['产品名称']} (分数: {score:.3f})")
            else:
                print(f"  无匹配")
        
        return True
        
    except Exception as e:
        print(f"❌ 边缘情况测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 精确匹配功能测试")
    print()
    
    # 运行测试
    tests = [
        ("精确匹配器", test_exact_matcher),
        ("精确vs模糊", test_exact_vs_fuzzy),
        ("回复引擎", test_reply_engine_with_exact_matching),
        ("边缘情况", test_edge_cases)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n🔍 运行测试: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
            if result:
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 总结
    print(f"\n" + "=" * 60)
    print(f"          精确匹配测试总结")
    print(f"=" * 60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{status} {test_name}")
    
    print(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 精确匹配功能完美工作！")
        print("\n💡 精确匹配优势:")
        print("   1. ✅ 只匹配产品名称，避免误匹配")
        print("   2. ✅ 用户问'手表'只推荐手表产品")
        print("   3. ✅ 用户问'手机'只推荐手机产品")
        print("   4. ✅ 用户问'电脑'只推荐电脑产品")
        print("   5. ✅ 精确度高，用户体验好")
        print("\n🚀 现在产品推荐完全精确了！")
    else:
        print("⚠️ 部分功能需要优化")

if __name__ == "__main__":
    main()
