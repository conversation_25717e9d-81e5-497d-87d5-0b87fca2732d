#!/usr/bin/env python3
"""
检查微信PC端状态
"""
import sys
from pathlib import Path

# 添加项目路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def check_wechat_status():
    """检查微信状态"""
    print("=" * 60)
    print("          检查微信PC端状态")
    print("=" * 60)
    
    try:
        import wxauto
        print("✅ wxauto库导入成功")
        
        # 尝试连接微信
        print("正在连接微信...")
        wx = wxauto.WeChat()
        
        if wx:
            print("✅ 微信连接成功")
            
            # 获取当前用户信息
            try:
                current_chat = wx.CurrentChat()
                print(f"当前聊天: {current_chat}")
            except Exception as e:
                print(f"获取当前聊天失败: {e}")
            
            # 获取聊天列表
            try:
                chat_list = wx.ChatWith()
                print(f"聊天列表: {len(chat_list) if chat_list else 0} 个聊天")
            except Exception as e:
                print(f"获取聊天列表失败: {e}")
            
            return True
        else:
            print("❌ 微信连接失败")
            return False
            
    except ImportError:
        print("❌ wxauto库未安装")
        print("请运行: pip install wxauto")
        return False
    except Exception as e:
        print(f"❌ 微信连接失败: {e}")
        print("\n可能的原因:")
        print("1. 微信PC端未启动")
        print("2. 微信PC端未登录")
        print("3. 微信PC端版本不兼容")
        print("4. 微信PC端卡死或异常")
        return False

def main():
    """主函数"""
    print("检查微信PC端状态...")
    
    if check_wechat_status():
        print("\n✅ 微信状态正常，可以启动机器人")
        print("\n启动命令:")
        print("python quick_start.py")
    else:
        print("\n❌ 微信状态异常，请按以下步骤操作:")
        print("\n解决步骤:")
        print("1. 打开任务管理器 (Ctrl+Shift+Esc)")
        print("2. 结束所有微信相关进程")
        print("3. 重新启动微信PC端")
        print("4. 完成登录")
        print("5. 重新运行此检查脚本")
        
        print("\n常见问题:")
        print("• 微信版本过新：建议使用3.9.x版本")
        print("• 权限问题：以管理员身份运行")
        print("• 防火墙阻止：检查防火墙设置")

if __name__ == "__main__":
    main()
