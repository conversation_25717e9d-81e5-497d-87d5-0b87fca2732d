# 私域自动化系统 - 使用说明

## 🚀 一键启动方式

### 方式一：Python启动（推荐）

```bash
# 进入wchat目录
cd wchat

# 快速启动（会自动打开浏览器）
python 快速启动系统.py

# 或者使用完整启动
python 一键启动.py
```

### 方式二：直接启动Web服务

```bash
cd wchat
python web_config.py
```

### 方式三：使用原有启动脚本

```bash
# 双击运行
启动.bat

# 然后选择启动Web配置界面
```

## 🌐 访问系统

### 本地访问
```
http://127.0.0.1:5000
http://localhost:5000
```

### 局域网访问
```
http://*************:5000  (替换为实际IP)
```

### 远程访问
```
http://your-public-ip:5000
http://your-domain.com:5000
```

## 🔐 登录信息

### 普通用户
- **密码**: `admin123`
- **权限**: 查看状态、管理数据、安装许可证

### 管理员
- **密码**: `admin888`
- **权限**: 所有普通用户权限 + **生成许可证**

## 📋 主要功能

### 1. 系统状态监控
- 查看系统运行状态
- 监控资源使用情况
- 查看操作日志

### 2. 数据管理
- FAQ问答库管理
- 产品信息管理
- 数据导入导出

### 3. 许可证管理
- **普通用户**: 查看许可证状态、安装许可证
- **管理员**: 生成许可证、管理所有许可证

### 4. 系统配置
- 基础参数配置
- AI模型设置
- 网络访问配置

## 🛠️ 许可证管理流程

### 客户端操作（获取硬件ID）
1. 启动系统并访问Web界面
2. 登录系统（使用 admin123）
3. 点击左侧菜单"许可证管理"
4. 复制显示的硬件ID
5. 发送硬件ID给管理员

### 管理员操作（生成许可证）
1. 访问管理系统（可以是远程访问）
2. 使用管理员密码登录（admin888）
3. 点击左侧菜单"许可证生成器"
4. 输入客户端硬件ID
5. 设置有效期和功能
6. 生成许可证并发送给客户

### 客户端操作（安装许可证）
1. 在许可证管理页面
2. 粘贴管理员提供的许可证密钥
3. 点击"安装"按钮
4. 验证许可证状态

## 🌐 远程访问配置

### 快速配置
```bash
# 使用网络配置工具
python 网络配置工具.py

# 选择"配置远程访问"
# 设置监听地址为 0.0.0.0
```

### 防火墙配置
1. Windows防火墙开放5000端口
2. 路由器配置端口映射
3. 设置IP白名单（可选）

### 动态域名配置
```bash
# 使用动态域名工具
python 动态域名配置工具.py

# 支持No-IP、DynDNS等服务
```

## 🔧 常用工具

### 网络配置工具
```bash
python 网络配置工具.py
```
- 配置远程访问
- 设置安全选项
- 测试网络连接

### 动态域名工具
```bash
python 动态域名配置工具.py
```
- 配置动态域名服务
- 自动更新IP地址

### 许可证管理工具
```bash
python license_manager.py
```
- 命令行许可证管理
- 批量操作支持

## 🚨 故障排除

### 无法启动服务
```
检查项目:
✓ Python是否已安装
✓ 依赖包是否已安装
✓ 端口5000是否被占用
```

### 无法访问Web界面
```
检查项目:
✓ 服务是否正常启动
✓ 防火墙是否开放端口
✓ IP地址是否正确
```

### 许可证相关问题
```
检查项目:
✓ 是否使用正确的密码登录
✓ 硬件ID是否正确
✓ 许可证是否过期
```

## 📞 获取帮助

### 查看日志
```bash
# 日志文件位置
wchat/logs/

# 包含详细的错误信息和操作记录
```

### 测试网络
```bash
# 使用网络配置工具测试
python 网络配置工具.py
# 选择"测试网络连接"
```

### 常用命令
```bash
# 查看端口占用
netstat -an | findstr :5000

# 查看本机IP
ipconfig

# 测试连通性
ping *************
```

## 💡 使用技巧

### 1. 快速访问
- 将访问地址添加到浏览器书签
- 使用快捷方式启动系统

### 2. 安全建议
- 定期更改登录密码
- 设置IP白名单限制访问
- 启用HTTPS（生产环境）

### 3. 性能优化
- 定期清理日志文件
- 监控系统资源使用
- 使用固定IP或域名

---

**私域自动化系统**  
**简单易用 · 功能强大 · 安全可靠** 🚀🔐✨
