"""
CSV文件读取模块
简化版本，专门处理CSV格式的FAQ和产品数据
"""
import os
import csv
from typing import List, Dict, Any, Optional
from src.utils.logger import get_logger

logger = get_logger("csv_reader")


class CSVReader:
    """CSV文件读取器"""
    
    def __init__(self, file_path: str):
        self.file_path = file_path
        self.data = []
        self.columns = []
        self.last_modified = None
        
    def load_data(self, force_reload: bool = False) -> bool:
        """加载CSV数据"""
        try:
            if not os.path.exists(self.file_path):
                logger.warning(f"CSV文件不存在: {self.file_path}")
                return False
            
            # 检查文件是否有更新
            current_modified = os.path.getmtime(self.file_path)
            if not force_reload and self.last_modified == current_modified and self.data:
                return True
            
            # 读取CSV文件
            self.data = []
            with open(self.file_path, 'r', encoding='utf-8-sig') as f:
                reader = csv.DictReader(f)
                self.columns = reader.fieldnames or []
                for row in reader:
                    self.data.append(row)
            
            self.last_modified = current_modified
            logger.info(f"成功加载CSV文件: {self.file_path}, 共{len(self.data)}条记录")
            return True
            
        except Exception as e:
            logger.error(f"加载CSV文件失败 {self.file_path}: {e}")
            return False
    
    def get_data(self) -> List[Dict]:
        """获取数据"""
        if not self.data:
            self.load_data()
        return self.data
    
    def search_by_column(self, column: str, value: str, exact_match: bool = False) -> List[Dict]:
        """按列搜索数据"""
        if not self.data or column not in self.columns:
            return []
        
        try:
            results = []
            value_lower = value.lower()
            
            for row in self.data:
                cell_value = str(row.get(column, '')).lower()
                
                if exact_match:
                    if cell_value == value_lower:
                        results.append(row)
                else:
                    if value_lower in cell_value:
                        results.append(row)
            
            return results
            
        except Exception as e:
            logger.error(f"搜索数据失败: {e}")
            return []


class FAQCSVReader(CSVReader):
    """FAQ CSV读取器"""
    
    def __init__(self, file_path: str):
        super().__init__(file_path)
        self.required_columns = ['问题关键词', '标准问题', '回复内容', '分类', '状态']
    
    def validate_format(self) -> bool:
        """验证CSV格式是否正确"""
        if not self.data:
            return False
        
        missing_columns = [col for col in self.required_columns if col not in self.columns]
        if missing_columns:
            logger.error(f"FAQ文件缺少必要列: {missing_columns}")
            return False
        
        return True
    
    def search_faq(self, question: str) -> List[Dict]:
        """搜索FAQ"""
        if not self.load_data() or not self.validate_format():
            return []
        
        results = []
        question_lower = question.lower()
        
        for row in self.data:
            if row.get('状态') == '启用':
                keywords = str(row.get('问题关键词', '')).split(',')
                for keyword in keywords:
                    keyword = keyword.strip().lower()
                    if keyword and keyword in question_lower:
                        results.append(row)
                        break
        
        return results
    
    def get_categories(self) -> List[str]:
        """获取所有分类"""
        if not self.load_data() or not self.validate_format():
            return []
        
        categories = set()
        for row in self.data:
            category = row.get('分类')
            if category and category.strip():
                categories.add(category.strip())
        
        return list(categories)


class ProductCSVReader(CSVReader):
    """产品CSV读取器"""
    
    def __init__(self, file_path: str):
        super().__init__(file_path)
        self.required_columns = ['产品名称', '产品描述', '价格', '分类', '详细信息', '状态']
    
    def validate_format(self) -> bool:
        """验证CSV格式是否正确"""
        if not self.data:
            return False
        
        missing_columns = [col for col in self.required_columns if col not in self.columns]
        if missing_columns:
            logger.error(f"产品文件缺少必要列: {missing_columns}")
            return False
        
        return True
    
    def search_products(self, query: str) -> List[Dict]:
        """搜索产品"""
        if not self.load_data() or not self.validate_format():
            return []
        
        results = []
        query_lower = query.lower()
        
        for row in self.data:
            if row.get('状态') == '上架':
                product_name = str(row.get('产品名称', '')).lower()
                product_desc = str(row.get('产品描述', '')).lower()
                
                if query_lower in product_name or query_lower in product_desc:
                    results.append(row)
        
        return results
    
    def get_product_by_name(self, name: str) -> Optional[Dict]:
        """根据产品名称获取产品信息"""
        if not self.load_data() or not self.validate_format():
            return None
        
        name_lower = name.lower()
        for row in self.data:
            if row.get('状态') == '上架':
                product_name = str(row.get('产品名称', '')).lower()
                if name_lower in product_name:
                    return row
        
        return None
    
    def get_categories(self) -> List[str]:
        """获取所有产品分类"""
        if not self.load_data() or not self.validate_format():
            return []
        
        categories = set()
        for row in self.data:
            category = row.get('分类')
            if category and category.strip():
                categories.add(category.strip())
        
        return list(categories)
