#!/usr/bin/env python3
"""
测试语音文件提取功能
"""
import os
import sys
import glob
import time
from pathlib import Path

# 添加项目路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def test_voice_cache_search():
    """测试语音缓存搜索功能"""
    print("🔍 测试语音缓存搜索功能")
    print("=" * 60)
    
    try:
        from src.bot.wechat_handler import WeChatHandler
        
        handler = WeChatHandler()
        
        print("1. 测试缓存目录搜索")
        
        # 常见的微信缓存目录模式
        possible_cache_dirs = [
            os.path.expanduser("~/Documents/WeChat Files/*/FileStorage/Voice"),
            os.path.expanduser("~/AppData/Roaming/Tencent/WeChat/*/FileStorage/Voice"),
            os.path.expanduser("~/AppData/Local/Packages/TencentWeChatLimited.forWindows10_*/LocalCache/Roaming/Tencent/WeChat/*/FileStorage/Voice"),
            "C:/Users/<USER>/Documents/WeChat Files/*/FileStorage/Voice",
            "C:/Users/<USER>/AppData/Roaming/Tencent/WeChat/*/FileStorage/Voice"
        ]
        
        found_dirs = []
        for pattern in possible_cache_dirs:
            try:
                dirs = glob.glob(pattern)
                if dirs:
                    found_dirs.extend(dirs)
                    print(f"  ✅ 找到缓存目录: {pattern}")
                    for dir_path in dirs:
                        print(f"    - {dir_path}")
                else:
                    print(f"  ❌ 未找到: {pattern}")
            except Exception as e:
                print(f"  ❌ 搜索失败: {pattern} ({e})")
        
        if found_dirs:
            print(f"\n2. 检查缓存目录内容")
            for cache_dir in found_dirs[:3]:  # 只检查前3个
                if os.path.exists(cache_dir):
                    print(f"\n  检查目录: {cache_dir}")
                    
                    # 统计语音文件
                    voice_patterns = ["*.amr", "*.wav", "*.silk", "*.mp3"]
                    total_files = 0
                    
                    for pattern in voice_patterns:
                        files = glob.glob(os.path.join(cache_dir, "**", pattern), recursive=True)
                        if files:
                            print(f"    {pattern}: {len(files)} 个文件")
                            total_files += len(files)
                            
                            # 显示最新的几个文件
                            files_with_time = []
                            for file_path in files[:5]:  # 只检查前5个
                                try:
                                    mtime = os.path.getmtime(file_path)
                                    files_with_time.append((file_path, mtime))
                                except:
                                    continue
                            
                            if files_with_time:
                                files_with_time.sort(key=lambda x: x[1], reverse=True)
                                print(f"      最新文件: {os.path.basename(files_with_time[0][0])}")
                    
                    print(f"    总计: {total_files} 个语音文件")
        else:
            print(f"\n❌ 未找到任何微信缓存目录")
        
        print(f"\n3. 测试缓存查找方法")
        result = handler._try_find_voice_file_in_cache("测试用户")
        
        if result:
            print(f"  ✅ 找到语音文件: {result}")
            print(f"  文件大小: {os.path.getsize(result)} bytes")
            print(f"  修改时间: {time.ctime(os.path.getmtime(result))}")
        else:
            print(f"  ❌ 未找到最新的语音文件")
        
        return len(found_dirs) > 0
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_voice_message_object_analysis():
    """测试语音消息对象分析"""
    print("\n🎤 测试语音消息对象分析")
    print("=" * 60)
    
    try:
        from src.bot.wechat_handler import WeChatHandler
        
        handler = WeChatHandler()
        
        # 模拟不同类型的语音消息对象
        class MockVoiceMessage:
            def __init__(self, **kwargs):
                for key, value in kwargs.items():
                    setattr(self, key, value)
        
        test_cases = [
            {
                "name": "带路径属性的消息",
                "msg": MockVoiceMessage(
                    content="[语音]3秒,未播放",
                    path="C:/temp/voice_001.amr",
                    sender="用户A"
                )
            },
            {
                "name": "带文件路径属性的消息", 
                "msg": MockVoiceMessage(
                    content="[语音]2秒,未播放",
                    file_path="/tmp/voice_002.wav",
                    sender="用户B"
                )
            },
            {
                "name": "只有占位符的消息",
                "msg": MockVoiceMessage(
                    content="[语音]1秒,未播放",
                    sender="用户C"
                )
            }
        ]
        
        print("语音消息对象分析测试:")
        
        for case in test_cases:
            print(f"\n  测试: {case['name']}")
            msg = case['msg']
            
            # 测试增强的语音转文字方法
            result = handler._convert_voice_to_text_enhanced(
                msg.content, msg.sender, msg
            )
            
            print(f"    输入: {msg.content}")
            print(f"    结果: {result if result else '无结果'}")
            
            # 检查消息对象属性
            attrs = [attr for attr in dir(msg) if not attr.startswith('_')]
            print(f"    属性: {attrs}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_voice_placeholder_handling():
    """测试语音占位符处理"""
    print("\n📝 测试语音占位符处理")
    print("=" * 60)
    
    try:
        from src.bot.wechat_handler import WeChatHandler
        
        handler = WeChatHandler()
        
        test_cases = [
            "[语音]1秒,未播放",
            "[语音]3秒,未播放", 
            "[语音]10秒,未播放",
            "语音消息 2秒",
            "voice message 5s",
            "推荐一款手机",  # 非占位符
            "voice_001.amr"  # 文件名
        ]
        
        print("语音占位符检测测试:")
        
        for content in test_cases:
            is_placeholder = handler._is_voice_placeholder(content)
            status = "🎤" if is_placeholder else "📝"
            print(f"  {status} '{content}' → {'占位符' if is_placeholder else '非占位符'}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def simulate_voice_message_flow():
    """模拟完整的语音消息处理流程"""
    print("\n🔄 模拟完整的语音消息处理流程")
    print("=" * 60)
    
    try:
        from src.bot.wechat_handler import WeChatHandler
        
        handler = WeChatHandler()
        
        # 模拟语音消息
        class MockVoiceMessage:
            def __init__(self, content, sender, **kwargs):
                self.content = content
                self.sender = sender
                for key, value in kwargs.items():
                    setattr(self, key, value)
        
        scenarios = [
            {
                "name": "场景1: 有真实文件路径",
                "msg": MockVoiceMessage(
                    content="[语音]3秒,未播放",
                    sender="用户A",
                    path="test_voice.amr"
                ),
                "expected": "尝试识别真实文件"
            },
            {
                "name": "场景2: 只有占位符",
                "msg": MockVoiceMessage(
                    content="[语音]2秒,未播放", 
                    sender="用户B"
                ),
                "expected": "缓存查找或引导"
            },
            {
                "name": "场景3: 非占位符内容",
                "msg": MockVoiceMessage(
                    content="推荐一款手机",
                    sender="用户C"
                ),
                "expected": "直接处理文字"
            }
        ]
        
        print("语音消息处理流程模拟:")
        
        for scenario in scenarios:
            print(f"\n  {scenario['name']}")
            msg = scenario['msg']
            
            print(f"    输入: {msg.content}")
            print(f"    发送者: {msg.sender}")
            print(f"    期望: {scenario['expected']}")
            
            # 模拟处理流程
            print(f"    处理步骤:")
            
            # 1. 占位符检测
            is_placeholder = handler._is_voice_placeholder(msg.content)
            print(f"      1. 占位符检测: {'是' if is_placeholder else '否'}")
            
            # 2. 增强转换
            result = handler._convert_voice_to_text_enhanced(
                msg.content, msg.sender, msg
            )
            print(f"      2. 增强转换: {'成功' if result else '失败'}")
            
            if result:
                if handler._is_voice_placeholder(result):
                    print(f"      3. 结果处理: 发送引导消息")
                else:
                    print(f"      3. 结果处理: 正常回复 - {result[:20]}...")
            else:
                print(f"      3. 结果处理: 发送引导消息")
        
        return True
        
    except Exception as e:
        print(f"❌ 模拟失败: {e}")
        return False

def provide_optimization_suggestions():
    """提供优化建议"""
    print("\n💡 优化建议")
    print("=" * 60)
    
    suggestions = [
        {
            "category": "短期优化",
            "items": [
                "增加更多微信缓存目录搜索路径",
                "优化语音文件时间窗口匹配",
                "添加语音文件格式检测",
                "改进用户引导消息的个性化"
            ]
        },
        {
            "category": "中期改进", 
            "items": [
                "集成更多语音识别API（腾讯云、阿里云）",
                "添加语音质量检测和预处理",
                "实现语音文件缓存和管理",
                "支持用户自定义语音处理偏好"
            ]
        },
        {
            "category": "长期规划",
            "items": [
                "开发本地语音识别能力",
                "支持实时语音流处理",
                "集成语音合成回复功能",
                "建立语音交互的完整生态"
            ]
        }
    ]
    
    for suggestion in suggestions:
        print(f"\n{suggestion['category']}:")
        for item in suggestion['items']:
            print(f"  • {item}")
    
    print(f"\n🎯 立即行动建议:")
    print(f"  1. 重启机器人，测试新的语音处理逻辑")
    print(f"  2. 发送语音消息，观察详细的调试日志")
    print(f"  3. 根据日志信息调整缓存搜索路径")
    print(f"  4. 配置百度语音识别的完整API密钥")

def main():
    """主函数"""
    print("🎤 语音文件提取功能测试")
    print()
    
    tests = [
        ("语音缓存搜索", test_voice_cache_search),
        ("消息对象分析", test_voice_message_object_analysis),
        ("占位符处理", test_voice_placeholder_handling),
        ("完整流程模拟", simulate_voice_message_flow),
        ("优化建议", provide_optimization_suggestions)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n🔍 运行测试: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 总结
    print(f"\n" + "=" * 60)
    print(f"          语音文件提取测试总结")
    print(f"=" * 60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{status} {test_name}")
    
    print(f"\n总计: {passed}/{total} 个测试通过")
    
    print(f"\n🎯 当前状态:")
    print(f"  ✅ 语音占位符检测逻辑已完善")
    print(f"  ✅ 增强的语音转文字方法已实现")
    print(f"  ✅ 微信缓存搜索功能已添加")
    print(f"  ✅ 百度语音识别已集成")
    
    print(f"\n🚀 下一步:")
    print(f"  1. 重启机器人程序")
    print(f"  2. 发送语音消息测试")
    print(f"  3. 观察详细的调试日志")
    print(f"  4. 根据日志优化语音文件提取逻辑")

if __name__ == "__main__":
    main()
