{% extends "base.html" %}

{% block title %}FAQ管理 - 微信客服机器人{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-question-circle me-2"></i>
        FAQ管理
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <button type="button" class="btn btn-outline-primary btn-sm me-2" onclick="location.reload()">
            <i class="fas fa-sync-alt me-1"></i>刷新数据
        </button>
        <button type="button" class="btn btn-outline-info btn-sm me-2" onclick="showUploadModal()">
            <i class="fas fa-upload me-1"></i>上传数据
        </button>
        <button type="button" class="btn btn-success btn-sm" onclick="downloadTemplate()">
            <i class="fas fa-download me-1"></i>下载模板
        </button>
    </div>
</div>

<!-- 统计信息 -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-primary">{{ faq_list|length }}</h5>
                <p class="card-text">总FAQ数量</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-success">
                    {{ faq_list|selectattr('状态', 'equalto', '启用')|list|length }}
                </h5>
                <p class="card-text">启用状态</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-warning">
                    {{ faq_list|map(attribute='分类')|unique|list|length }}
                </h5>
                <p class="card-text">分类数量</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-info">Excel</h5>
                <p class="card-text">数据格式</p>
            </div>
        </div>
    </div>
</div>

<!-- 搜索和筛选 -->
<div class="card mb-4">
    <div class="card-body">
        <div class="row">
            <div class="col-md-4">
                <input type="text" class="form-control" id="searchInput" placeholder="搜索FAQ内容...">
            </div>
            <div class="col-md-3">
                <select class="form-select" id="categoryFilter">
                    <option value="">所有分类</option>
                    {% for category in faq_list|map(attribute='分类')|unique %}
                        <option value="{{ category }}">{{ category }}</option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-3">
                <select class="form-select" id="statusFilter">
                    <option value="">所有状态</option>
                    <option value="启用">启用</option>
                    <option value="禁用">禁用</option>
                </select>
            </div>
            <div class="col-md-2">
                <button class="btn btn-primary w-100" onclick="filterFAQ()">
                    <i class="fas fa-search me-1"></i>筛选
                </button>
            </div>
        </div>
    </div>
</div>

<!-- FAQ列表 -->
<div class="card">
    <div class="card-header">
        <h6 class="mb-0">FAQ列表</h6>
    </div>
    <div class="card-body">
        {% if faq_list %}
            <div class="table-responsive">
                <table class="table table-hover" id="faqTable">
                    <thead>
                        <tr>
                            <th>序号</th>
                            <th>问题关键词</th>
                            <th>标准问题</th>
                            <th>回复内容</th>
                            <th>分类</th>
                            <th>状态</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for faq in faq_list %}
                        <tr data-category="{{ faq.分类 }}" data-status="{{ faq.状态 }}">
                            <td>{{ loop.index }}</td>
                            <td>
                                <span class="badge bg-secondary">{{ faq.问题关键词 }}</span>
                            </td>
                            <td>
                                <strong>{{ faq.标准问题 }}</strong>
                            </td>
                            <td>
                                <div class="text-truncate" style="max-width: 300px;" 
                                     title="{{ faq.回复内容 }}">
                                    {{ faq.回复内容 }}
                                </div>
                            </td>
                            <td>
                                <span class="badge bg-info">{{ faq.分类 }}</span>
                            </td>
                            <td>
                                {% if faq.状态 == '启用' %}
                                    <span class="badge bg-success">启用</span>
                                {% else %}
                                    <span class="badge bg-secondary">禁用</span>
                                {% endif %}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        {% else %}
            <div class="text-center py-5">
                <i class="fas fa-question-circle fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">暂无FAQ数据</h5>
                <p class="text-muted">请确保FAQ文件存在并格式正确</p>
                <button class="btn btn-primary" onclick="downloadTemplate()">
                    <i class="fas fa-download me-1"></i>下载Excel模板
                </button>
            </div>
        {% endif %}
    </div>
</div>

<!-- 使用说明 -->
<div class="card mt-4">
    <div class="card-header">
        <h6 class="mb-0">
            <i class="fas fa-info-circle me-2"></i>
            使用说明
        </h6>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <h6>Excel文件格式要求：</h6>
                <ul>
                    <li>文件名：faq.xlsx</li>
                    <li>必需列：问题关键词、标准问题、回复内容、分类、状态</li>
                    <li>问题关键词：用逗号分隔多个关键词</li>
                    <li>状态：只能是"启用"或"禁用"</li>
                </ul>
            </div>
            <div class="col-md-6">
                <h6>匹配规则：</h6>
                <ul>
                    <li>优先匹配问题关键词</li>
                    <li>然后匹配标准问题</li>
                    <li>只有状态为"启用"的FAQ才会被使用</li>
                    <li>支持模糊匹配和相似度计算</li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function filterFAQ() {
    const searchText = $('#searchInput').val().toLowerCase();
    const categoryFilter = $('#categoryFilter').val();
    const statusFilter = $('#statusFilter').val();
    
    $('#faqTable tbody tr').each(function() {
        const row = $(this);
        const text = row.text().toLowerCase();
        const category = row.data('category');
        const status = row.data('status');
        
        let show = true;
        
        // 文本搜索
        if (searchText && !text.includes(searchText)) {
            show = false;
        }
        
        // 分类筛选
        if (categoryFilter && category !== categoryFilter) {
            show = false;
        }
        
        // 状态筛选
        if (statusFilter && status !== statusFilter) {
            show = false;
        }
        
        row.toggle(show);
    });
}

function downloadTemplate() {
    // 创建示例数据
    const templateData = [
        ['问题关键词', '标准问题', '回复内容', '分类', '状态'],
        ['退货,退款', '如何申请退货？', '您可以在订单页面申请退货...', '售后服务', '启用'],
        ['发货,物流', '什么时候发货？', '我们会在24小时内发货...', '物流配送', '启用'],
        ['价格,优惠', '有优惠活动吗？', '请关注我们的优惠活动...', '促销活动', '启用']
    ];
    
    // 转换为CSV格式
    const csvContent = templateData.map(row => row.join(',')).join('\n');
    
    // 创建下载链接
    const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', 'faq_template.csv');
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    showAlert('FAQ模板下载成功，请用Excel打开并保存为.xlsx格式', 'success');
}

function showAlert(message, type) {
    const alertHtml = `
        <div class="alert alert-${type} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    $('.main-content .pt-3').prepend(alertHtml);
}

// 实时搜索
$('#searchInput').on('input', function() {
    filterFAQ();
});

$('#categoryFilter, #statusFilter').on('change', function() {
    filterFAQ();
});

// 上传功能
function showUploadModal() {
    $('#uploadModal').modal('show');
}

function downloadTemplate() {
    window.location.href = '/api/download_template/faq';
}

function uploadFAQ() {
    const fileInput = document.getElementById('faqFile');
    const replaceData = document.getElementById('replaceData').checked;

    if (!fileInput.files.length) {
        alert('请选择文件');
        return;
    }

    const formData = new FormData();
    formData.append('file', fileInput.files[0]);
    formData.append('replace', replaceData);

    // 显示上传进度
    $('#uploadProgress').show();
    $('#uploadBtn').prop('disabled', true);

    $.ajax({
        url: '/api/upload_faq',
        type: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        success: function(response) {
            alert('FAQ数据上传成功！');
            $('#uploadModal').modal('hide');
            location.reload();
        },
        error: function(xhr) {
            const error = xhr.responseJSON ? xhr.responseJSON.error : '上传失败';
            alert('上传失败：' + error);
        },
        complete: function() {
            $('#uploadProgress').hide();
            $('#uploadBtn').prop('disabled', false);
        }
    });
}
</script>

<!-- 上传模态框 -->
<div class="modal fade" id="uploadModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-upload me-2"></i>上传FAQ数据
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label for="faqFile" class="form-label">选择文件</label>
                    <input type="file" class="form-control" id="faqFile" accept=".xlsx,.xls,.csv">
                    <div class="form-text">支持Excel (.xlsx, .xls) 和CSV (.csv) 格式</div>
                </div>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="replaceData">
                    <label class="form-check-label" for="replaceData">
                        替换现有数据（不勾选则合并数据）
                    </label>
                </div>
                <div id="uploadProgress" class="mt-3" style="display: none;">
                    <div class="progress">
                        <div class="progress-bar progress-bar-striped progress-bar-animated"
                             style="width: 100%">上传中...</div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="uploadBtn" onclick="uploadFAQ()">
                    <i class="fas fa-upload me-1"></i>上传
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}
