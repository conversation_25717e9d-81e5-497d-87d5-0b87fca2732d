#!/usr/bin/env python3
"""
产品图片处理器
支持产品图片的发送和管理
"""
import os
import re
from typing import List, Optional, Tuple, Dict
from pathlib import Path

class ProductImageHandler:
    """产品图片处理器"""
    
    def __init__(self, images_dir: str = "data/images"):
        self.images_dir = images_dir
        
        # 产品名称到图片文件的映射规则
        self.name_mapping = {
            "智能手机A1": "phone_a1.jpg",
            "无线蓝牙耳机B2": "earphone_b2.jpg", 
            "智能手表C3": "watch_c3.jpg",
            "笔记本电脑D4": "laptop_d4.jpg",
            "无线充电器E5": "charger_e5.jpg",
            "游戏鼠标F6": "mouse_f6.jpg"
        }
    
    def get_product_image_path(self, product_name: str) -> Optional[str]:
        """
        根据产品名称获取对应的图片路径
        
        Args:
            product_name: 产品名称，如"智能手机A1"
            
        Returns:
            str: 图片文件路径，如果找不到则返回None
        """
        if not os.path.exists(self.images_dir):
            return None
        
        # 查找精确匹配的图片文件
        image_filename = self.name_mapping.get(product_name)
        if image_filename:
            image_path = os.path.join(self.images_dir, image_filename)
            if os.path.exists(image_path):
                return image_path
        
        # 如果没有精确匹配，尝试模糊匹配
        if product_name:
            # 从产品名称中提取代码（如A1, B2等）
            code_match = re.search(r'[A-Z]\d+', product_name)
            if code_match:
                code = code_match.group().lower()
                for filename in os.listdir(self.images_dir):
                    if filename.endswith(('.jpg', '.jpeg', '.png', '.gif')):
                        if code in filename.lower():
                            return os.path.join(self.images_dir, filename)
        
        return None
    
    def get_products_images(self, products: List[Dict]) -> List[str]:
        """
        获取多个产品的图片路径列表
        
        Args:
            products: 产品列表
            
        Returns:
            List[str]: 图片路径列表
        """
        image_paths = []
        
        for product in products:
            product_name = product.get('产品名称', '')
            if product_name:
                image_path = self.get_product_image_path(product_name)
                if image_path:
                    image_paths.append(image_path)
        
        return image_paths
    
    def format_product_reply_with_images(self, products: List[Dict]) -> Tuple[str, List[str]]:
        """
        格式化产品回复，包含图片信息
        
        Args:
            products: 产品列表
            
        Returns:
            tuple: (文本回复, 图片路径列表)
        """
        if not products:
            return "抱歉，没有找到相关产品。您可以描述更具体的需求，比如想要什么类型的产品、价格范围等，我会为您推荐合适的产品。", []
        
        # 根据产品数量调整开场白
        if len(products) == 1:
            reply = f"为您推荐这款产品：\n\n"
        else:
            reply = f"为您推荐 {len(products)} 款相关产品：\n\n"
        
        image_paths = []
        
        for i, product in enumerate(products, 1):
            name = product.get('产品名称', '')
            desc = product.get('产品描述', '')
            price = product.get('价格', '')
            details = product.get('详细信息', '')
            
            reply += f"🛍️ {i}. {name}\n"
            reply += f"💰 价格：¥{price}\n"
            reply += f"📝 描述：{desc}\n"
            
            if details:
                # 格式化详细信息，去掉多余的换行
                formatted_details = details.replace('\n\n', '\n').strip()
                reply += f"ℹ️ 详情：{formatted_details}\n"
            
            # 查找对应的图片
            image_path = self.get_product_image_path(name)
            if image_path:
                image_paths.append(image_path)
            
            reply += "\n"
        
        # 根据产品数量调整结尾，使用更有感情但不急于推销的表达
        if len(products) == 1:
            reply += "这款怎么样？"
        else:
            reply += "看看你喜欢哪款～"
        
        return reply, image_paths
    
    def validate_image_files(self) -> Dict[str, bool]:
        """
        验证所有产品图片文件是否存在
        
        Returns:
            Dict[str, bool]: 产品名称到图片存在状态的映射
        """
        validation_result = {}
        
        for product_name, image_filename in self.name_mapping.items():
            image_path = os.path.join(self.images_dir, image_filename)
            validation_result[product_name] = os.path.exists(image_path)
        
        return validation_result
    
    def get_missing_images(self) -> List[str]:
        """
        获取缺失的图片文件列表
        
        Returns:
            List[str]: 缺失的图片文件名列表
        """
        missing_images = []
        
        for product_name, image_filename in self.name_mapping.items():
            image_path = os.path.join(self.images_dir, image_filename)
            if not os.path.exists(image_path):
                missing_images.append(image_filename)
        
        return missing_images
    
    def get_image_info(self) -> Dict[str, Dict]:
        """
        获取所有图片的详细信息
        
        Returns:
            Dict[str, Dict]: 图片信息字典
        """
        image_info = {}
        
        for product_name, image_filename in self.name_mapping.items():
            image_path = os.path.join(self.images_dir, image_filename)
            
            info = {
                'filename': image_filename,
                'path': image_path,
                'exists': os.path.exists(image_path),
                'size': 0
            }
            
            if info['exists']:
                try:
                    info['size'] = os.path.getsize(image_path)
                except OSError:
                    info['size'] = 0
            
            image_info[product_name] = info
        
        return image_info
