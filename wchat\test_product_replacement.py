#!/usr/bin/env python3
"""
测试产品库替换后的图片功能兼容性
"""
import os
import sys
import pandas as pd
import shutil
from pathlib import Path

# 添加项目路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def create_test_product_data():
    """创建测试用的新产品数据"""
    
    # 场景1: 完全不同的产品
    new_products_1 = [
        {
            "产品名称": "智能音箱X1",
            "产品描述": "AI语音助手，智能家居控制",
            "价格": "299",
            "分类": "智能家居",
            "详细信息": "语音识别：支持多种方言\n连接方式：WiFi+蓝牙\n音质：360度环绕音效",
            "状态": "上架"
        },
        {
            "产品名称": "无线键盘Y2",
            "产品描述": "机械键盘，RGB背光，无线连接",
            "价格": "399",
            "分类": "电脑配件",
            "详细信息": "轴体：青轴\n连接：2.4G无线\n续航：30天\n背光：RGB可调",
            "状态": "上架"
        }
    ]
    
    # 场景2: 部分产品名称变化
    new_products_2 = [
        {
            "产品名称": "智能手机A1 Pro",  # 原来是"智能手机A1"
            "产品描述": "升级版智能手机，更强性能",
            "价格": "3299",
            "分类": "数码电子",
            "详细信息": "处理器：骁龙8 Gen2\n内存：12GB\n存储：256GB",
            "状态": "上架"
        },
        {
            "产品名称": "蓝牙耳机B2",  # 原来是"无线蓝牙耳机B2"
            "产品描述": "降噪蓝牙耳机，长续航",
            "价格": "499",
            "分类": "数码配件",
            "详细信息": "续航：40小时\n降噪：ANC主动降噪",
            "状态": "上架"
        }
    ]
    
    # 场景3: 保持相同的产品代码但内容更新
    new_products_3 = [
        {
            "产品名称": "智能手机A1",  # 名称相同
            "产品描述": "全新升级，性能更强",  # 描述更新
            "价格": "2799",  # 价格更新
            "分类": "数码电子",
            "详细信息": "处理器：骁龙888 Plus\n内存：8GB\n存储：256GB",  # 详情更新
            "状态": "上架"
        },
        {
            "产品名称": "游戏鼠标F6",
            "产品描述": "专业电竞鼠标，高精度传感器",
            "价格": "249",
            "分类": "电脑配件", 
            "详细信息": "DPI：16000\n按键：8个可编程\nRGB：1680万色",
            "状态": "上架"
        }
    ]
    
    return {
        "完全新产品": new_products_1,
        "部分名称变化": new_products_2,
        "内容更新": new_products_3
    }

def backup_current_data():
    """备份当前数据"""
    try:
        # 备份产品文件
        if os.path.exists("data/products.xlsx"):
            shutil.copy2("data/products.xlsx", "data/products_backup.xlsx")
            print("✅ 已备份当前产品数据")
        
        return True
    except Exception as e:
        print(f"❌ 备份失败: {e}")
        return False

def restore_data():
    """恢复原始数据"""
    try:
        if os.path.exists("data/products_backup.xlsx"):
            shutil.copy2("data/products_backup.xlsx", "data/products.xlsx")
            os.remove("data/products_backup.xlsx")
            print("✅ 已恢复原始产品数据")
        return True
    except Exception as e:
        print(f"❌ 恢复失败: {e}")
        return False

def test_product_replacement_scenario(scenario_name, new_products):
    """测试产品替换场景"""
    print(f"\n🔍 测试场景: {scenario_name}")
    print("=" * 50)
    
    try:
        # 创建新的产品数据文件
        df = pd.DataFrame(new_products)
        test_file = "data/products_test.xlsx"
        df.to_excel(test_file, index=False)
        
        print(f"📝 创建测试产品数据: {len(new_products)} 个产品")
        for product in new_products:
            print(f"   - {product['产品名称']}")
        
        # 测试图片匹配
        from src.bot.product_image_handler import ProductImageHandler
        image_handler = ProductImageHandler()
        
        print(f"\n📷 图片匹配测试:")
        matched_count = 0
        for product in new_products:
            product_name = product['产品名称']
            image_path = image_handler.get_product_image_path(product_name)
            
            if image_path and os.path.exists(image_path):
                print(f"   ✅ {product_name} → {os.path.basename(image_path)}")
                matched_count += 1
            else:
                print(f"   ❌ {product_name} → 无匹配图片")
        
        print(f"\n📊 匹配结果: {matched_count}/{len(new_products)} 个产品有图片")
        
        # 测试产品搜索和回复
        from src.database.enhanced_reader import EnhancedProductReader
        
        # 临时替换产品文件进行测试
        original_file = "data/products.xlsx"
        if os.path.exists(original_file):
            shutil.copy2(original_file, "data/products_temp_backup.xlsx")
        shutil.copy2(test_file, original_file)
        
        try:
            product_reader = EnhancedProductReader(original_file)
            print(f"\n🔍 产品搜索测试:")
            
            # 测试搜索
            test_queries = ["手机", "耳机", "鼠标", "键盘", "音箱"]
            for query in test_queries:
                products = product_reader.search_products(query)
                if products:
                    print(f"   '{query}' → 找到 {len(products)} 个产品")
                    
                    # 测试回复生成
                    reply, image_paths = image_handler.format_product_reply_with_images(products[:1])
                    print(f"     回复长度: {len(reply)} 字符")
                    print(f"     图片数量: {len(image_paths)} 张")
                else:
                    print(f"   '{query}' → 无匹配产品")
        
        finally:
            # 恢复原始文件
            if os.path.exists("data/products_temp_backup.xlsx"):
                shutil.copy2("data/products_temp_backup.xlsx", original_file)
                os.remove("data/products_temp_backup.xlsx")
        
        # 清理测试文件
        if os.path.exists(test_file):
            os.remove(test_file)
        
        return matched_count, len(new_products)
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return 0, len(new_products)

def test_image_mapping_flexibility():
    """测试图片映射的灵活性"""
    print("\n🔧 测试图片映射灵活性")
    print("=" * 50)
    
    try:
        from src.bot.product_image_handler import ProductImageHandler
        image_handler = ProductImageHandler()
        
        # 测试各种产品名称变体
        test_names = [
            "智能手机A1",           # 原始名称
            "智能手机A1 Pro",       # 添加后缀
            "智能手机 A1",          # 添加空格
            "手机A1",               # 简化名称
            "A1智能手机",           # 调换顺序
            "无线蓝牙耳机B2",       # 原始名称
            "蓝牙耳机B2",           # 简化名称
            "耳机B2",               # 进一步简化
            "B2耳机",               # 调换顺序
            "笔记本电脑D4",         # 原始名称
            "笔记本D4",             # 简化名称
            "电脑D4",               # 进一步简化
        ]
        
        print("产品名称变体匹配测试:")
        successful_matches = 0
        
        for name in test_names:
            image_path = image_handler.get_product_image_path(name)
            if image_path and os.path.exists(image_path):
                filename = os.path.basename(image_path)
                print(f"   ✅ '{name}' → {filename}")
                successful_matches += 1
            else:
                print(f"   ❌ '{name}' → 无匹配")
        
        print(f"\n📊 灵活性测试结果: {successful_matches}/{len(test_names)} 个变体成功匹配")
        
        flexibility_score = (successful_matches / len(test_names)) * 100
        print(f"🎯 灵活性评分: {flexibility_score:.1f}%")
        
        return flexibility_score
        
    except Exception as e:
        print(f"❌ 灵活性测试失败: {e}")
        return 0

def suggest_improvements():
    """建议改进方案"""
    print("\n💡 产品库替换兼容性改进建议")
    print("=" * 50)
    
    suggestions = [
        {
            "问题": "新产品名称与现有图片不匹配",
            "解决方案": [
                "1. 在产品数据中添加'图片文件'字段，明确指定图片文件名",
                "2. 创建产品名称到图片的映射配置文件",
                "3. 支持多个产品共享同一张图片"
            ]
        },
        {
            "问题": "产品代码规则变化",
            "解决方案": [
                "1. 支持多种代码格式（A1, A-1, A_1等）",
                "2. 添加产品分类到图片的映射",
                "3. 实现智能的产品名称解析"
            ]
        },
        {
            "问题": "图片文件管理",
            "解决方案": [
                "1. 建立标准的图片命名规范",
                "2. 支持图片文件的自动重命名",
                "3. 提供图片上传和管理界面"
            ]
        }
    ]
    
    for i, suggestion in enumerate(suggestions, 1):
        print(f"{i}. {suggestion['问题']}")
        for solution in suggestion['解决方案']:
            print(f"   {solution}")
        print()

def main():
    """主测试函数"""
    print("🔄 产品库替换兼容性测试")
    print("=" * 60)
    
    # 备份当前数据
    if not backup_current_data():
        print("❌ 无法备份数据，测试终止")
        return
    
    try:
        # 创建测试数据
        test_scenarios = create_test_product_data()
        
        # 测试各种替换场景
        results = []
        for scenario_name, new_products in test_scenarios.items():
            matched, total = test_product_replacement_scenario(scenario_name, new_products)
            results.append((scenario_name, matched, total))
        
        # 测试图片映射灵活性
        flexibility_score = test_image_mapping_flexibility()
        
        # 总结结果
        print("\n📊 测试结果总结")
        print("=" * 50)
        
        total_matched = 0
        total_products = 0
        
        for scenario, matched, total in results:
            match_rate = (matched / total * 100) if total > 0 else 0
            print(f"{scenario}: {matched}/{total} ({match_rate:.1f}%)")
            total_matched += matched
            total_products += total
        
        overall_rate = (total_matched / total_products * 100) if total_products > 0 else 0
        print(f"\n总体匹配率: {total_matched}/{total_products} ({overall_rate:.1f}%)")
        print(f"图片映射灵活性: {flexibility_score:.1f}%")
        
        # 评估兼容性
        if overall_rate >= 80 and flexibility_score >= 60:
            print(f"\n✅ 兼容性评估: 优秀")
            print("产品库替换后图片功能基本正常")
        elif overall_rate >= 60 and flexibility_score >= 40:
            print(f"\n⚠️ 兼容性评估: 良好")
            print("产品库替换后部分图片功能正常，建议优化")
        else:
            print(f"\n❌ 兼容性评估: 需要改进")
            print("产品库替换后图片功能受影响较大")
        
        # 提供改进建议
        suggest_improvements()
        
    finally:
        # 恢复原始数据
        restore_data()

if __name__ == "__main__":
    main()
