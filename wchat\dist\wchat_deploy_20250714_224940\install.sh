#!/bin/bash
echo "========================================"
echo "  WChat微信客服机器人 - 自动安装"
echo "========================================"
echo

echo "正在检查Python环境..."
if ! command -v python3 &> /dev/null; then
    echo "[ERROR] 未检测到Python3环境"
    echo "请先安装Python 3.8或更高版本"
    exit 1
fi

echo "[OK] Python环境检测成功"

echo
echo "正在安装依赖包..."
python3 install_deps.py

echo
echo "========================================"
echo "  安装完成！"
echo "========================================"
echo
echo "下一步操作:"
echo "1. 配置API密钥 (编辑 config/config.json)"
echo "2. 运行: python3 quick_start.py"
echo
