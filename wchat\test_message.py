"""
消息解析测试脚本
测试wxauto消息对象的属性和解析
"""
import os
import sys
import time

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def analyze_message_object(msg):
    """分析消息对象的属性"""
    print(f"\n消息对象分析:")
    print(f"  类型: {type(msg)}")
    print(f"  类名: {type(msg).__name__}")
    
    # 列出所有属性
    attrs = [attr for attr in dir(msg) if not attr.startswith('_')]
    print(f"  可用属性: {attrs}")
    
    # 检查常见属性
    common_attrs = ['sender', 'content', 'msg', 'text', 'who', 'time', 'type']
    for attr in common_attrs:
        if hasattr(msg, attr):
            value = getattr(msg, attr)
            print(f"  {attr}: {value} (类型: {type(value)})")
    
    return msg


def test_message_parsing():
    """测试消息解析"""
    print("=" * 50)
    print("测试消息解析")
    print("=" * 50)
    
    try:
        from wxauto import WeChat
        
        wx = WeChat()
        print(f"微信连接成功，用户: {wx.nickname}")
        
        print("\n开始监听消息（30秒）...")
        print("请在微信中发送一些测试消息")
        
        start_time = time.time()
        message_count = 0
        
        while time.time() - start_time < 30:
            try:
                msgs = wx.GetNewMessage()
                if msgs:
                    for msg in msgs:
                        message_count += 1
                        print(f"\n--- 消息 {message_count} ---")
                        analyze_message_object(msg)
                        
                        # 测试我们的解析逻辑
                        sender, content, msg_type = parse_message(msg)
                        print(f"\n解析结果:")
                        print(f"  发送者: {sender}")
                        print(f"  内容: {content}")
                        print(f"  类型: {msg_type}")
                        
                time.sleep(1)
                
            except Exception as e:
                print(f"获取消息失败: {e}")
                time.sleep(2)
        
        print(f"\n监听结束，共处理 {message_count} 条消息")
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()


def parse_message(msg):
    """解析消息对象（模拟我们的解析逻辑）"""
    sender = ""
    content = ""
    msg_type = "unknown"
    
    # 检查消息对象的属性
    if hasattr(msg, 'sender'):
        sender = str(msg.sender) if msg.sender else ""
    elif hasattr(msg, 'who'):
        sender = str(msg.who) if msg.who else ""
    
    if hasattr(msg, 'content'):
        content = str(msg.content) if msg.content else ""
    elif hasattr(msg, 'msg'):
        content = str(msg.msg) if msg.msg else ""
    elif hasattr(msg, 'text'):
        content = str(msg.text) if msg.text else ""
    
    # 判断消息类型
    msg_class_name = type(msg).__name__
    if 'Text' in msg_class_name:
        msg_type = "text"
    elif 'Image' in msg_class_name:
        msg_type = "image"
    elif 'Voice' in msg_class_name:
        msg_type = "voice"
    elif 'Self' in msg_class_name:
        msg_type = "self"
    else:
        msg_type = "other"
    
    return sender, content, msg_type


def test_specific_message_types():
    """测试特定消息类型"""
    print("\n" + "=" * 50)
    print("测试特定消息类型")
    print("=" * 50)
    
    try:
        from wxauto import WeChat
        
        wx = WeChat()
        
        # 发送测试消息到文件传输助手
        if wx.ChatWith("文件传输助手"):
            test_msg = f"测试消息 - {time.strftime('%H:%M:%S')}"
            wx.SendMsg(test_msg)
            print(f"已发送测试消息: {test_msg}")
            
            # 等待并获取消息
            time.sleep(2)
            msgs = wx.GetNewMessage()
            
            if msgs:
                print(f"获取到 {len(msgs)} 条新消息")
                for i, msg in enumerate(msgs):
                    print(f"\n消息 {i+1}:")
                    analyze_message_object(msg)
            else:
                print("未获取到新消息")
        else:
            print("无法切换到文件传输助手")
            
    except Exception as e:
        print(f"测试失败: {e}")


def main():
    """主函数"""
    print("微信消息解析测试")
    print("=" * 50)
    
    choice = input("选择测试模式:\n1. 实时消息监听\n2. 发送测试消息\n请输入 (1/2): ").strip()
    
    if choice == "1":
        test_message_parsing()
    elif choice == "2":
        test_specific_message_types()
    else:
        print("无效选择")


if __name__ == "__main__":
    main()
