"""
回复引擎
实现智能回复逻辑：优先使用FAQ库和产品库，无匹配时使用AI
"""
import os
import sys
from typing import Optional, Dict, Any, List, Tuple

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(current_dir))
sys.path.insert(0, project_root)

from src.database.excel_reader import FAQReader, ProductReader
from src.database.matcher import ContentMatcher
from src.ai.llm_service import LLMService
from src.utils.logger import get_logger
from config import config

logger = get_logger("reply_engine")


class ReplyEngine:
    """回复引擎类"""
    
    def __init__(self):
        # 初始化数据库读取器
        # 使用绝对路径
        faq_path = os.path.join(project_root, config.database.faq_file)
        products_path = os.path.join(project_root, config.database.products_file)

        self.faq_reader = FAQReader(faq_path)
        self.product_reader = ProductReader(products_path)
        
        # 初始化内容匹配器
        self.matcher = ContentMatcher(config.database.similarity_threshold)
        
        # 初始化AI服务
        self.llm_service = None
        if config.ai.enabled and config.ai.api_key:
            self.llm_service = LLMService(
                api_key=config.ai.api_key,
                base_url=config.ai.base_url,
                model=config.ai.model,
                max_tokens=config.ai.max_tokens,
                temperature=config.ai.temperature
            )
        
        logger.info("回复引擎初始化完成")
    
    def generate_reply(self, user_message: str, sender_name: str = "") -> str:
        """
        生成回复
        
        Args:
            user_message: 用户消息
            sender_name: 发送者名称
            
        Returns:
            str: 回复内容
        """
        logger.info(f"处理消息: {user_message[:50]}...")
        
        # 1. 优先尝试FAQ匹配
        if config.reply.priority_faq:
            faq_reply = self._try_faq_match(user_message)
            if faq_reply:
                logger.info("使用FAQ回复")
                return self._format_reply(faq_reply, sender_name)
        
        # 2. 尝试产品库匹配
        if config.reply.priority_products:
            product_reply = self._try_product_match(user_message)
            if product_reply:
                logger.info("使用产品库回复")
                return self._format_reply(product_reply, sender_name)
        
        # 3. 使用AI生成回复
        if config.reply.use_ai_fallback and self.llm_service and self.llm_service.is_available():
            ai_reply = self._try_ai_reply(user_message)
            if ai_reply:
                logger.info("使用AI回复")
                return self._format_reply(ai_reply, sender_name)
        
        # 4. 返回默认回复
        logger.info("使用默认回复")
        return self._format_reply(config.reply.default_reply, sender_name)
    
    def _try_faq_match(self, user_message: str) -> Optional[str]:
        """尝试FAQ匹配"""
        try:
            # 搜索FAQ
            faq_list = self.faq_reader.search_faq(user_message)
            if not faq_list:
                return None
            
            # 使用匹配器计算相似度
            matches = self.matcher.match_faq(user_message, faq_list)
            if matches:
                best_match = matches[0][0]  # 获取最佳匹配
                reply_content = best_match.get('回复内容', '')
                logger.debug(f"FAQ匹配成功: {best_match.get('标准问题', '')}")
                return reply_content
            
        except Exception as e:
            logger.error(f"FAQ匹配失败: {e}")
        
        return None
    
    def _try_product_match(self, user_message: str) -> Optional[str]:
        """尝试产品库匹配"""
        try:
            # 搜索产品
            product_list = self.product_reader.search_products(user_message)
            if not product_list:
                return None
            
            # 使用匹配器计算相似度
            matches = self.matcher.match_products(user_message, product_list)
            if matches:
                # 获取前3个最佳匹配
                top_matches = [match[0] for match in matches[:3]]
                
                # 如果有AI服务，生成推荐回复
                if self.llm_service and self.llm_service.is_available():
                    ai_reply = self.llm_service.generate_product_recommendation(user_message, top_matches)
                    if ai_reply:
                        return ai_reply
                
                # 否则生成简单的产品介绍
                return self._format_product_reply(top_matches)
            
        except Exception as e:
            logger.error(f"产品匹配失败: {e}")
        
        return None
    
    def _try_ai_reply(self, user_message: str) -> Optional[str]:
        """尝试AI回复"""
        try:
            if not self.llm_service or not self.llm_service.is_available():
                return None
            
            # 生成AI回复
            ai_reply = self.llm_service.generate_reply(user_message)
            return ai_reply
            
        except Exception as e:
            logger.error(f"AI回复失败: {e}")
        
        return None
    
    def _format_product_reply(self, products: List[Dict]) -> str:
        """格式化产品回复"""
        if not products:
            return ""
        
        reply = "为您推荐以下产品：\n\n"
        
        for i, product in enumerate(products, 1):
            name = product.get('产品名称', '')
            desc = product.get('产品描述', '')
            price = product.get('价格', '')
            
            reply += f"{i}. {name}\n"
            reply += f"   {desc}\n"
            reply += f"   价格：¥{price}\n\n"
        
        reply += "如需了解更多详情，请告诉我您感兴趣的产品名称。"
        return reply
    
    def _format_reply(self, reply: str, sender_name: str = "") -> str:
        """格式化回复内容"""
        if not reply:
            return config.reply.default_reply
        
        # 如果是群聊，添加@用户名
        if sender_name and sender_name != "":
            # 检查回复是否已经包含@用户名
            if not reply.startswith(f"@{sender_name}"):
                reply = f"@{sender_name} {reply}"
        
        return reply
    
    def reload_data(self):
        """重新加载数据"""
        try:
            self.faq_reader.load_data(force_reload=True)
            self.product_reader.load_data(force_reload=True)
            logger.info("数据重新加载完成")
        except Exception as e:
            logger.error(f"数据重新加载失败: {e}")
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        try:
            faq_data = self.faq_reader.get_data()
            product_data = self.product_reader.get_data()

            # 安全地获取数据长度
            faq_count = 0
            if faq_data is not None:
                try:
                    faq_count = len(faq_data)
                except:
                    faq_count = 0

            product_count = 0
            if product_data is not None:
                try:
                    product_count = len(product_data)
                except:
                    product_count = 0

            stats = {
                'faq_count': faq_count,
                'product_count': product_count,
                'ai_available': self.llm_service.is_available() if self.llm_service else False,
                'faq_categories': self.faq_reader.get_categories(),
                'product_categories': self.product_reader.get_categories()
            }

            return stats

        except Exception as e:
            logger.error(f"获取统计信息失败: {e}")
            return {
                'faq_count': 0,
                'product_count': 0,
                'ai_available': False,
                'faq_categories': [],
                'product_categories': []
            }
