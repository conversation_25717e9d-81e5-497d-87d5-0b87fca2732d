#!/usr/bin/env python3
"""
快速测试百度语音识别
"""
import os
import sys
from pathlib import Path

# 添加项目路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def test_baidu_import():
    """测试百度SDK导入"""
    print("🔧 测试百度SDK导入")
    print("=" * 40)
    
    try:
        from aip import AipSpeech
        print("✅ 百度SDK导入成功")
        return True
    except ImportError as e:
        print(f"❌ 百度SDK导入失败: {e}")
        return False

def test_baidu_service_creation():
    """测试百度语音服务创建"""
    print("\n🎤 测试百度语音服务创建")
    print("=" * 40)
    
    try:
        from config import config
        from src.ai.baidu_voice_service import get_baidu_voice_service
        
        print("配置信息:")
        print(f"  enabled: {config.baidu_voice.enabled}")
        print(f"  app_id: {config.baidu_voice.app_id}")
        print(f"  api_key: {config.baidu_voice.api_key[:10]}..." if config.baidu_voice.api_key else "  api_key: (空)")
        print(f"  secret_key: {config.baidu_voice.secret_key[:10]}..." if config.baidu_voice.secret_key else "  secret_key: (空)")
        
        service = get_baidu_voice_service(config)
        
        if service:
            print("✅ 百度语音服务创建成功")
            return True
        else:
            print("❌ 百度语音服务创建失败")
            print("   可能原因: 配置不完整（需要App ID和Secret Key）")
            return False
            
    except Exception as e:
        print(f"❌ 服务创建异常: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_voice_placeholder_detection():
    """测试语音占位符检测"""
    print("\n📝 测试语音占位符检测")
    print("=" * 40)
    
    try:
        from src.bot.wechat_handler import WeChatHandler
        
        handler = WeChatHandler()
        
        test_cases = [
            "[语音]1秒,未播放",
            "[语音]3秒,未播放",
            "推荐一款手机",
            "voice_001.amr"
        ]
        
        for content in test_cases:
            is_placeholder = handler._is_voice_placeholder(content)
            status = "🎤" if is_placeholder else "📝"
            print(f"  {status} '{content}' → {'占位符' if is_placeholder else '非占位符'}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_cache_search():
    """测试缓存搜索"""
    print("\n🔍 测试缓存搜索")
    print("=" * 40)
    
    try:
        from src.bot.wechat_handler import WeChatHandler
        
        handler = WeChatHandler()
        
        print("搜索微信缓存目录...")
        result = handler._try_find_voice_file_in_cache("测试用户")
        
        if result:
            print(f"✅ 找到语音文件: {result}")
        else:
            print("❌ 未找到最新的语音文件")
            print("   这是正常的，因为没有最近的语音消息")
        
        return True
        
    except Exception as e:
        print(f"❌ 缓存搜索失败: {e}")
        return False

def create_test_voice_file():
    """创建测试语音文件"""
    print("\n🎵 创建测试语音文件")
    print("=" * 40)
    
    try:
        # 创建一个简单的测试音频文件（空文件，仅用于测试）
        test_file = "test_voice.wav"
        
        with open(test_file, 'wb') as f:
            # 写入一个简单的WAV文件头（44字节）
            f.write(b'RIFF')  # ChunkID
            f.write((36).to_bytes(4, 'little'))  # ChunkSize
            f.write(b'WAVE')  # Format
            f.write(b'fmt ')  # Subchunk1ID
            f.write((16).to_bytes(4, 'little'))  # Subchunk1Size
            f.write((1).to_bytes(2, 'little'))   # AudioFormat
            f.write((1).to_bytes(2, 'little'))   # NumChannels
            f.write((16000).to_bytes(4, 'little'))  # SampleRate
            f.write((32000).to_bytes(4, 'little'))  # ByteRate
            f.write((2).to_bytes(2, 'little'))   # BlockAlign
            f.write((16).to_bytes(2, 'little'))  # BitsPerSample
            f.write(b'data')  # Subchunk2ID
            f.write((0).to_bytes(4, 'little'))   # Subchunk2Size
        
        print(f"✅ 创建测试文件: {test_file}")
        return test_file
        
    except Exception as e:
        print(f"❌ 创建测试文件失败: {e}")
        return None

def test_baidu_recognition():
    """测试百度语音识别"""
    print("\n🎯 测试百度语音识别")
    print("=" * 40)
    
    try:
        from config import config
        from src.ai.baidu_voice_service import get_baidu_voice_service
        
        # 检查配置
        if not config.baidu_voice.enabled:
            print("❌ 百度语音识别功能未启用")
            return False
        
        if not config.baidu_voice.api_key:
            print("❌ 百度API Key未配置")
            return False
        
        if not config.baidu_voice.secret_key:
            print("❌ 百度Secret Key未配置")
            print("💡 请在Web配置页面填写完整的API密钥")
            return False
        
        # 创建服务
        service = get_baidu_voice_service(config)
        if not service:
            print("❌ 百度语音服务创建失败")
            return False
        
        # 测试连接
        print("测试百度API连接...")
        if service.test_connection():
            print("✅ 百度API连接正常")
        else:
            print("❌ 百度API连接失败")
            print("💡 请检查API密钥是否正确")
            return False
        
        # 创建测试文件
        test_file = create_test_voice_file()
        if not test_file:
            return False
        
        try:
            # 尝试识别（预期会失败，因为是空文件）
            print(f"尝试识别测试文件: {test_file}")
            result = service.recognize_voice_file(test_file)
            
            if result:
                print(f"✅ 识别成功: {result}")
            else:
                print("⚠️ 识别失败（预期结果，因为是空文件）")
                print("✅ 但API调用正常，说明配置正确")
            
            return True
            
        finally:
            # 清理测试文件
            if os.path.exists(test_file):
                os.remove(test_file)
                print(f"🗑️ 清理测试文件: {test_file}")
        
    except Exception as e:
        print(f"❌ 百度识别测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🎤 百度语音识别快速测试")
    print()
    
    tests = [
        ("百度SDK导入", test_baidu_import),
        ("百度服务创建", test_baidu_service_creation),
        ("语音占位符检测", test_voice_placeholder_detection),
        ("缓存搜索", test_cache_search),
        ("百度语音识别", test_baidu_recognition)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"🔍 运行测试: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
            if result:
                print(f"✅ {test_name} 通过")
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
            results.append((test_name, False))
        print()
    
    # 总结
    print("=" * 50)
    print("          测试总结")
    print("=" * 50)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅" if result else "❌"
        print(f"{status} {test_name}")
    
    print(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed >= 4:  # 前4个测试通过就基本OK
        print("\n🎉 基础功能正常！")
        print("\n💡 下一步:")
        print("  1. 在Web配置页面填写百度的App ID和Secret Key")
        print("  2. 重启机器人程序")
        print("  3. 发送语音消息测试")
    else:
        print("\n⚠️ 需要修复基础问题")
        
        failed_tests = [name for name, result in results if not result]
        print(f"\n需要修复:")
        for test_name in failed_tests:
            print(f"  - {test_name}")

if __name__ == "__main__":
    main()
