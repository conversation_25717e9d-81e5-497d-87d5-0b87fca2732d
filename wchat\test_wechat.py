"""
微信连接测试脚本
测试wxauto库的基本功能
"""
import os
import sys
import time

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def test_wxauto_basic():
    """测试wxauto基本功能"""
    print("=" * 50)
    print("测试wxauto基本功能")
    print("=" * 50)
    
    try:
        from wxauto import WeChat
        print("✅ wxauto库导入成功")
        
        # 创建微信实例
        print("\n正在连接微信...")
        wx = WeChat()
        print("✅ 微信实例创建成功")
        
        # 检查微信状态
        if hasattr(wx, 'nickname'):
            print(f"✅ 微信已登录，用户: {wx.nickname}")
        else:
            print("⚠️  无法获取微信用户信息")
        
        # 测试可用方法
        print("\n可用的方法:")
        methods = [method for method in dir(wx) if not method.startswith('_') and callable(getattr(wx, method))]
        for method in sorted(methods):
            print(f"  - {method}")
        
        return True
        
    except ImportError as e:
        print(f"❌ wxauto库导入失败: {e}")
        print("请安装wxauto: pip install wxauto")
        return False
    except Exception as e:
        print(f"❌ 微信连接失败: {e}")
        print("请确保:")
        print("  1. 微信PC版已安装并登录")
        print("  2. 微信版本兼容wxauto库")
        return False


def test_message_listening():
    """测试消息监听功能"""
    print("\n" + "=" * 50)
    print("测试消息监听功能")
    print("=" * 50)
    
    try:
        from wxauto import WeChat
        
        wx = WeChat()
        
        # 测试获取新消息
        print("测试获取新消息...")
        try:
            msgs = wx.GetNewMessage()
            print(f"✅ GetNewMessage方法可用，返回类型: {type(msgs)}")
            if msgs:
                print(f"   获取到 {len(msgs)} 条消息")
                for i, msg in enumerate(msgs[:3]):  # 只显示前3条
                    print(f"   消息{i+1}: {msg}")
            else:
                print("   当前无新消息")
        except Exception as e:
            print(f"❌ GetNewMessage方法失败: {e}")
        
        # 测试其他消息相关方法
        message_methods = ['GetAllMessage', 'GetNextNewMessage']
        for method_name in message_methods:
            if hasattr(wx, method_name):
                print(f"✅ {method_name} 方法可用")
            else:
                print(f"❌ {method_name} 方法不可用")
        
        return True
        
    except Exception as e:
        print(f"❌ 消息监听测试失败: {e}")
        return False


def test_chat_operations():
    """测试聊天操作功能"""
    print("\n" + "=" * 50)
    print("测试聊天操作功能")
    print("=" * 50)
    
    try:
        from wxauto import WeChat
        
        wx = WeChat()
        
        # 测试聊天相关方法
        chat_methods = ['ChatWith', 'SendMsg', 'AddListenChat', 'RemoveListenChat']
        for method_name in chat_methods:
            if hasattr(wx, method_name):
                print(f"✅ {method_name} 方法可用")
            else:
                print(f"❌ {method_name} 方法不可用")
        
        # 测试获取聊天信息
        if hasattr(wx, 'GetSession'):
            print("✅ GetSession 方法可用")
        else:
            print("❌ GetSession 方法不可用")
        
        return True
        
    except Exception as e:
        print(f"❌ 聊天操作测试失败: {e}")
        return False


def interactive_test():
    """交互式测试"""
    print("\n" + "=" * 50)
    print("交互式测试")
    print("=" * 50)
    
    try:
        from wxauto import WeChat
        
        wx = WeChat()
        
        print("微信连接成功！")
        print("可以进行以下测试:")
        print("1. 发送测试消息")
        print("2. 监听消息")
        print("3. 退出")
        
        while True:
            choice = input("\n请选择 (1-3): ").strip()
            
            if choice == "1":
                chat_name = input("请输入聊天对象名称: ").strip()
                if chat_name:
                    try:
                        if wx.ChatWith(chat_name):
                            test_msg = "这是一条测试消息 - " + time.strftime("%H:%M:%S")
                            wx.SendMsg(test_msg)
                            print(f"✅ 测试消息已发送到: {chat_name}")
                        else:
                            print(f"❌ 找不到聊天对象: {chat_name}")
                    except Exception as e:
                        print(f"❌ 发送失败: {e}")
                        
            elif choice == "2":
                print("开始监听消息（10秒）...")
                start_time = time.time()
                while time.time() - start_time < 10:
                    try:
                        msgs = wx.GetNewMessage()
                        if msgs:
                            for msg in msgs:
                                print(f"收到消息: {msg}")
                        time.sleep(1)
                    except Exception as e:
                        print(f"监听失败: {e}")
                        break
                print("监听结束")
                
            elif choice == "3":
                break
            else:
                print("无效选择")
        
        return True
        
    except Exception as e:
        print(f"❌ 交互式测试失败: {e}")
        return False


def main():
    """主函数"""
    print("微信客服机器人 - 微信连接测试")
    print("=" * 50)
    
    # 基本功能测试
    if not test_wxauto_basic():
        return
    
    # 消息监听测试
    test_message_listening()
    
    # 聊天操作测试
    test_chat_operations()
    
    # 询问是否进行交互式测试
    print("\n" + "=" * 50)
    choice = input("是否进行交互式测试？(y/n): ").strip().lower()
    if choice in ['y', 'yes', '是']:
        interactive_test()
    
    print("\n测试完成！")
    print("如果所有测试都通过，说明wxauto库工作正常")
    print("可以尝试启动完整的机器人: python run.py")


if __name__ == "__main__":
    main()
