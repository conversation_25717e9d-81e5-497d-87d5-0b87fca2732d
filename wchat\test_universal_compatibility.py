#!/usr/bin/env python3
"""
测试通用匹配器在不同行业产品库中的兼容性
"""
import os
import sys
from pathlib import Path

# 添加项目路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def test_electronics_store():
    """测试电子产品商店"""
    print("🔌 测试电子产品商店")
    print("-" * 40)
    
    from src.database.universal_product_matcher import UniversalProductMatcher
    matcher = UniversalProductMatcher()
    
    # 电子产品库
    products = [
        {"产品名称": "iPhone 15 Pro", "分类": "智能手机", "产品描述": "苹果最新旗舰手机"},
        {"产品名称": "MacBook Pro", "分类": "笔记本电脑", "产品描述": "苹果专业笔记本"},
        {"产品名称": "AirPods Pro", "分类": "无线耳机", "产品描述": "苹果降噪耳机"},
        {"产品名称": "Magic Mouse", "分类": "电脑配件", "产品描述": "苹果无线鼠标"},
        {"产品名称": "iPhone充电器", "分类": "手机配件", "产品描述": "20W快充充电器"},
        {"产品名称": "iPad", "分类": "平板电脑", "产品描述": "苹果平板电脑"},
    ]
    
    test_queries = ["手机", "电脑", "耳机", "平板", "鼠标", "充电器"]
    
    for query in test_queries:
        print(f"\n查询: '{query}'")
        matches = []
        for product in products:
            score = matcher.calculate_product_score(query, product)
            if score > 0.5:
                matches.append((product, score))
        
        matches.sort(key=lambda x: x[1], reverse=True)
        if matches:
            for product, score in matches[:2]:
                print(f"  {product['产品名称']} (分数: {score:.3f})")
        else:
            print(f"  无匹配产品")
    
    return True

def test_clothing_store():
    """测试服装店"""
    print("\n👕 测试服装店")
    print("-" * 40)
    
    from src.database.universal_product_matcher import UniversalProductMatcher
    matcher = UniversalProductMatcher()
    
    # 服装产品库
    products = [
        {"产品名称": "纯棉白T恤", "分类": "上衣", "产品描述": "100%纯棉短袖T恤"},
        {"产品名称": "牛仔外套", "分类": "外套", "产品描述": "经典牛仔夹克"},
        {"产品名称": "修身牛仔裤", "分类": "裤子", "产品描述": "弹力修身牛仔裤"},
        {"产品名称": "运动短裤", "分类": "裤子", "产品描述": "透气运动短裤"},
        {"产品名称": "帆布鞋", "分类": "鞋子", "产品描述": "经典帆布休闲鞋"},
        {"产品名称": "运动鞋", "分类": "鞋子", "产品描述": "透气跑步鞋"},
        {"产品名称": "连衣裙", "分类": "裙子", "产品描述": "夏季清新连衣裙"},
    ]
    
    test_queries = ["T恤", "外套", "裤子", "鞋子", "裙子", "牛仔", "运动"]
    
    for query in test_queries:
        print(f"\n查询: '{query}'")
        matches = []
        for product in products:
            score = matcher.calculate_product_score(query, product)
            if score > 0.5:
                matches.append((product, score))
        
        matches.sort(key=lambda x: x[1], reverse=True)
        if matches:
            for product, score in matches[:2]:
                print(f"  {product['产品名称']} (分数: {score:.3f})")
        else:
            print(f"  无匹配产品")
    
    return True

def test_home_appliance_store():
    """测试家电商店"""
    print("\n🏠 测试家电商店")
    print("-" * 40)
    
    from src.database.universal_product_matcher import UniversalProductMatcher
    matcher = UniversalProductMatcher()
    
    # 家电产品库
    products = [
        {"产品名称": "海尔冰箱", "分类": "大家电", "产品描述": "双门对开冰箱"},
        {"产品名称": "美的洗衣机", "分类": "大家电", "产品描述": "全自动洗衣机"},
        {"产品名称": "格力空调", "分类": "大家电", "产品描述": "变频空调"},
        {"产品名称": "九阳豆浆机", "分类": "小家电", "产品描述": "全自动豆浆机"},
        {"产品名称": "苏泊尔电饭煲", "分类": "小家电", "产品描述": "智能电饭煲"},
        {"产品名称": "飞利浦吹风机", "分类": "个护家电", "产品描述": "负离子吹风机"},
    ]
    
    test_queries = ["冰箱", "洗衣机", "空调", "豆浆机", "电饭煲", "吹风机", "大家电", "小家电"]
    
    for query in test_queries:
        print(f"\n查询: '{query}'")
        matches = []
        for product in products:
            score = matcher.calculate_product_score(query, product)
            if score > 0.5:
                matches.append((product, score))
        
        matches.sort(key=lambda x: x[1], reverse=True)
        if matches:
            for product, score in matches[:2]:
                print(f"  {product['产品名称']} (分数: {score:.3f})")
        else:
            print(f"  无匹配产品")
    
    return True

def test_food_store():
    """测试食品店"""
    print("\n🍎 测试食品店")
    print("-" * 40)
    
    from src.database.universal_product_matcher import UniversalProductMatcher
    matcher = UniversalProductMatcher()
    
    # 食品产品库
    products = [
        {"产品名称": "新鲜苹果", "分类": "水果", "产品描述": "红富士苹果"},
        {"产品名称": "有机香蕉", "分类": "水果", "产品描述": "进口有机香蕉"},
        {"产品名称": "新鲜牛肉", "分类": "肉类", "产品描述": "优质牛肉"},
        {"产品名称": "三文鱼", "分类": "海鲜", "产品描述": "挪威三文鱼"},
        {"产品名称": "纯牛奶", "分类": "乳制品", "产品描述": "全脂纯牛奶"},
        {"产品名称": "薯片", "分类": "零食", "产品描述": "原味薯片"},
        {"产品名称": "可乐", "分类": "饮料", "产品描述": "经典可乐"},
    ]
    
    test_queries = ["水果", "苹果", "肉类", "海鲜", "牛奶", "零食", "饮料"]
    
    for query in test_queries:
        print(f"\n查询: '{query}'")
        matches = []
        for product in products:
            score = matcher.calculate_product_score(query, product)
            if score > 0.5:
                matches.append((product, score))
        
        matches.sort(key=lambda x: x[1], reverse=True)
        if matches:
            for product, score in matches[:2]:
                print(f"  {product['产品名称']} (分数: {score:.3f})")
        else:
            print(f"  无匹配产品")
    
    return True

def test_automotive_store():
    """测试汽车用品店"""
    print("\n🚗 测试汽车用品店")
    print("-" * 40)
    
    from src.database.universal_product_matcher import UniversalProductMatcher
    matcher = UniversalProductMatcher()
    
    # 汽车用品库
    products = [
        {"产品名称": "行车记录仪", "分类": "车载电子", "产品描述": "高清行车记录仪"},
        {"产品名称": "车载充电器", "分类": "车载配件", "产品描述": "双USB车载充电器"},
        {"产品名称": "汽车脚垫", "分类": "内饰用品", "产品描述": "全包围脚垫"},
        {"产品名称": "机油", "分类": "保养用品", "产品描述": "全合成机油"},
        {"产品名称": "轮胎", "分类": "汽车配件", "产品描述": "四季轮胎"},
        {"产品名称": "车载香水", "分类": "装饰用品", "产品描述": "汽车香水"},
    ]
    
    test_queries = ["记录仪", "充电器", "脚垫", "机油", "轮胎", "香水", "车载", "汽车"]
    
    for query in test_queries:
        print(f"\n查询: '{query}'")
        matches = []
        for product in products:
            score = matcher.calculate_product_score(query, product)
            if score > 0.5:
                matches.append((product, score))
        
        matches.sort(key=lambda x: x[1], reverse=True)
        if matches:
            for product, score in matches[:2]:
                print(f"  {product['产品名称']} (分数: {score:.3f})")
        else:
            print(f"  无匹配产品")
    
    return True

def main():
    """主函数"""
    print("🌍 通用产品匹配器跨行业兼容性测试")
    print("=" * 60)
    
    # 运行不同行业的测试
    industry_tests = [
        ("电子产品商店", test_electronics_store),
        ("服装店", test_clothing_store),
        ("家电商店", test_home_appliance_store),
        ("食品店", test_food_store),
        ("汽车用品店", test_automotive_store)
    ]
    
    results = []
    for industry_name, test_func in industry_tests:
        print(f"\n🔍 测试行业: {industry_name}")
        try:
            result = test_func()
            results.append((industry_name, result))
            if result:
                print(f"✅ {industry_name} 测试通过")
            else:
                print(f"❌ {industry_name} 测试失败")
        except Exception as e:
            print(f"❌ {industry_name} 测试异常: {e}")
            results.append((industry_name, False))
    
    # 总结
    print(f"\n" + "=" * 60)
    print(f"          跨行业兼容性总结")
    print(f"=" * 60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for industry_name, result in results:
        status = "✅ 兼容" if result else "❌ 不兼容"
        print(f"{status} {industry_name}")
    
    print(f"\n总计: {passed}/{total} 个行业兼容")
    
    if passed == total:
        print("🎉 通用产品匹配器具有完全的跨行业兼容性！")
        print("\n💡 通用性优势:")
        print("   1. ✅ 支持电子产品、服装、家电、食品、汽车等各行业")
        print("   2. ✅ 智能识别不同行业的产品类型和特征")
        print("   3. ✅ 自动适应不同的产品分类体系")
        print("   4. ✅ 基于语义理解，无需硬编码行业规则")
        print("   5. ✅ 可扩展到任何新的产品类别")
        print("\n🚀 您可以放心地将此系统应用到任何产品库！")
        print("\n📋 使用建议:")
        print("   - 保持产品名称的准确性")
        print("   - 合理设置产品分类")
        print("   - 可选择性添加产品关键词")
        print("   - 定期测试和优化匹配效果")
    else:
        print("⚠️ 部分行业兼容性需要优化")
        print("建议针对特定行业调整匹配规则")

if __name__ == "__main__":
    main()
