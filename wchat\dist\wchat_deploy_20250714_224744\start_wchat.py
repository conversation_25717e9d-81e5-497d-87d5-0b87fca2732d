#!/usr/bin/env python3
"""
WChat 一键启动程序
"""
import os
import sys
import time
import subprocess
import threading
import webbrowser
from pathlib import Path

# 添加项目根目录到Python路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def print_banner():
    """打印启动横幅"""
    banner = """
╔══════════════════════════════════════════════════════════════╗
║                                                              ║
║                    🤖 WChat 智能客服系统                     ║
║                                                              ║
║                        一键启动程序                          ║
║                                                              ║
╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)

def check_dependencies():
    """检查依赖项"""
    print("🔍 检查系统依赖...")
    
    required_packages = [
        'flask', 'pandas', 'openpyxl', 'requests', 
        'jieba', 'fuzzywuzzy', 'openai', 'beautifulsoup4'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
            print(f"   ✅ {package}")
        except ImportError:
            print(f"   ❌ {package} (缺失)")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n⚠️  发现缺失的依赖包: {', '.join(missing_packages)}")
        print("正在自动安装...")
        
        for package in missing_packages:
            try:
                subprocess.check_call([sys.executable, '-m', 'pip', 'install', package])
                print(f"   ✅ 已安装 {package}")
            except subprocess.CalledProcessError:
                print(f"   ❌ 安装 {package} 失败")
                return False
    
    print("✅ 所有依赖项检查完成")
    return True

def check_config():
    """检查配置文件"""
    print("\n🔧 检查配置文件...")

    # 检查config目录和配置文件
    config_dir = current_dir / "config"
    config_init = config_dir / "__init__.py"
    config_json = config_dir / "config.json"

    if not config_dir.exists():
        print("❌ config目录不存在")
        return False

    if not config_init.exists():
        print("❌ config模块不存在")
        return False

    try:
        # 添加当前目录到Python路径
        if str(current_dir) not in sys.path:
            sys.path.insert(0, str(current_dir))

        from config import config
        print("✅ 配置模块加载成功")

        # 检查关键配置
        if hasattr(config, 'ai') and config.ai.api_key:
            print("✅ AI配置已设置")
        else:
            print("⚠️  AI配置未完整设置，部分功能可能受限")

        # 检查配置文件
        if config_json.exists():
            print("✅ 配置文件存在")
        else:
            print("⚠️  配置文件不存在，将使用默认配置")

        return True
    except Exception as e:
        print(f"❌ 配置加载失败: {e}")
        return False

def check_data_files():
    """检查数据文件"""
    print("\n📊 检查数据文件...")

    # 检查当前目录和上级目录的data文件夹
    data_paths = [
        current_dir / "data",
        current_dir.parent / "data"
    ]

    data_dir = None
    for path in data_paths:
        if path.exists():
            data_dir = path
            break

    if not data_dir:
        # 在当前目录创建data目录
        data_dir = current_dir / "data"
        print("⚠️  数据目录不存在，正在创建...")
        data_dir.mkdir(exist_ok=True)

    faq_file = data_dir / "faq.xlsx"
    products_file = data_dir / "products.xlsx"

    if faq_file.exists():
        print("✅ FAQ数据文件存在")
    else:
        print("⚠️  FAQ数据文件不存在，将使用默认数据")

    if products_file.exists():
        print("✅ 产品数据文件存在")
    else:
        print("⚠️  产品数据文件不存在，将使用默认数据")

    return True

def start_web_server():
    """启动Web服务器"""
    print("\n🌐 启动Web服务器...")

    try:
        # 查找Web应用目录
        web_paths = [
            current_dir / "src" / "web",
            current_dir.parent / "wchat" / "src" / "web"
        ]

        web_dir = None
        for path in web_paths:
            if path.exists() and (path / "app.py").exists():
                web_dir = path
                break

        if not web_dir:
            print("❌ 未找到Web应用目录")
            return None

        # 切换到Web目录
        original_dir = os.getcwd()
        os.chdir(web_dir)

        # 设置环境变量
        env = os.environ.copy()
        # 添加项目根目录到Python路径
        project_root = str(current_dir.parent) if (current_dir.parent / "config.py").exists() else str(current_dir)
        env['PYTHONPATH'] = project_root

        process = subprocess.Popen(
            [sys.executable, "app.py"],
            env=env,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True,
            bufsize=1
        )
        
        print("✅ Web服务器启动中...")
        
        # 监控启动日志
        startup_success = False
        for line in iter(process.stdout.readline, ''):
            print(f"   {line.strip()}")
            if "Running on http://127.0.0.1:5000" in line:
                startup_success = True
                break
            elif "Debugger is active!" in line:
                startup_success = True
                break
            elif process.poll() is not None:
                break
        
        if startup_success:
            print("✅ Web服务器启动成功")
            return process
        else:
            print("❌ Web服务器启动失败")
            return None
            
    except Exception as e:
        print(f"❌ 启动Web服务器失败: {e}")
        return None

def start_wechat_bot():
    """启动微信机器人"""
    print("\n🤖 启动微信机器人...")

    try:
        # 检查run.py文件是否存在
        bot_file = current_dir / "run.py"
        if not bot_file.exists():
            print("❌ 未找到机器人启动文件 run.py")
            return None

        # 设置环境变量
        env = os.environ.copy()
        env['PYTHONPATH'] = str(current_dir)

        # 启动机器人进程
        process = subprocess.Popen(
            [sys.executable, "run.py"],
            cwd=current_dir,
            env=env,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True,
            bufsize=1
        )

        print("✅ 微信机器人启动中...")

        # 监控启动日志（简化版，避免阻塞）
        try:
            # 等待一小段时间看看是否有明显错误
            time.sleep(2)
            if process.poll() is None:
                print("✅ 微信机器人进程已启动")
                return process
            else:
                print("⚠️  微信机器人启动可能有问题，请检查微信PC版是否已登录")
                return process
        except:
            return process

    except Exception as e:
        print(f"❌ 启动微信机器人失败: {e}")
        return None

def open_browser():
    """打开浏览器"""
    print("\n🌍 打开浏览器...")
    
    # 等待服务器完全启动
    time.sleep(3)
    
    try:
        webbrowser.open("http://localhost:5000")
        print("✅ 浏览器已打开")
    except Exception as e:
        print(f"⚠️  自动打开浏览器失败: {e}")
        print("请手动访问: http://localhost:5000")

def show_usage_info():
    """显示使用说明"""
    info = """
╔══════════════════════════════════════════════════════════════╗
║                        🎉 启动成功！                         ║
╠══════════════════════════════════════════════════════════════╣
║                                                              ║
║  📱 Web管理界面: http://localhost:5000                       ║
║  👤 默认账号: admin                                          ║
║  🔑 默认密码: admin123                                       ║
║                                                              ║
║  📋 主要功能:                                                ║
║     • 仪表板 - 系统状态监控                                  ║
║     • FAQ管理 - 常见问题管理                                 ║
║     • 产品管理 - 产品信息管理                                ║
║     • 配置管理 - 系统参数配置                                ║
║     • 数据管理 - 数据导入导出                                ║
║                                                              ║
║  🔧 操作提示:                                                ║
║     • 按 Ctrl+C 停止服务器                                   ║
║     • 修改配置后需重启服务器                                 ║
║     • 数据文件位于 data/ 目录                                ║
║                                                              ║
╚══════════════════════════════════════════════════════════════╝
    """
    print(info)

def main():
    """主函数"""
    print_banner()
    
    # 检查系统环境
    if not check_dependencies():
        print("\n❌ 依赖检查失败，请手动安装缺失的包")
        input("按回车键退出...")
        return
    
    if not check_config():
        print("\n❌ 配置检查失败，请检查配置文件")
        input("按回车键退出...")
        return
    
    check_data_files()
    
    # 启动Web服务器
    web_process = start_web_server()
    if not web_process:
        print("\n❌ Web服务器启动失败")
        input("按回车键退出...")
        return

    # 启动微信机器人
    bot_process = start_wechat_bot()
    if not bot_process:
        print("⚠️  微信机器人启动失败，但Web服务器仍可使用")

    # 在后台线程中打开浏览器
    browser_thread = threading.Thread(target=open_browser)
    browser_thread.daemon = True
    browser_thread.start()

    # 显示使用说明
    show_usage_info()

    try:
        # 保持程序运行，监控进程
        print("🔄 系统运行中... (按 Ctrl+C 停止)")

        # 监控两个进程
        processes = [web_process]
        if bot_process:
            processes.append(bot_process)

        while True:
            time.sleep(1)
            # 检查进程是否还在运行
            for process in processes[:]:  # 使用切片避免修改列表时的问题
                if process.poll() is not None:
                    processes.remove(process)
                    if process == web_process:
                        print("⚠️  Web服务器意外停止")
                    elif process == bot_process:
                        print("⚠️  微信机器人意外停止")

            if not processes:
                print("所有进程都已停止")
                break

    except KeyboardInterrupt:
        print("\n\n🛑 正在停止系统...")

        # 停止Web服务器
        if web_process and web_process.poll() is None:
            print("停止Web服务器...")
            web_process.terminate()
            try:
                web_process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                web_process.kill()

        # 停止微信机器人
        if bot_process and bot_process.poll() is None:
            print("停止微信机器人...")
            bot_process.terminate()
            try:
                bot_process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                bot_process.kill()

        print("✅ 系统已停止")
    except Exception as e:
        print(f"\n❌ 运行时错误: {e}")
    finally:
        print("\n👋 感谢使用 WChat 智能客服系统！")

if __name__ == "__main__":
    main()
