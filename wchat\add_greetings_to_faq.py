#!/usr/bin/env python3
"""
添加基本问候语到FAQ
"""
import os
import sys
import pandas as pd
from pathlib import Path

# 添加项目路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def add_greetings_to_faq():
    """添加基本问候语到FAQ"""
    print("👋 添加基本问候语到FAQ")
    print("=" * 60)
    
    try:
        faq_file = current_dir / "data" / "faq.xlsx"
        
        # 读取现有FAQ
        if faq_file.exists():
            df = pd.read_excel(faq_file)
            print(f"✅ 读取现有FAQ: {len(df)} 条记录")
        else:
            # 创建新的DataFrame
            df = pd.DataFrame(columns=['问题', '答案'])
            print("✅ 创建新的FAQ文件")
        
        # 显示现有内容
        print(f"\n现有FAQ内容:")
        for i, row in df.iterrows():
            print(f"   {i+1}. {row['问题']} → {row['答案']}")
        
        # 定义要添加的问候语
        greetings = [
            {
                "问题": "你好",
                "答案": "你好！有什么可以帮你的吗？"
            },
            {
                "问题": "您好",
                "答案": "您好！有什么需要了解的吗？"
            },
            {
                "问题": "hi",
                "答案": "Hi！有什么可以帮助你的？"
            },
            {
                "问题": "hello",
                "答案": "Hello！需要什么帮助吗？"
            },
            {
                "问题": "早上好",
                "答案": "早上好！今天想看看什么产品？"
            },
            {
                "问题": "晚上好",
                "答案": "晚上好！有什么可以为你介绍的？"
            }
        ]
        
        # 检查哪些问候语需要添加
        existing_questions = df['问题'].str.lower().tolist() if not df.empty else []
        new_greetings = []
        
        for greeting in greetings:
            if greeting['问题'].lower() not in existing_questions:
                new_greetings.append(greeting)
        
        if new_greetings:
            print(f"\n准备添加 {len(new_greetings)} 个新问候语:")
            for greeting in new_greetings:
                print(f"   + {greeting['问题']} → {greeting['答案']}")
            
            # 添加新问候语
            new_df = pd.DataFrame(new_greetings)
            df = pd.concat([df, new_df], ignore_index=True)
            
            # 保存到Excel文件
            df.to_excel(faq_file, index=False)
            print(f"\n✅ 已保存到 {faq_file}")
            print(f"✅ 总计FAQ条目: {len(df)} 条")
        else:
            print(f"\n✅ 所有问候语已存在，无需添加")
        
        # 显示最终内容
        print(f"\n最终FAQ内容:")
        for i, row in df.iterrows():
            print(f"   {i+1}. {row['问题']} → {row['答案']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 添加问候语失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_greeting_replies():
    """测试问候语回复"""
    print("\n🧪 测试问候语回复")
    print("=" * 60)
    
    try:
        from src.bot.enhanced_reply_engine_with_images import EnhancedReplyEngineWithImages
        
        reply_engine = EnhancedReplyEngineWithImages()
        
        # 测试问候语
        greetings = ["你好", "您好", "hi", "hello", "早上好", "晚上好"]
        
        for greeting in greetings:
            print(f"\n测试: '{greeting}'")
            reply, image_paths = reply_engine.generate_reply_with_images(greeting)
            
            if reply:
                print(f"回复: {reply}")
                print(f"图片: {len(image_paths)} 张")
                print("✅ 有回复")
            else:
                print("回复: (无回复)")
                print("❌ 无回复")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 FAQ问候语添加工具")
    print()
    
    # 添加问候语
    success1 = add_greetings_to_faq()
    
    # 测试回复
    success2 = test_greeting_replies()
    
    # 总结
    print(f"\n" + "=" * 60)
    print(f"          总结")
    print(f"=" * 60)
    
    if success1 and success2:
        print("🎉 问候语添加和测试成功！")
        print("\n💡 现在支持的问候语:")
        print("   - 你好 / 您好")
        print("   - hi / hello") 
        print("   - 早上好 / 晚上好")
        print("\n🚀 重启机器人后，'你好'将有友好回复！")
    else:
        print("⚠️ 部分操作失败，请检查错误信息")

if __name__ == "__main__":
    main()
