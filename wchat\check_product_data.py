#!/usr/bin/env python3
"""
检查产品数据内容
"""
import sys
from pathlib import Path

# 添加项目路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def check_product_data():
    """检查产品数据"""
    print("=" * 60)
    print("          检查产品数据内容")
    print("=" * 60)
    
    try:
        from src.database.enhanced_reader import EnhancedProductReader
        from config import config
        
        # 创建产品读取器
        product_reader = EnhancedProductReader(config.database.products_file)
        print(f"✅ 产品数据加载: {len(product_reader.data)} 条记录")
        
        # 显示所有产品数据
        print("\n完整产品数据:")
        for i, row in product_reader.data.iterrows():
            print(f"\n产品 {i+1}:")
            for col in product_reader.data.columns:
                value = row[col]
                print(f"  {col}: {value}")
        
        # 测试搜索功能
        print("\n" + "=" * 60)
        print("          测试产品搜索")
        print("=" * 60)
        
        test_keywords = ["电脑", "手机", "耳机", "智能", "蓝牙", "笔记本"]
        
        for keyword in test_keywords:
            print(f"\n搜索关键词: '{keyword}'")
            results = product_reader.search_products(keyword)
            if results:
                print(f"  找到 {len(results)} 个结果:")
                for j, product in enumerate(results, 1):
                    name = product.get('产品名称', 'N/A')
                    desc = product.get('产品描述', 'N/A')
                    print(f"    {j}. {name} - {desc[:50]}...")
            else:
                print("  未找到相关产品")
        
        return True
        
    except Exception as e:
        print(f"❌ 检查产品数据失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_ai_prompt():
    """检查AI提示词"""
    print("\n" + "=" * 60)
    print("          检查AI提示词")
    print("=" * 60)
    
    try:
        from src.ai.llm_service import LLMService
        from config import config
        
        # 创建LLM服务
        llm = LLMService(
            api_key=config.ai.api_key,
            base_url=config.ai.base_url,
            model=config.ai.model,
            max_tokens=config.ai.max_tokens,
            temperature=config.ai.temperature
        )
        
        # 检查系统提示词
        if hasattr(llm, 'system_prompt'):
            print("当前系统提示词:")
            print(llm.system_prompt)
        else:
            print("未找到系统提示词配置")
        
        # 测试AI回复
        print("\n测试AI回复:")
        test_messages = [
            "电脑",
            "笔记本电脑推荐",
            "有什么电脑产品",
            "我想买台电脑"
        ]
        
        for msg in test_messages:
            print(f"\n测试: {msg}")
            try:
                reply = llm.generate_reply(msg)
                print(f"回复: {reply}")
            except Exception as e:
                print(f"回复失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 检查AI提示词失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_reply_engine():
    """测试回复引擎"""
    print("\n" + "=" * 60)
    print("          测试回复引擎")
    print("=" * 60)
    
    try:
        from src.bot.enhanced_reply_engine import EnhancedReplyEngine
        
        # 创建回复引擎
        reply_engine = EnhancedReplyEngine()
        print("✅ 增强回复引擎创建成功")
        
        # 测试电脑相关查询
        test_messages = [
            "电脑",
            "笔记本电脑",
            "有什么电脑推荐",
            "我想买台电脑",
            "电脑价格",
            "台式电脑"
        ]
        
        print("\n测试电脑相关查询:")
        for msg in test_messages:
            print(f"\n查询: {msg}")
            print("-" * 40)
            
            try:
                reply = reply_engine.generate_reply(msg)
                print(f"回复: {reply}")
                
                # 分析回复类型
                if "产品" in reply or "推荐" in reply or "价格" in reply:
                    print("类型: 产品推荐 ✅")
                elif "FAQ" in reply or "客服" in reply:
                    print("类型: FAQ回复")
                else:
                    print("类型: AI回复")
                    
            except Exception as e:
                print(f"回复失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试回复引擎失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("开始检查产品数据和AI逻辑...")
    
    # 检查产品数据
    if check_product_data():
        print("\n✅ 产品数据检查完成")
    else:
        print("\n❌ 产品数据检查失败")
        return
    
    # 检查AI提示词
    if check_ai_prompt():
        print("\n✅ AI提示词检查完成")
    else:
        print("\n❌ AI提示词检查失败")
    
    # 测试回复引擎
    if test_reply_engine():
        print("\n✅ 回复引擎测试完成")
    else:
        print("\n❌ 回复引擎测试失败")
    
    print("\n" + "=" * 60)
    print("检查完成！")
    print("=" * 60)

if __name__ == "__main__":
    main()
