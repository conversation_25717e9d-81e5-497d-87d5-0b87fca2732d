#!/usr/bin/env python3
"""
测试微信处理器AI集成
"""
import os
import sys
from pathlib import Path

# 添加项目路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def test_wechat_handler_engine():
    """测试微信处理器使用的回复引擎"""
    print("🔧 测试微信处理器回复引擎")
    print("=" * 60)
    
    try:
        from src.bot.wechat_handler import WeChatHandler, USE_IMAGE_ENGINE
        
        print(f"回复引擎模式:")
        print(f"  USE_IMAGE_ENGINE: {USE_IMAGE_ENGINE}")
        
        # 创建微信处理器
        handler = WeChatHandler()
        
        print(f"  回复引擎类型: {type(handler.reply_engine).__name__}")
        print(f"  回复引擎模块: {type(handler.reply_engine).__module__}")
        
        # 检查回复引擎是否支持AI
        has_ai_method = hasattr(handler.reply_engine, 'generate_reply_with_images')
        print(f"  支持图片回复: {has_ai_method}")
        
        if USE_IMAGE_ENGINE and has_ai_method:
            print("✅ 使用支持AI和图片的回复引擎")
        elif USE_IMAGE_ENGINE:
            print("⚠️ 应该使用图片引擎但方法不存在")
        else:
            print("⚠️ 使用旧版回复引擎，不支持AI")
        
        return USE_IMAGE_ENGINE and has_ai_method
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_reply_generation():
    """测试回复生成"""
    print("\n💬 测试回复生成")
    print("=" * 60)
    
    try:
        from src.bot.wechat_handler import WeChatHandler
        
        handler = WeChatHandler()
        
        # 测试不同类型的消息
        test_messages = [
            {
                "content": "你好",
                "type": "基本问候",
                "should_reply": True
            },
            {
                "content": "您好",
                "type": "礼貌问候",
                "should_reply": True
            },
            {
                "content": "手机推荐",
                "type": "产品查询",
                "should_reply": True
            },
            {
                "content": "你是谁",
                "type": "身份询问",
                "should_reply": True
            },
            {
                "content": "随便聊聊",
                "type": "闲聊",
                "should_reply": True
            }
        ]
        
        print("回复测试结果:")
        print("-" * 40)
        
        all_passed = True
        
        for test in test_messages:
            print(f"\n测试: '{test['content']}' ({test['type']})")
            
            try:
                reply, image_paths = handler._generate_reply(
                    test['content'], 
                    "测试用户", 
                    "测试用户"
                )
                
                has_reply = bool(reply and reply.strip())
                should_reply = test['should_reply']
                
                if has_reply:
                    print(f"回复: {reply[:100]}...")
                    if image_paths:
                        print(f"图片: {len(image_paths)} 张")
                    
                    # 检查回复质量
                    if "AI" in reply or "机器人" in reply:
                        print("⚠️ 回复暴露AI身份")
                    elif "联系客服" in reply:
                        print("⚠️ 回复提及客服")
                    else:
                        print("✅ 回复质量良好")
                        
                    if should_reply:
                        print("✅ 正确有回复")
                    else:
                        print("⚠️ 意外有回复")
                else:
                    print("回复: (无回复)")
                    if should_reply:
                        print("❌ 应该有回复但没有")
                        all_passed = False
                    else:
                        print("✅ 正确保持沉默")
                        
            except Exception as e:
                print(f"❌ 回复生成失败: {e}")
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        print(f"❌ 回复测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_ai_configuration():
    """测试AI配置"""
    print("\n🤖 测试AI配置")
    print("=" * 60)
    
    try:
        from config import config
        
        print("AI配置状态:")
        print(f"  API密钥: {'已配置' if config.ai.api_key else '未配置'}")
        print(f"  API地址: {config.ai.base_url}")
        print(f"  模型: {config.ai.model}")
        print(f"  AI启用: {config.ai.enabled}")
        print(f"  AI回退: {config.reply.use_ai_fallback}")
        
        # 检查配置完整性
        config_complete = (
            bool(config.ai.api_key) and
            config.ai.enabled and
            config.reply.use_ai_fallback
        )
        
        if config_complete:
            print("✅ AI配置完整")
        else:
            print("❌ AI配置不完整")
            if not config.ai.api_key:
                print("  - 缺少API密钥")
            if not config.ai.enabled:
                print("  - AI未启用")
            if not config.reply.use_ai_fallback:
                print("  - AI回退未启用")
        
        return config_complete
        
    except Exception as e:
        print(f"❌ AI配置测试失败: {e}")
        return False

def simulate_wechat_message():
    """模拟微信消息处理"""
    print("\n📱 模拟微信消息处理")
    print("=" * 60)
    
    try:
        from src.bot.wechat_handler import WeChatHandler
        
        # 创建模拟微信API
        class MockWeChat:
            def __init__(self):
                self.sent_messages = []
                self.sent_files = []
                self.current_chat = None
            
            def ChatWith(self, chat_name):
                self.current_chat = chat_name
                return True
            
            def SendMsg(self, msg):
                self.sent_messages.append((self.current_chat, msg))
                print(f"📤 发送消息: {msg}")
            
            def SendFiles(self, filepath):
                self.sent_files.append((self.current_chat, filepath))
                filename = os.path.basename(filepath)
                print(f"📷 发送图片: {filename}")
        
        # 创建处理器并设置模拟微信
        handler = WeChatHandler()
        mock_wx = MockWeChat()
        handler.wx = mock_wx
        
        # 模拟消息
        test_message = "你好"
        sender = "测试用户"
        
        print(f"模拟消息: '{test_message}' 来自 {sender}")
        
        # 生成回复
        reply, image_paths = handler._generate_reply(test_message, sender, sender)
        
        if reply:
            # 发送文本回复
            handler._send_reply(reply, sender)
            
            # 发送图片
            if image_paths:
                handler._send_product_images(image_paths, sender)
            
            print(f"\n处理结果:")
            print(f"  文本消息: {len(mock_wx.sent_messages)} 条")
            print(f"  图片文件: {len(mock_wx.sent_files)} 张")
            print("✅ 消息处理成功")
            
            return True
        else:
            print("❌ 无回复生成")
            return False
        
    except Exception as e:
        print(f"❌ 模拟测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 微信处理器AI集成测试")
    print()
    
    # 运行测试
    tests = [
        ("回复引擎", test_wechat_handler_engine),
        ("回复生成", test_reply_generation),
        ("AI配置", test_ai_configuration),
        ("消息模拟", simulate_wechat_message)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n🔍 运行测试: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
            if result:
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 总结
    print(f"\n" + "=" * 60)
    print(f"          微信AI集成测试总结")
    print(f"=" * 60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{status} {test_name}")
    
    print(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 微信处理器AI集成成功！")
        print("\n💡 现在支持:")
        print("   1. ✅ 统一使用支持AI的回复引擎")
        print("   2. ✅ 基本聊天由AI处理")
        print("   3. ✅ 产品查询自动发送图片")
        print("   4. ✅ 拟人化AI回复")
        print("\n🚀 重启微信机器人，'你好'将有AI回复！")
    else:
        print("⚠️ 部分功能存在问题")
        
        # 给出具体建议
        if not results[0][1]:  # 回复引擎测试失败
            print("\n💡 建议: 检查回复引擎导入")
        if not results[2][1]:  # AI配置测试失败
            print("\n💡 建议: 检查AI配置和API密钥")

if __name__ == "__main__":
    main()
