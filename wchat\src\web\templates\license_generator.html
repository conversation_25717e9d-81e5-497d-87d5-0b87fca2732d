{% extends "base.html" %}

{% block title %}许可证生成器 - 私域自动化{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-certificate me-2"></i>
        许可证生成器
        <span class="badge bg-warning text-dark ms-2">管理员专用</span>
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <button type="button" class="btn btn-outline-secondary btn-sm" onclick="clearForm()">
            <i class="fas fa-eraser me-1"></i>清空表单
        </button>
    </div>
</div>

<!-- 许可证生成表单 -->
<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-plus-circle me-2"></i>
                    生成新许可证
                </h5>
            </div>
            <div class="card-body">
                <form id="license-form">
                    <!-- 硬件ID -->
                    <div class="mb-3">
                        <label for="hardware-id" class="form-label">
                            <i class="fas fa-microchip me-1"></i>
                            硬件ID <span class="text-danger">*</span>
                        </label>
                        <input type="text" class="form-control font-monospace" id="hardware-id" 
                               placeholder="请输入目标设备的硬件ID" required>
                        <div class="form-text">
                            客户端可通过"许可证管理"页面获取硬件ID
                        </div>
                    </div>

                    <!-- 有效期 -->
                    <div class="mb-3">
                        <label for="validity-days" class="form-label">
                            <i class="fas fa-calendar-alt me-1"></i>
                            有效期（天）<span class="text-danger">*</span>
                        </label>
                        <div class="row">
                            <div class="col-md-6">
                                <input type="number" class="form-control" id="validity-days" 
                                       value="365" min="1" max="3650" required>
                            </div>
                            <div class="col-md-6">
                                <div class="btn-group" role="group">
                                    <button type="button" class="btn btn-outline-secondary" onclick="setDays(30)">30天</button>
                                    <button type="button" class="btn btn-outline-secondary" onclick="setDays(90)">90天</button>
                                    <button type="button" class="btn btn-outline-secondary" onclick="setDays(365)">1年</button>
                                    <button type="button" class="btn btn-outline-secondary" onclick="setDays(1095)">3年</button>
                                </div>
                            </div>
                        </div>
                        <div class="form-text">
                            有效期范围：1-3650天（最多10年）
                        </div>
                    </div>

                    <!-- 功能配置 -->
                    <div class="mb-4">
                        <label class="form-label">
                            <i class="fas fa-cogs me-1"></i>
                            授权功能
                        </label>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="voice-recognition" checked>
                                    <label class="form-check-label" for="voice-recognition">
                                        <i class="fas fa-microphone me-1"></i>语音识别
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="ai-chat" checked>
                                    <label class="form-check-label" for="ai-chat">
                                        <i class="fas fa-robot me-1"></i>AI聊天
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="product-management" checked>
                                    <label class="form-check-label" for="product-management">
                                        <i class="fas fa-box me-1"></i>产品管理
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="faq-management" checked>
                                    <label class="form-check-label" for="faq-management">
                                        <i class="fas fa-question-circle me-1"></i>FAQ管理
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="web-interface" checked>
                                    <label class="form-check-label" for="web-interface">
                                        <i class="fas fa-globe me-1"></i>Web界面
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 生成按钮 -->
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary btn-lg">
                            <i class="fas fa-magic me-2"></i>
                            生成许可证
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 生成结果 -->
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-key me-2"></i>
                    生成结果
                </h5>
            </div>
            <div class="card-body">
                <div id="result-empty" class="text-center text-muted">
                    <i class="fas fa-info-circle fa-2x mb-3"></i>
                    <p>填写表单并点击"生成许可证"按钮</p>
                </div>
                
                <div id="result-content" style="display: none;">
                    <!-- 许可证信息 -->
                    <div class="mb-3">
                        <label class="form-label">许可证密钥</label>
                        <div class="input-group">
                            <textarea class="form-control font-monospace" id="generated-license" 
                                      rows="4" readonly></textarea>
                            <button class="btn btn-outline-secondary" type="button" 
                                    onclick="copyToClipboard('generated-license')">
                                <i class="fas fa-copy"></i>
                            </button>
                        </div>
                    </div>
                    
                    <!-- 许可证详情 -->
                    <div class="mb-3">
                        <h6>许可证详情</h6>
                        <table class="table table-sm">
                            <tr>
                                <td>硬件ID:</td>
                                <td class="font-monospace" id="result-hardware-id"></td>
                            </tr>
                            <tr>
                                <td>有效期:</td>
                                <td id="result-days"></td>
                            </tr>
                            <tr>
                                <td>生成时间:</td>
                                <td id="result-time"></td>
                            </tr>
                        </table>
                    </div>
                    
                    <!-- 授权功能 -->
                    <div class="mb-3">
                        <h6>授权功能</h6>
                        <div id="result-features"></div>
                    </div>
                    
                    <!-- 操作按钮 -->
                    <div class="d-grid gap-2">
                        <button class="btn btn-success btn-sm" onclick="downloadLicense()">
                            <i class="fas fa-download me-1"></i>下载许可证
                        </button>
                        <button class="btn btn-outline-primary btn-sm" onclick="copyLicenseInfo()">
                            <i class="fas fa-clipboard me-1"></i>复制完整信息
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 使用说明 -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    使用说明
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>生成流程</h6>
                        <ol>
                            <li>客户端运行系统并获取硬件ID</li>
                            <li>在此页面输入硬件ID和配置信息</li>
                            <li>点击"生成许可证"按钮</li>
                            <li>将生成的许可证密钥发送给客户</li>
                            <li>客户在许可证管理页面安装许可证</li>
                        </ol>
                    </div>
                    <div class="col-md-6">
                        <h6>注意事项</h6>
                        <ul>
                            <li>每个硬件ID只能生成对应的许可证</li>
                            <li>许可证密钥包含加密的硬件绑定信息</li>
                            <li>建议根据客户需求选择合适的有效期</li>
                            <li>可以根据版本差异配置不同的功能</li>
                            <li>生成的许可证密钥请妥善保管</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
let currentLicenseData = null;

// 表单提交处理
document.getElementById('license-form').addEventListener('submit', function(e) {
    e.preventDefault();
    generateLicense();
});

// 生成许可证
function generateLicense() {
    const hardwareId = document.getElementById('hardware-id').value.trim();
    const days = parseInt(document.getElementById('validity-days').value);
    
    if (!hardwareId) {
        showError('请输入硬件ID');
        return;
    }
    
    if (!days || days < 1 || days > 3650) {
        showError('有效期必须在1-3650天之间');
        return;
    }
    
    // 收集功能配置
    const features = {
        voice_recognition: document.getElementById('voice-recognition').checked,
        ai_chat: document.getElementById('ai-chat').checked,
        product_management: document.getElementById('product-management').checked,
        faq_management: document.getElementById('faq-management').checked,
        web_interface: document.getElementById('web-interface').checked
    };
    
    const button = document.querySelector('button[type="submit"]');
    const originalText = button.innerHTML;
    button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>生成中...';
    button.disabled = true;
    
    fetch('/api/license/generate', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            hardware_id: hardwareId,
            days: days,
            features: features
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showLicenseResult(data.data);
            showSuccess(data.message);
        } else {
            showError(data.error);
        }
    })
    .catch(error => {
        showError('生成许可证失败: ' + error.message);
    })
    .finally(() => {
        button.innerHTML = originalText;
        button.disabled = false;
    });
}

// 显示生成结果
function showLicenseResult(data) {
    currentLicenseData = data;
    
    document.getElementById('generated-license').value = data.license_key;
    document.getElementById('result-hardware-id').textContent = data.hardware_id.substring(0, 16) + '...';
    document.getElementById('result-days').textContent = data.days + ' 天';
    document.getElementById('result-time').textContent = new Date().toLocaleString();
    
    // 显示功能列表
    const featuresDiv = document.getElementById('result-features');
    featuresDiv.innerHTML = '';
    
    for (const [feature, enabled] of Object.entries(data.features)) {
        const badge = document.createElement('span');
        badge.className = enabled ? 'badge bg-success me-1 mb-1' : 'badge bg-secondary me-1 mb-1';
        badge.innerHTML = (enabled ? '<i class="fas fa-check me-1"></i>' : '<i class="fas fa-times me-1"></i>') + getFeatureName(feature);
        featuresDiv.appendChild(badge);
    }
    
    document.getElementById('result-empty').style.display = 'none';
    document.getElementById('result-content').style.display = 'block';
}

// 获取功能显示名称
function getFeatureName(feature) {
    const names = {
        voice_recognition: '语音识别',
        ai_chat: 'AI聊天',
        product_management: '产品管理',
        faq_management: 'FAQ管理',
        web_interface: 'Web界面'
    };
    return names[feature] || feature;
}

// 设置天数
function setDays(days) {
    document.getElementById('validity-days').value = days;
}

// 清空表单
function clearForm() {
    document.getElementById('license-form').reset();
    document.getElementById('validity-days').value = 365;
    document.getElementById('result-empty').style.display = 'block';
    document.getElementById('result-content').style.display = 'none';
    currentLicenseData = null;
}

// 复制到剪贴板
function copyToClipboard(elementId) {
    const element = document.getElementById(elementId);
    element.select();
    element.setSelectionRange(0, 99999);
    document.execCommand('copy');
    showSuccess('已复制到剪贴板');
}

// 复制完整许可证信息
function copyLicenseInfo() {
    if (!currentLicenseData) return;
    
    const info = `许可证信息
硬件ID: ${currentLicenseData.hardware_id}
有效期: ${currentLicenseData.days} 天
生成时间: ${new Date().toLocaleString()}

许可证密钥:
${currentLicenseData.license_key}

授权功能:
${Object.entries(currentLicenseData.features).map(([k, v]) => `- ${getFeatureName(k)}: ${v ? '启用' : '禁用'}`).join('\n')}`;
    
    navigator.clipboard.writeText(info).then(() => {
        showSuccess('完整信息已复制到剪贴板');
    }).catch(() => {
        showError('复制失败，请手动复制');
    });
}

// 下载许可证
function downloadLicense() {
    if (!currentLicenseData) return;
    
    const content = `# 私域自动化系统许可证
# 生成时间: ${new Date().toLocaleString()}
# 硬件ID: ${currentLicenseData.hardware_id}
# 有效期: ${currentLicenseData.days} 天

${currentLicenseData.license_key}`;
    
    const blob = new Blob([content], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `license_${currentLicenseData.hardware_id.substring(0, 8)}_${new Date().toISOString().split('T')[0]}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    
    showSuccess('许可证文件已下载');
}

// 显示成功消息
function showSuccess(message) {
    // 这里可以使用 Bootstrap Toast 或其他通知组件
    alert('✅ ' + message);
}

// 显示错误消息
function showError(message) {
    // 这里可以使用 Bootstrap Toast 或其他通知组件
    alert('❌ ' + message);
}
</script>
{% endblock %}
