#!/usr/bin/env python3
"""
专门调试语音消息对象的脚本
"""
import os
import sys
from pathlib import Path

# 添加项目路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def create_voice_message_interceptor():
    """创建语音消息拦截器"""
    print("🔧 创建语音消息拦截器")
    print("=" * 60)
    
    interceptor_code = '''
# 在 wechat_handler.py 的 _handle_message 方法中添加
def _debug_voice_message_object(self, msg, msg_type, content, sender):
    """详细调试语音消息对象"""
    if msg_type in ['voice', 'audio']:
        print("\\n" + "="*60)
        print(f"🎤 语音消息对象详细调试")
        print("="*60)
        
        # 1. 基本信息
        print(f"消息类型: {msg_type}")
        print(f"发送者: {sender}")
        print(f"内容: {content}")
        print(f"对象类型: {type(msg)}")
        print(f"对象ID: {id(msg)}")
        
        # 2. 所有属性
        all_attrs = [attr for attr in dir(msg) if not attr.startswith('_')]
        print(f"\\n所有属性 ({len(all_attrs)}个):")
        for i, attr in enumerate(all_attrs, 1):
            try:
                value = getattr(msg, attr, None)
                value_type = type(value).__name__
                
                # 限制显示长度
                if isinstance(value, str):
                    display_value = value[:100] + "..." if len(value) > 100 else value
                elif callable(value):
                    display_value = f"<方法: {attr}>"
                else:
                    display_value = str(value)[:100]
                
                print(f"  {i:2d}. {attr:20s} = {display_value} ({value_type})")
            except Exception as e:
                print(f"  {i:2d}. {attr:20s} = <获取失败: {e}>")
        
        # 3. 重点检查语音相关属性
        voice_attrs = [
            'path', 'file_path', 'voice_path', 'filepath', 'voice_filepath',
            'file', 'voice_file', 'audio_path', 'audio_file', 'audio_filepath',
            'local_path', 'download_path', 'save_path', 'temp_path',
            'url', 'voice_url', 'audio_url', 'download_url',
            'data', 'voice_data', 'audio_data', 'binary_data'
        ]
        
        print(f"\\n🎯 语音相关属性检查:")
        found_voice_attrs = []
        for attr in voice_attrs:
            if hasattr(msg, attr):
                try:
                    value = getattr(msg, attr, None)
                    if value:
                        found_voice_attrs.append((attr, value))
                        print(f"  ✅ {attr}: {value}")
                    else:
                        print(f"  ⚪ {attr}: (空值)")
                except Exception as e:
                    print(f"  ❌ {attr}: <获取失败: {e}>")
        
        if not found_voice_attrs:
            print("  ❌ 未找到任何语音相关属性")
        
        # 4. 尝试调用可能的方法
        voice_methods = [
            'get_path', 'get_file_path', 'get_voice_path', 'get_filepath',
            'download', 'save', 'save_file', 'download_file', 'export',
            'to_file', 'get_data', 'get_voice_data', 'get_audio_data'
        ]
        
        print(f"\\n🔧 语音方法调用测试:")
        for method_name in voice_methods:
            if hasattr(msg, method_name):
                try:
                    method = getattr(msg, method_name)
                    if callable(method):
                        print(f"  🔍 尝试调用 {method_name}()")
                        
                        # 尝试不同的调用方式
                        try:
                            result = method()
                            print(f"    ✅ {method_name}() = {result}")
                        except TypeError:
                            # 可能需要参数
                            try:
                                result = method("temp_voice")
                                print(f"    ✅ {method_name}('temp_voice') = {result}")
                            except:
                                try:
                                    result = method(path="temp_voice.amr")
                                    print(f"    ✅ {method_name}(path='temp_voice.amr') = {result}")
                                except:
                                    print(f"    ❌ {method_name}() 调用失败")
                        except Exception as e:
                            print(f"    ❌ {method_name}() 异常: {e}")
                except Exception as e:
                    print(f"  ❌ {method_name} 获取失败: {e}")
        
        # 5. 检查父类和模块信息
        print(f"\\n📦 对象信息:")
        print(f"  模块: {msg.__class__.__module__}")
        print(f"  类名: {msg.__class__.__name__}")
        print(f"  MRO: {[cls.__name__ for cls in msg.__class__.__mro__]}")
        
        # 6. 尝试序列化
        print(f"\\n💾 序列化测试:")
        try:
            import json
            # 尝试转换为字典
            if hasattr(msg, '__dict__'):
                msg_dict = msg.__dict__
                print(f"  __dict__: {msg_dict}")
            
            if hasattr(msg, 'to_dict'):
                to_dict_result = msg.to_dict()
                print(f"  to_dict(): {to_dict_result}")
                
        except Exception as e:
            print(f"  序列化失败: {e}")
        
        print("="*60)
        print("🎤 语音消息对象调试完成")
        print("="*60 + "\\n")
        
        return found_voice_attrs
'''
    
    print("语音消息拦截器代码:")
    print(interceptor_code)
    
    return interceptor_code

def create_enhanced_voice_handler():
    """创建增强的语音处理器"""
    print("\n🎯 创建增强的语音处理器")
    print("=" * 60)
    
    handler_code = '''
# 替换现有的 _convert_voice_to_text_enhanced 方法
def _convert_voice_to_text_enhanced(self, voice_content: str, sender: str, msg_obj=None) -> Optional[str]:
    """超级增强的语音转文字方法"""
    logger.info(f"🎤 开始超级增强语音转文字")
    logger.info(f"  输入内容: {voice_content}")
    logger.info(f"  发送者: {sender}")
    logger.info(f"  消息对象: {type(msg_obj) if msg_obj else None}")
    
    try:
        # 1. 详细分析消息对象
        real_voice_paths = []
        
        if msg_obj:
            logger.info(f"🔍 分析消息对象...")
            
            # 获取所有属性
            all_attrs = [attr for attr in dir(msg_obj) if not attr.startswith('_')]
            logger.info(f"  对象属性: {all_attrs}")
            
            # 检查每个属性的值
            for attr in all_attrs:
                try:
                    value = getattr(msg_obj, attr, None)
                    if value and isinstance(value, str):
                        # 检查是否看起来像文件路径
                        if self._looks_like_voice_file_path(value):
                            real_voice_paths.append(value)
                            logger.info(f"  🎯 找到可能的语音路径 {attr}: {value}")
                        elif len(value) > 10 and ('/' in value or '\\\\' in value):
                            logger.info(f"  📁 可能的路径 {attr}: {value}")
                except:
                    continue
            
            # 尝试调用可能的方法
            methods_to_try = [
                ('get_path', []),
                ('get_file_path', []),
                ('download', []),
                ('save', ['temp_voice.amr']),
                ('save_file', ['temp_voice.amr']),
                ('to_file', ['temp_voice.amr'])
            ]
            
            for method_name, args in methods_to_try:
                if hasattr(msg_obj, method_name):
                    try:
                        method = getattr(msg_obj, method_name)
                        if callable(method):
                            logger.info(f"  🔧 尝试调用 {method_name}({args})")
                            result = method(*args) if args else method()
                            if result and isinstance(result, str):
                                if self._looks_like_voice_file_path(result):
                                    real_voice_paths.append(result)
                                    logger.info(f"  ✅ 方法 {method_name} 返回路径: {result}")
                    except Exception as e:
                        logger.debug(f"  ❌ 方法 {method_name} 失败: {e}")
        
        # 2. 使用找到的真实路径
        for voice_path in real_voice_paths:
            logger.info(f"🎵 尝试识别语音文件: {voice_path}")
            
            if os.path.exists(voice_path):
                logger.info(f"  ✅ 文件存在，开始识别")
                result = self._convert_voice_to_text(voice_path, sender)
                if result and not self._is_voice_placeholder(result):
                    logger.info(f"  🎉 识别成功: {result}")
                    return result
            else:
                logger.warning(f"  ❌ 文件不存在: {voice_path}")
        
        # 3. 尝试缓存搜索
        logger.info(f"🔍 尝试缓存搜索...")
        cache_path = self._try_find_voice_file_in_cache(sender)
        if cache_path:
            logger.info(f"  ✅ 缓存中找到: {cache_path}")
            result = self._convert_voice_to_text(cache_path, sender)
            if result and not self._is_voice_placeholder(result):
                logger.info(f"  🎉 缓存识别成功: {result}")
                return result
        
        # 4. 最后尝试原始内容
        logger.info(f"🔄 尝试原始内容...")
        result = self._convert_voice_to_text(voice_content, sender)
        if result and not self._is_voice_placeholder(result):
            logger.info(f"  🎉 原始内容识别成功: {result}")
            return result
        
        logger.warning(f"❌ 所有语音识别方法都失败")
        return None
        
    except Exception as e:
        logger.error(f"❌ 超级增强语音转文字异常: {e}")
        import traceback
        traceback.print_exc()
        return None

def _looks_like_voice_file_path(self, path: str) -> bool:
    """检查是否看起来像语音文件路径"""
    if not path or not isinstance(path, str):
        return False
    
    # 语音文件扩展名
    voice_exts = ['.amr', '.wav', '.mp3', '.m4a', '.silk', '.pcm', '.aac']
    path_lower = path.lower()
    
    # 检查扩展名
    for ext in voice_exts:
        if path_lower.endswith(ext):
            return True
    
    # 检查路径特征
    if ('/' in path or '\\\\' in path) and any(keyword in path_lower for keyword in ['voice', 'audio', 'sound']):
        return True
    
    return False
'''
    
    print("增强语音处理器代码:")
    print(handler_code)
    
    return handler_code

def main():
    """主函数"""
    print("🎤 语音消息对象调试工具")
    print()
    
    steps = [
        ("语音消息拦截器", create_voice_message_interceptor),
        ("增强语音处理器", create_enhanced_voice_handler)
    ]
    
    for step_name, step_func in steps:
        print(f"🔍 {step_name}")
        try:
            step_func()
        except Exception as e:
            print(f"❌ {step_name} 失败: {e}")
    
    print(f"\n" + "=" * 60)
    print(f"          调试建议")
    print(f"=" * 60)
    
    print("🎯 立即行动:")
    print("  1. 将调试代码添加到 wechat_handler.py")
    print("  2. 重启机器人程序")
    print("  3. 发送语音消息")
    print("  4. 查看详细的对象调试信息")
    
    print("\n💡 关键信息:")
    print("  - 查看语音消息对象的所有属性")
    print("  - 找到包含文件路径的属性")
    print("  - 测试可能的下载/保存方法")
    print("  - 确定wxauto的语音消息结构")
    
    print("\n🔧 根据调试结果:")
    print("  - 如果找到文件路径属性 → 直接使用")
    print("  - 如果有下载方法 → 调用下载后识别")
    print("  - 如果都没有 → 考虑其他方案")

if __name__ == "__main__":
    main()
