#!/usr/bin/env python3
"""
测试回复结尾
"""
import sys
from pathlib import Path

# 添加项目路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def test_endings():
    """测试回复结尾"""
    print("测试产品回复结尾...")
    
    try:
        from src.database.enhanced_reader import format_product_reply
        
        # 测试单个产品结尾
        single_product = [{
            'name': '智能手机A1',
            'price': '2999',
            'description': '高性能智能手机',
            'stock': '有货'
        }]
        
        print("\n单个产品结尾:")
        print("=" * 50)
        reply = format_product_reply(single_product)
        # 只显示结尾部分
        lines = reply.split('\n')
        ending = lines[-1] if lines else ""
        print(f"结尾: {ending}")
        
        # 测试多个产品结尾
        multiple_products = [
            {
                'name': '笔记本电脑D4',
                'price': '4999',
                'description': '轻薄便携',
                'stock': '有货'
            },
            {
                'name': '游戏电脑G1',
                'price': '7999',
                'description': '高性能游戏电脑',
                'stock': '有货'
            }
        ]
        
        print("\n多个产品结尾:")
        print("=" * 50)
        reply = format_product_reply(multiple_products)
        # 只显示结尾部分
        lines = reply.split('\n')
        ending = lines[-1] if lines else ""
        print(f"结尾: {ending}")
        
        # 检查是否包含期望的表达
        print("\n结尾风格检查:")
        print("=" * 50)
        
        single_ending = format_product_reply(single_product).split('\n')[-1]
        multiple_ending = format_product_reply(multiple_products).split('\n')[-1]
        
        # 检查单个产品结尾
        if "这款怎么样" in single_ending and "～" in single_ending:
            print("✅ 单个产品结尾：亲切自然")
        else:
            print("❌ 单个产品结尾：仍然官方")
        
        # 检查多个产品结尾
        if "看看你喜欢哪款" in multiple_ending and "～" in multiple_ending:
            print("✅ 多个产品结尾：亲切自然")
        else:
            print("❌ 多个产品结尾：仍然官方")
        
        # 对比官方vs亲切的表达
        print("\n表达风格对比:")
        print("=" * 50)
        print("官方表达 → 亲切表达")
        print("请问您对哪款产品感兴趣？我可以为您详细介绍！")
        print("↓")
        print("看看你喜欢哪款？我来给你详细介绍一下～")
        print()
        print("这款产品怎么样？如果您觉得合适，我可以为您介绍更多详情！")
        print("↓")
        print("这款怎么样？觉得合适的话我再给你详细说说～")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_complete_reply():
    """测试完整回复"""
    print("\n测试完整回复效果...")
    
    try:
        from src.bot.enhanced_reply_engine import EnhancedReplyEngine
        
        reply_engine = EnhancedReplyEngine()
        
        # 测试手机查询的完整回复
        print("\n手机查询完整回复:")
        print("=" * 60)
        reply = reply_engine.generate_reply("手机")
        print(reply)
        
        # 检查结尾
        lines = reply.split('\n')
        ending = lines[-1] if lines else ""
        print(f"\n实际结尾: {ending}")
        
        if "看看你喜欢哪款" in ending:
            print("✅ 结尾已更新为亲切表达")
        else:
            print("❌ 结尾仍为官方表达")
        
        return True
        
    except Exception as e:
        print(f"❌ 完整回复测试失败: {e}")
        return False

if __name__ == "__main__":
    print("测试产品回复结尾的感情化表达...")
    
    if test_endings():
        print("\n✅ 结尾测试完成")
    
    if test_complete_reply():
        print("\n✅ 完整回复测试完成")
    
    print("\n🎉 感情化表达测试完成！")
    print("\n改进效果:")
    print("• 更亲切：'你' 代替 '您'")
    print("• 更自然：'看看你喜欢哪款' 代替 '请问您对哪款产品感兴趣'")
    print("• 更有感情：'～' 符号增加亲和力")
    print("• 更口语化：'我来给你详细介绍一下' 代替 '我可以为您详细介绍'")
