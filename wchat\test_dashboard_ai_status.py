#!/usr/bin/env python3
"""
测试仪表板AI状态显示
"""
import requests
import re

def test_dashboard_ai_status():
    """测试仪表板AI状态显示"""
    print("🖥️ 测试仪表板AI状态显示")
    print("=" * 60)
    
    try:
        session = requests.Session()
        
        # 登录
        login_data = {'password': 'admin123'}
        login_response = session.post('http://localhost:5000/login', data=login_data)
        print(f"登录状态: {login_response.status_code}")
        
        if login_response.status_code != 200:
            print("❌ 登录失败")
            return False
        
        # 访问仪表板页面
        dashboard_response = session.get('http://localhost:5000/dashboard')
        print(f"仪表板页面状态: {dashboard_response.status_code}")
        
        if dashboard_response.status_code == 200:
            content = dashboard_response.text
            
            # 检查AI状态显示
            print("\n🔍 检查AI状态显示...")
            
            # 查找AI状态卡片
            if "AI状态" in content:
                print("✅ 找到AI状态卡片")
                
                # 检查是否显示"可用"
                if "可用" in content and "text-success" in content:
                    print("✅ AI状态显示：可用 (绿色)")
                    ai_status_ok = True
                elif "不可用" in content and "text-danger" in content:
                    print("❌ AI状态显示：不可用 (红色)")
                    ai_status_ok = False
                else:
                    print("❓ AI状态显示不明确")
                    ai_status_ok = False
                
                # 检查模型信息显示
                model_pattern = r'<span id="current-ai-model">([^<]+)</span>'
                model_match = re.search(model_pattern, content)
                if model_match:
                    current_model = model_match.group(1)
                    print(f"✅ 找到模型信息: {current_model}")
                    model_info_ok = True
                else:
                    print("❌ 未找到模型信息")
                    model_info_ok = False
                
                # 检查统计信息
                faq_pattern = r'<span[^>]*id="faq-count"[^>]*>(\d+)</span>'
                product_pattern = r'<span[^>]*id="product-count"[^>]*>(\d+)</span>'
                
                faq_match = re.search(faq_pattern, content)
                product_match = re.search(product_pattern, content)
                
                if faq_match and product_match:
                    faq_count = faq_match.group(1)
                    product_count = product_match.group(1)
                    print(f"✅ 统计信息: FAQ={faq_count}, 产品={product_count}")
                    stats_ok = True
                else:
                    print("❌ 统计信息显示异常")
                    stats_ok = False
                
                return ai_status_ok and model_info_ok and stats_ok
            else:
                print("❌ 未找到AI状态卡片")
                return False
        else:
            print(f"❌ 仪表板页面访问失败: {dashboard_response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_api_stats():
    """测试API统计信息"""
    print("\n📊 测试API统计信息...")
    print("=" * 60)
    
    try:
        session = requests.Session()
        
        # 登录
        login_data = {'password': 'admin123'}
        login_response = session.post('http://localhost:5000/login', data=login_data)
        
        if login_response.status_code == 200:
            # 获取统计信息
            stats_response = session.get('http://localhost:5000/api/stats')
            print(f"统计API状态: {stats_response.status_code}")
            
            if stats_response.status_code == 200:
                stats = stats_response.json()
                
                print("✅ API统计信息:")
                print(f"   AI可用: {stats.get('ai_available', False)}")
                print(f"   当前模型: {stats.get('current_model', 'N/A')}")
                print(f"   FAQ数量: {stats.get('faq_count', 0)}")
                print(f"   产品数量: {stats.get('product_count', 0)}")
                print(f"   增强模式: {stats.get('enhanced_mode', False)}")
                
                # 检查必要字段
                required_fields = ['ai_available', 'current_model', 'faq_count', 'product_count']
                missing_fields = [field for field in required_fields if field not in stats]
                
                if not missing_fields:
                    print("✅ 所有必要字段都存在")
                    return True
                else:
                    print(f"❌ 缺少字段: {missing_fields}")
                    return False
            else:
                print(f"❌ 获取统计信息失败: {stats_response.status_code}")
                return False
        else:
            print("❌ 登录失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_dashboard_refresh():
    """测试仪表板刷新功能"""
    print("\n🔄 测试仪表板刷新功能...")
    print("=" * 60)
    
    try:
        session = requests.Session()
        
        # 登录
        login_data = {'password': 'admin123'}
        login_response = session.post('http://localhost:5000/login', data=login_data)
        
        if login_response.status_code == 200:
            # 多次访问仪表板，检查一致性
            for i in range(3):
                dashboard_response = session.get('http://localhost:5000/dashboard')
                print(f"第{i+1}次访问: {dashboard_response.status_code}")
                
                if dashboard_response.status_code != 200:
                    print(f"❌ 第{i+1}次访问失败")
                    return False
            
            print("✅ 仪表板访问一致性正常")
            return True
        else:
            print("❌ 登录失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🎯 仪表板AI状态显示测试")
    print("=" * 60)
    
    # 测试各个功能
    dashboard_test_ok = test_dashboard_ai_status()
    api_test_ok = test_api_stats()
    refresh_test_ok = test_dashboard_refresh()
    
    print("\n" + "=" * 60)
    print("📊 测试结果总结:")
    print(f"   仪表板AI状态显示: {'✅ 正常' if dashboard_test_ok else '❌ 异常'}")
    print(f"   API统计信息: {'✅ 正常' if api_test_ok else '❌ 异常'}")
    print(f"   仪表板刷新: {'✅ 正常' if refresh_test_ok else '❌ 异常'}")
    
    if all([dashboard_test_ok, api_test_ok, refresh_test_ok]):
        print("\n🎉 所有测试通过！仪表板AI状态显示正常。")
        print("\n💡 建议:")
        print("   1. 刷新浏览器页面查看最新状态")
        print("   2. 检查AI状态卡片是否显示绿色'可用'")
        print("   3. 确认模型信息正确显示")
    else:
        print("\n❌ 部分测试失败，请检查相关功能。")
        if not dashboard_test_ok:
            print("   - 仪表板页面可能有显示问题")
        if not api_test_ok:
            print("   - API统计信息可能有问题")
        if not refresh_test_ok:
            print("   - 仪表板刷新可能不稳定")

if __name__ == "__main__":
    main()
