#!/usr/bin/env python3
"""
修复语音消息检测和处理
"""
import os
import sys
from pathlib import Path

# 添加项目路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def analyze_message_detection():
    """分析消息检测逻辑"""
    print("🔍 分析消息检测逻辑")
    print("=" * 60)
    
    print("当前消息检测流程:")
    print("1. 接收微信消息")
    print("2. 提取消息类型 (msg_type)")
    print("3. 检查是否为 text/voice/audio")
    print("4. 如果是语音，调用转换方法")
    
    print(f"\n可能的问题:")
    print("❓ 1. 消息类型可能不是 'voice' 或 'audio'")
    print("❓ 2. 微信API返回的类型名称可能不同")
    print("❓ 3. 语音消息可能被识别为其他类型")
    
    print(f"\n建议的改进:")
    print("💡 1. 扩展支持的语音消息类型")
    print("💡 2. 添加消息内容分析")
    print("💡 3. 检查文件扩展名")

def create_enhanced_voice_detection():
    """创建增强的语音检测逻辑"""
    print("\n🔧 创建增强的语音检测逻辑")
    print("=" * 60)
    
    enhanced_code = '''
def _is_voice_message(self, msg_type: str, content: str) -> bool:
    """
    增强的语音消息检测
    
    Args:
        msg_type: 消息类型
        content: 消息内容
        
    Returns:
        bool: 是否为语音消息
    """
    # 标准语音类型
    voice_types = ['voice', 'audio', 'ptt', 'voice_msg', 'audio_msg']
    
    if msg_type.lower() in voice_types:
        return True
    
    # 检查内容是否像语音文件
    if isinstance(content, str):
        voice_extensions = ['.amr', '.wav', '.mp3', '.m4a', '.silk', '.pcm']
        content_lower = content.lower()
        
        # 检查文件扩展名
        for ext in voice_extensions:
            if content_lower.endswith(ext):
                return True
        
        # 检查是否包含语音相关关键词
        voice_keywords = ['voice', 'audio', 'sound', 'record']
        for keyword in voice_keywords:
            if keyword in content_lower:
                return True
    
    return False

def _handle_message_enhanced(self, msg):
    """增强的消息处理方法"""
    try:
        # 提取消息信息
        sender = msg.get('sender', '')
        content = msg.get('content', '')
        msg_type = msg.get('type', 'unknown')
        
        # 详细日志
        logger.info(f"收到消息 - 发送者: {sender}, 类型: {msg_type}, 内容: {content[:100]}")
        
        # 跳过自己的消息
        if self._is_self_message(sender, content):
            return
        
        # 检查是否在监听列表中
        if not self._should_listen_to(sender):
            return
        
        # 增强的语音消息检测
        is_voice = self._is_voice_message(msg_type, content)
        is_text = msg_type.lower() in ['text', 'string', 'message']
        
        logger.info(f"消息分析 - 是文本: {is_text}, 是语音: {is_voice}")
        
        if not (is_text or is_voice):
            logger.debug(f"忽略非文本/语音消息: {msg_type}")
            return
        
        # 处理语音消息
        if is_voice:
            logger.info(f"处理语音消息 - 类型: {msg_type}")
            
            if not config.wechat.voice_to_text:
                logger.warning("语音转文字功能已禁用")
                return
            
            # 尝试转换语音为文字
            text_content = self._convert_voice_to_text_enhanced(content, sender, msg_type)
            if text_content:
                content = text_content
                logger.info(f"语音转文字成功: {content}")
            else:
                logger.warning("语音转文字失败，忽略消息")
                return
        
        # 生成回复
        reply, image_paths = self._generate_reply(content, sender, sender)
        
        if reply:
            self._send_reply(reply, sender)
            if image_paths:
                self._send_product_images(image_paths, sender)
            logger.info(f"已回复: {reply[:50]}... (图片: {len(image_paths)}张)")
        
    except Exception as e:
        logger.error(f"消息处理异常: {e}")

def _convert_voice_to_text_enhanced(self, voice_content: str, sender: str, msg_type: str) -> Optional[str]:
    """
    增强的语音转文字方法
    """
    logger.info(f"开始语音转文字 - 类型: {msg_type}, 内容: {voice_content}, 发送者: {sender}")
    
    try:
        # 方法1: 微信API - GetVoiceText
        if hasattr(self.wx, 'GetVoiceText'):
            try:
                logger.debug(f"尝试 GetVoiceText")
                text = self.wx.GetVoiceText(voice_content)
                if text and text.strip():
                    logger.info(f"GetVoiceText 成功: {text}")
                    return text.strip()
            except Exception as e:
                logger.debug(f"GetVoiceText 失败: {e}")
        
        # 方法2: 微信API - VoiceToText
        if hasattr(self.wx, 'VoiceToText'):
            try:
                logger.debug(f"尝试 VoiceToText")
                text = self.wx.VoiceToText(voice_content)
                if text and text.strip():
                    logger.info(f"VoiceToText 成功: {text}")
                    return text.strip()
            except Exception as e:
                logger.debug(f"VoiceToText 失败: {e}")
        
        # 方法3: 检查是否已经是文字内容
        if isinstance(voice_content, str) and len(voice_content) > 0:
            # 如果内容不像文件路径，可能已经是转换后的文字
            if not self._looks_like_file_path(voice_content):
                logger.info(f"内容似乎已是文字: {voice_content}")
                return voice_content
        
        # 方法4: 尝试从消息对象获取文字
        # 某些情况下，微信可能已经提供了转换后的文字
        
        logger.warning(f"所有语音转文字方法都失败")
        return None
        
    except Exception as e:
        logger.error(f"语音转文字异常: {e}")
        return None

def _looks_like_file_path(self, content: str) -> bool:
    """检查内容是否像文件路径"""
    if not content:
        return False
    
    # 检查是否包含路径分隔符
    if '/' in content or '\\\\' in content:
        return True
    
    # 检查是否有文件扩展名
    if '.' in content and len(content.split('.')[-1]) <= 4:
        return True
    
    # 检查是否很短（可能是文件ID）
    if len(content) < 50 and not any(c.isspace() for c in content):
        return True
    
    return False
'''
    
    print("增强的语音检测代码:")
    print(enhanced_code)
    
    return enhanced_code

def test_voice_message_types():
    """测试不同的语音消息类型"""
    print("\n🧪 测试不同的语音消息类型")
    print("=" * 60)
    
    test_cases = [
        # 标准类型
        {"type": "voice", "content": "voice_001.amr", "expected": True},
        {"type": "audio", "content": "audio_001.wav", "expected": True},
        {"type": "ptt", "content": "ptt_001.silk", "expected": True},
        
        # 文件扩展名检测
        {"type": "file", "content": "recording.amr", "expected": True},
        {"type": "unknown", "content": "voice.wav", "expected": True},
        {"type": "message", "content": "audio.mp3", "expected": True},
        
        # 关键词检测
        {"type": "data", "content": "voice_message_123", "expected": True},
        {"type": "media", "content": "audio_record_456", "expected": True},
        
        # 非语音消息
        {"type": "text", "content": "你好", "expected": False},
        {"type": "image", "content": "photo.jpg", "expected": False},
        {"type": "file", "content": "document.pdf", "expected": False},
        
        # 边界情况
        {"type": "voice", "content": "", "expected": True},
        {"type": "unknown", "content": "voice", "expected": True},
        {"type": "data", "content": "12345", "expected": False},
    ]
    
    def is_voice_message(msg_type, content):
        """模拟增强的语音检测逻辑"""
        voice_types = ['voice', 'audio', 'ptt', 'voice_msg', 'audio_msg']
        
        if msg_type.lower() in voice_types:
            return True
        
        if isinstance(content, str):
            voice_extensions = ['.amr', '.wav', '.mp3', '.m4a', '.silk', '.pcm']
            content_lower = content.lower()
            
            for ext in voice_extensions:
                if content_lower.endswith(ext):
                    return True
            
            voice_keywords = ['voice', 'audio', 'sound', 'record']
            for keyword in voice_keywords:
                if keyword in content_lower:
                    return True
        
        return False
    
    print("语音消息类型检测测试:")
    correct = 0
    total = len(test_cases)
    
    for i, case in enumerate(test_cases, 1):
        msg_type = case["type"]
        content = case["content"]
        expected = case["expected"]
        
        result = is_voice_message(msg_type, content)
        is_correct = result == expected
        
        status = "✅" if is_correct else "❌"
        print(f"{status} 测试{i}: {msg_type} + '{content}' → {result} (期望: {expected})")
        
        if is_correct:
            correct += 1
    
    print(f"\n测试结果: {correct}/{total} 通过 ({correct/total*100:.1f}%)")

def main():
    """主函数"""
    print("🔧 语音消息检测修复工具")
    print()
    
    steps = [
        ("消息检测分析", analyze_message_detection),
        ("增强检测逻辑", create_enhanced_voice_detection),
        ("类型检测测试", test_voice_message_types)
    ]
    
    for step_name, step_func in steps:
        print(f"\n🔍 执行: {step_name}")
        try:
            step_func()
        except Exception as e:
            print(f"❌ {step_name} 失败: {e}")
    
    print(f"\n" + "=" * 60)
    print(f"          语音消息修复建议")
    print(f"=" * 60)
    
    print("🔧 立即修复建议:")
    print("1. 添加详细的调试日志（已完成）")
    print("2. 扩展语音消息类型检测")
    print("3. 增强语音转文字方法")
    print("4. 测试不同的微信版本")
    
    print("\n💡 长期改进建议:")
    print("1. 集成第三方语音识别API")
    print("2. 支持更多语音格式")
    print("3. 添加语音质量检测")
    print("4. 实现语音消息缓存")
    
    print("\n🚀 下一步操作:")
    print("1. 重启机器人，查看详细日志")
    print("2. 发送语音消息，观察日志输出")
    print("3. 根据日志信息进一步调试")

if __name__ == "__main__":
    main()
