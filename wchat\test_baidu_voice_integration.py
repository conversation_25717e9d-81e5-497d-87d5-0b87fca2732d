#!/usr/bin/env python3
"""
测试百度语音识别集成
"""
import os
import sys
from pathlib import Path

# 添加项目路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def test_baidu_voice_config():
    """测试百度语音配置"""
    print("🔧 测试百度语音配置")
    print("=" * 60)
    
    try:
        from config import config
        
        print("百度语音配置:")
        print(f"  enabled: {config.baidu_voice.enabled}")
        print(f"  app_id: {config.baidu_voice.app_id}")
        print(f"  api_key: {config.baidu_voice.api_key[:10]}..." if config.baidu_voice.api_key else "  api_key: (空)")
        print(f"  secret_key: {config.baidu_voice.secret_key[:10]}..." if config.baidu_voice.secret_key else "  secret_key: (空)")
        print(f"  dev_pid: {config.baidu_voice.dev_pid}")
        print(f"  format: {config.baidu_voice.format}")
        print(f"  rate: {config.baidu_voice.rate}")
        print(f"  channel: {config.baidu_voice.channel}")
        
        # 检查配置完整性
        if config.baidu_voice.enabled:
            print("✅ 百度语音识别功能已启用")
            
            if config.baidu_voice.api_key:
                print("✅ API Key 已配置")
            else:
                print("❌ API Key 未配置")
            
            if config.baidu_voice.app_id:
                print("✅ App ID 已配置")
            else:
                print("⚠️ App ID 未配置（可选）")
            
            if config.baidu_voice.secret_key:
                print("✅ Secret Key 已配置")
            else:
                print("❌ Secret Key 未配置")
        else:
            print("❌ 百度语音识别功能已禁用")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置检查失败: {e}")
        return False

def test_baidu_voice_service():
    """测试百度语音识别服务"""
    print("\n🎤 测试百度语音识别服务")
    print("=" * 60)
    
    try:
        from src.ai.baidu_voice_service import BaiduVoiceService, get_baidu_voice_service
        from config import config
        
        print("1. 测试服务创建")
        service = get_baidu_voice_service(config)
        
        if service:
            print("✅ 百度语音识别服务创建成功")
            
            print("\n2. 测试连接")
            if service.test_connection():
                print("✅ 百度语音识别连接测试成功")
            else:
                print("❌ 百度语音识别连接测试失败")
            
            print("\n3. 服务信息")
            print(f"  支持的格式: {service.get_supported_formats()}")
            print(f"  支持的采样率: {service.get_supported_rates()}")
            print(f"  语言模型: {service.get_language_models()}")
            
            return True
        else:
            print("❌ 百度语音识别服务创建失败")
            print("   可能原因:")
            print("   - 配置不完整")
            print("   - API密钥无效")
            print("   - 网络连接问题")
            return False
            
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        print("   请确保已安装 baidu-aip: pip install baidu-aip")
        return False
    except Exception as e:
        print(f"❌ 服务测试失败: {e}")
        return False

def test_wechat_integration():
    """测试微信集成"""
    print("\n🔗 测试微信集成")
    print("=" * 60)
    
    try:
        from src.bot.wechat_handler import WeChatHandler
        
        handler = WeChatHandler()
        
        print("1. 测试语音占位符检测")
        test_cases = [
            ("[语音]3秒,未播放", True),
            ("推荐一款手机", False),
            ("voice_001.amr", False)
        ]
        
        for content, expected in test_cases:
            result = handler._is_voice_placeholder(content)
            status = "✅" if result == expected else "❌"
            print(f"  {status} '{content}' → {result} (期望: {expected})")
        
        print("\n2. 测试百度语音转换方法")
        # 模拟语音文件路径
        test_voice_path = "test_voice.wav"
        result = handler._convert_voice_with_baidu(test_voice_path, "测试用户")
        
        if result is None:
            print("✅ 百度语音转换方法正常（文件不存在时返回None）")
        else:
            print(f"⚠️ 意外的返回结果: {result}")
        
        print("\n3. 测试用户引导消息")
        guidance = handler._get_voice_guidance_message()
        print("引导消息内容:")
        print(f"  {guidance}")
        
        if "语音消息" in guidance and "文字" in guidance:
            print("✅ 引导消息内容正确")
        else:
            print("❌ 引导消息内容不完整")
        
        return True
        
    except Exception as e:
        print(f"❌ 微信集成测试失败: {e}")
        return False

def test_web_config():
    """测试Web配置页面"""
    print("\n🌐 测试Web配置页面")
    print("=" * 60)
    
    try:
        # 检查配置模板是否包含百度语音配置
        config_template_path = current_dir / "src" / "web" / "templates" / "config.html"
        
        if config_template_path.exists():
            with open(config_template_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            checks = [
                ("百度语音识别配置", "百度语音识别配置" in content),
                ("baidu_voice_enabled", "baidu_voice_enabled" in content),
                ("baidu_voice_api_key", "baidu_voice_api_key" in content),
                ("baidu_voice_secret_key", "baidu_voice_secret_key" in content),
                ("baidu_voice_app_id", "baidu_voice_app_id" in content),
                ("语言模型选择", "baidu_voice_dev_pid" in content),
                ("使用说明", "百度AI开放平台" in content)
            ]
            
            print("配置页面检查:")
            all_passed = True
            for check_name, passed in checks:
                status = "✅" if passed else "❌"
                print(f"  {status} {check_name}")
                if not passed:
                    all_passed = False
            
            if all_passed:
                print("✅ Web配置页面集成完成")
            else:
                print("❌ Web配置页面集成不完整")
            
            return all_passed
        else:
            print("❌ 配置模板文件不存在")
            return False
            
    except Exception as e:
        print(f"❌ Web配置测试失败: {e}")
        return False

def simulate_voice_message_flow():
    """模拟语音消息处理流程"""
    print("\n🎭 模拟语音消息处理流程")
    print("=" * 60)
    
    try:
        from src.bot.wechat_handler import WeChatHandler
        from config import config
        
        handler = WeChatHandler()
        
        # 模拟语音消息场景
        scenarios = [
            {
                "name": "语音占位符",
                "content": "[语音]3秒,未播放",
                "expected": "引导消息"
            },
            {
                "name": "语音文件路径",
                "content": "voice_001.amr",
                "expected": "百度识别或引导"
            },
            {
                "name": "有效文字",
                "content": "推荐一款手机",
                "expected": "正常处理"
            }
        ]
        
        print("语音消息处理流程模拟:")
        
        for scenario in scenarios:
            print(f"\n场景: {scenario['name']}")
            print(f"  输入: {scenario['content']}")
            print(f"  期望: {scenario['expected']}")
            
            # 检查是否为语音占位符
            is_placeholder = handler._is_voice_placeholder(scenario['content'])
            print(f"  占位符检测: {is_placeholder}")
            
            if is_placeholder:
                print(f"  处理结果: 发送引导消息")
            else:
                # 尝试百度语音识别
                baidu_result = handler._convert_voice_with_baidu(scenario['content'], "测试用户")
                if baidu_result:
                    print(f"  处理结果: 百度识别成功 - {baidu_result}")
                else:
                    print(f"  处理结果: 百度识别失败，发送引导消息")
        
        return True
        
    except Exception as e:
        print(f"❌ 流程模拟失败: {e}")
        return False

def main():
    """主函数"""
    print("🎤 百度语音识别集成测试")
    print()
    
    # 运行测试
    tests = [
        ("百度语音配置", test_baidu_voice_config),
        ("百度语音服务", test_baidu_voice_service),
        ("微信集成", test_wechat_integration),
        ("Web配置页面", test_web_config),
        ("语音消息流程", simulate_voice_message_flow)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n🔍 运行测试: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 总结
    print(f"\n" + "=" * 60)
    print(f"          百度语音识别集成测试总结")
    print(f"=" * 60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{status} {test_name}")
    
    print(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 百度语音识别集成完成！")
        print("\n💡 使用说明:")
        print("   1. 在Web配置页面填写百度语音识别的App ID和Secret Key")
        print("   2. 启用百度语音识别功能")
        print("   3. 重启机器人程序")
        print("   4. 发送语音消息测试")
        print("\n🔗 获取API密钥:")
        print("   访问 https://ai.baidu.com/tech/speech 创建应用")
    else:
        print("⚠️ 部分测试失败，请检查配置和代码")
        
        failed_tests = [name for name, result in results if not result]
        if failed_tests:
            print(f"\n需要检查的测试:")
            for test_name in failed_tests:
                print(f"   - {test_name}")

if __name__ == "__main__":
    main()
