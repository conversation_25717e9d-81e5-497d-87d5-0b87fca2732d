#!/usr/bin/env python3
"""
测试Web界面AI状态显示
"""
import requests

def test_web_ai_status():
    """测试Web界面AI状态"""
    try:
        session = requests.Session()
        
        # 登录
        login_data = {'password': 'admin123'}
        login_response = session.post('http://localhost:5000/login', data=login_data)
        print(f"登录状态: {login_response.status_code}")
        
        if login_response.status_code == 200:
            # 访问仪表板页面
            dashboard_response = session.get('http://localhost:5000/')
            print(f"仪表板页面状态: {dashboard_response.status_code}")
            
            if dashboard_response.status_code == 200:
                content = dashboard_response.text
                
                # 检查AI状态显示
                if "AI服务" in content:
                    print("✅ 找到AI服务状态显示")
                    
                    if "可用" in content or "正常" in content:
                        print("✅ AI服务显示为可用状态")
                    elif "不可用" in content or "错误" in content:
                        print("⚠️ AI服务显示为不可用状态")
                    else:
                        print("❓ AI服务状态不明确")
                        
                    # 检查模型信息
                    if "Qwen/Qwen2.5-72B-Instruct" in content:
                        print("✅ 显示了正确的模型名称")
                    elif "模型" in content:
                        print("⚠️ 显示了模型信息，但可能不是最新的")
                    
                else:
                    print("❌ 未找到AI服务状态显示")
                    
                # 检查配置页面
                config_response = session.get('http://localhost:5000/config')
                print(f"配置页面状态: {config_response.status_code}")
                
                if config_response.status_code == 200:
                    config_content = config_response.text
                    if "Qwen/Qwen2.5-72B-Instruct" in config_content:
                        print("✅ 配置页面显示了正确的模型名称")
                    else:
                        print("⚠️ 配置页面可能未显示最新模型")
                        
            else:
                print(f"❌ 仪表板页面访问失败: {dashboard_response.status_code}")
        else:
            print("❌ 登录失败")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")

if __name__ == "__main__":
    print("🌐 测试Web界面AI状态显示")
    print("=" * 50)
    test_web_ai_status()
