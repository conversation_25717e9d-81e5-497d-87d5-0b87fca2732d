#!/usr/bin/env python3
"""
验证硅基流动配置是否正确
"""
import os
import sys
from pathlib import Path

# 添加项目路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def verify_config():
    """验证配置"""
    print("🔧 验证硅基流动配置")
    print("=" * 60)
    
    try:
        from config import config
        
        print("当前AI配置:")
        print(f"  API密钥: {'已配置' if config.ai.api_key else '未配置'}")
        print(f"  基础URL: {config.ai.base_url}")
        print(f"  模型: {config.ai.model}")
        print(f"  最大令牌: {config.ai.max_tokens}")
        print(f"  温度: {config.ai.temperature}")
        print(f"  AI启用: {config.ai.enabled}")
        
        # 检查是否是硅基流动配置
        is_siliconflow = "siliconflow.cn" in config.ai.base_url
        is_qwen_model = "Qwen" in config.ai.model
        
        print(f"\n配置检查:")
        if is_siliconflow:
            print("✅ API地址正确 (硅基流动)")
        else:
            print(f"❌ API地址错误: {config.ai.base_url}")
            print("   应该是: https://api.siliconflow.cn/v1")
        
        if is_qwen_model:
            print("✅ 模型正确 (Qwen)")
        else:
            print(f"❌ 模型错误: {config.ai.model}")
            print("   应该是: Qwen/Qwen2.5-7B-Instruct")
        
        # 检查其他配置
        print(f"\n其他配置:")
        print(f"  微信自动回复: {config.wechat.auto_reply}")
        print(f"  回复延迟: {config.wechat.reply_delay}秒")
        print(f"  AI回退: {config.reply.use_ai_fallback}")
        print(f"  FAQ文件: {config.database.faq_file}")
        print(f"  产品文件: {config.database.products_file}")
        
        return is_siliconflow and is_qwen_model
        
    except Exception as e:
        print(f"❌ 配置验证失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_config_files():
    """检查配置文件"""
    print("\n📄 检查配置文件")
    print("=" * 60)
    
    try:
        import json
        
        # 检查JSON配置文件
        config_file = current_dir / "config" / "config.json"
        
        if config_file.exists():
            with open(config_file, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
            
            print("JSON配置文件内容:")
            ai_config = config_data.get('ai', {})
            print(f"  base_url: {ai_config.get('base_url')}")
            print(f"  model: {ai_config.get('model')}")
            print(f"  enabled: {ai_config.get('enabled')}")
            
            # 验证JSON配置
            json_correct = (
                "siliconflow.cn" in ai_config.get('base_url', '') and
                "Qwen" in ai_config.get('model', '')
            )
            
            if json_correct:
                print("✅ JSON配置文件正确")
            else:
                print("❌ JSON配置文件需要修复")
            
            return json_correct
        else:
            print("❌ 配置文件不存在")
            return False
            
    except Exception as e:
        print(f"❌ 配置文件检查失败: {e}")
        return False

def test_ai_import():
    """测试AI服务导入"""
    print("\n🤖 测试AI服务导入")
    print("=" * 60)
    
    try:
        from src.ai.llm_service import LLMService
        print("✅ AI服务模块导入成功")
        
        # 尝试创建AI服务实例（不实际调用API）
        llm_service = LLMService(
            api_key="test_key",
            base_url="https://api.siliconflow.cn/v1",
            model="Qwen/Qwen2.5-7B-Instruct",
            max_tokens=1000,
            temperature=0.7,
            system_prompt="测试提示词"
        )
        print("✅ AI服务实例创建成功")
        
        return True
        
    except Exception as e:
        print(f"❌ AI服务测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def show_usage_instructions():
    """显示使用说明"""
    print("\n📋 使用说明")
    print("=" * 60)
    
    print("要启用AI聊天功能，请按以下步骤操作:")
    print()
    print("1. 获取硅基流动API密钥:")
    print("   - 访问: https://siliconflow.cn")
    print("   - 注册账号并获取API密钥")
    print()
    print("2. 配置API密钥:")
    print("   - 编辑 config/config.json 文件")
    print("   - 将 'api_key' 设置为您的密钥")
    print("   - 或设置环境变量 SILICONFLOW_API_KEY")
    print()
    print("3. 验证配置:")
    print("   - 重启微信机器人")
    print("   - 发送 '你好' 测试AI回复")
    print()
    print("4. 当前配置已正确设置为:")
    print("   - API地址: https://api.siliconflow.cn/v1")
    print("   - 模型: Qwen/Qwen2.5-7B-Instruct")
    print("   - 系统提示词: 拟人化销售顾问")

def main():
    """主函数"""
    print("🚀 硅基流动配置验证")
    print()
    
    # 运行验证
    tests = [
        ("配置验证", verify_config),
        ("配置文件检查", check_config_files),
        ("AI服务测试", test_ai_import)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n🔍 运行: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
            if result:
                print(f"✅ {test_name} 通过")
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
            results.append((test_name, False))
    
    # 总结
    print(f"\n" + "=" * 60)
    print(f"          验证总结")
    print(f"=" * 60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{status} {test_name}")
    
    print(f"\n总计: {passed}/{total} 个验证通过")
    
    if passed == total:
        print("🎉 硅基流动配置完全正确！")
        print("💡 只需要添加API密钥即可启用AI聊天功能")
    else:
        print("⚠️ 配置存在问题，请检查上述错误")
    
    # 显示使用说明
    show_usage_instructions()

if __name__ == "__main__":
    main()
