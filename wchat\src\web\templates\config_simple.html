<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>基础配置测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input, textarea, select { width: 100%; padding: 8px; margin-bottom: 10px; }
        .radio-group { display: flex; gap: 20px; }
        .radio-group label { display: inline; font-weight: normal; }
        button { background: #007bff; color: white; padding: 10px 20px; border: none; cursor: pointer; }
        button:hover { background: #0056b3; }
    </style>
</head>
<body>
    <h1>基础配置测试</h1>
    
    <form>
        <div class="form-group">
            <label>监听模式</label>
            <div class="radio-group">
                <label>
                    <input type="radio" name="listen_mode" value="list" {% if not config.wechat.listen_all %}checked{% endif %}>
                    指定列表监听
                </label>
                <label>
                    <input type="radio" name="listen_mode" value="all" {% if config.wechat.listen_all %}checked{% endif %}>
                    监听所有消息
                </label>
            </div>
        </div>
        
        <div class="form-group">
            <label for="listen_list">监听列表</label>
            <textarea id="listen_list" rows="4" placeholder="每行一个聊天对象名称">{{ '\n'.join(config.wechat.listen_list) }}</textarea>
        </div>
        
        <div class="form-group">
            <label>
                <input type="checkbox" {% if config.wechat.auto_reply %}checked{% endif %}>
                启用自动回复
            </label>
        </div>
        
        <div class="form-group">
            <label for="reply_delay">回复延迟（秒）</label>
            <input type="number" id="reply_delay" value="{{ config.wechat.reply_delay }}" min="0" max="60">
        </div>
        
        <button type="button">保存配置</button>
    </form>
    
    <h2>调试信息</h2>
    <pre>
配置对象: {{ config }}
系统配置: {{ config.wechat }}
监听列表: {{ config.wechat.listen_list }}
监听所有: {{ config.wechat.listen_all }}
自动回复: {{ config.wechat.auto_reply }}
回复延迟: {{ config.wechat.reply_delay }}
    </pre>
</body>
</html>
