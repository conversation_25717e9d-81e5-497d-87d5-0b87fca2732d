"""
测试消息监听功能
验证修复后的消息监听是否正常工作
"""
import os
import sys
import time
import threading

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def test_message_listening():
    """测试消息监听"""
    print("=" * 50)
    print("测试消息监听功能")
    print("=" * 50)
    
    try:
        from src.bot.wechat_handler import WeChatHandler
        
        # 创建处理器
        handler = WeChatHandler()
        
        # 初始化微信
        if not handler.initialize_wechat():
            print("❌ 微信初始化失败")
            return False
        
        print("✅ 微信初始化成功")
        
        # 获取状态
        status = handler.get_status()
        print(f"监听列表: {status['listen_list']}")
        print(f"自动回复: {status['auto_reply']}")
        
        # 开始监听
        print("\n开始监听消息（30秒）...")
        print("请在微信中向监听对象发送测试消息")
        print("例如：向'文件传输助手'发送'如何退货'")
        
        handler.start_listening()
        
        # 等待30秒
        time.sleep(30)
        
        # 停止监听
        handler.stop_listening()
        print("\n监听测试完成")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_manual_polling():
    """测试手动轮询消息"""
    print("\n" + "=" * 50)
    print("测试手动轮询消息")
    print("=" * 50)
    
    try:
        from wxauto import WeChat
        from src.bot.reply_engine import ReplyEngine
        
        wx = WeChat()
        engine = ReplyEngine()
        
        print(f"微信连接成功，用户: {wx.nickname}")
        
        # 测试聊天对象
        test_chats = ["文件传输助手"]
        
        print(f"\n开始轮询监听（20秒）...")
        print("请向以下聊天对象发送测试消息:")
        for chat in test_chats:
            print(f"  - {chat}")
        
        start_time = time.time()
        processed_messages = []
        
        while time.time() - start_time < 20:
            for chat_name in test_chats:
                try:
                    # 切换到聊天
                    if wx.ChatWith(chat_name):
                        # 获取新消息
                        msgs = wx.GetNewMessage()
                        if msgs:
                            for msg in msgs:
                                # 避免重复处理
                                msg_id = f"{chat_name}_{getattr(msg, 'time', time.time())}"
                                if msg_id in processed_messages:
                                    continue
                                processed_messages.append(msg_id)
                                
                                # 解析消息
                                sender = getattr(msg, 'sender', '')
                                content = getattr(msg, 'content', '')
                                msg_type = type(msg).__name__
                                
                                print(f"\n收到消息:")
                                print(f"  聊天: {chat_name}")
                                print(f"  发送者: {sender}")
                                print(f"  内容: {content}")
                                print(f"  类型: {msg_type}")
                                
                                # 如果不是自己发送的消息，生成回复
                                if sender != "self" and content and 'Text' in msg_type:
                                    reply = engine.generate_reply(content)
                                    print(f"  生成回复: {reply[:100]}...")
                                    
                                    # 发送回复（可选）
                                    if input("  是否发送回复？(y/n): ").lower() == 'y':
                                        wx.SendMsg(reply)
                                        print("  ✅ 回复已发送")
                                
                except Exception as e:
                    print(f"处理聊天 {chat_name} 失败: {e}")
            
            time.sleep(1)
        
        print(f"\n轮询测试完成，共处理 {len(processed_messages)} 条消息")
        return True
        
    except Exception as e:
        print(f"❌ 手动轮询测试失败: {e}")
        return False


def test_simple_monitoring():
    """简单监控测试"""
    print("\n" + "=" * 50)
    print("简单监控测试")
    print("=" * 50)
    
    try:
        from wxauto import WeChat
        
        wx = WeChat()
        print(f"微信连接成功，用户: {wx.nickname}")
        
        print("\n监控所有新消息（15秒）...")
        start_time = time.time()
        
        while time.time() - start_time < 15:
            try:
                msgs = wx.GetNewMessage()
                if msgs:
                    print(f"\n获取到 {len(msgs)} 条新消息:")
                    for i, msg in enumerate(msgs):
                        sender = getattr(msg, 'sender', 'Unknown')
                        content = getattr(msg, 'content', 'No content')
                        msg_type = type(msg).__name__
                        
                        print(f"  {i+1}. [{msg_type}] {sender}: {content}")
                
                time.sleep(2)
                
            except Exception as e:
                print(f"获取消息失败: {e}")
                time.sleep(5)
        
        print("\n简单监控测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 简单监控测试失败: {e}")
        return False


def main():
    """主函数"""
    print("微信消息监听测试")
    print("=" * 50)
    
    print("选择测试模式:")
    print("1. 完整监听测试（使用WeChatHandler）")
    print("2. 手动轮询测试（交互式）")
    print("3. 简单监控测试（观察所有消息）")
    
    choice = input("\n请输入选择 (1-3): ").strip()
    
    if choice == "1":
        test_message_listening()
    elif choice == "2":
        test_manual_polling()
    elif choice == "3":
        test_simple_monitoring()
    else:
        print("无效选择")


if __name__ == "__main__":
    main()
