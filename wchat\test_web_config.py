#!/usr/bin/env python3
"""
测试Web配置页面
"""
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
current_dir = Path(__file__).parent
project_root = current_dir
sys.path.insert(0, str(project_root))

try:
    from config import config
    print("✅ 配置加载成功")
    
    # 测试模板渲染
    from jinja2 import Template
    
    template_content = """
    监听模式测试:
    listen_all = {{ config.wechat.listen_all }}
    not listen_all = {{ not config.wechat.listen_all }}
    
    单选按钮测试:
    {% if not config.wechat.listen_all %}checked{% endif %}
    {% if config.wechat.listen_all %}checked{% endif %}
    """
    
    template = Template(template_content)
    result = template.render(config=config)
    print("✅ 模板渲染成功:")
    print(result)
    
except Exception as e:
    print(f"❌ 测试失败: {e}")
    import traceback
    traceback.print_exc()
