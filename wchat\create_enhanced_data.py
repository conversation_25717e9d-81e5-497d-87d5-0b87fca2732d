"""
创建增强的产品和FAQ数据结构
支持关键词匹配和图片
"""
import os
import pandas as pd

def create_enhanced_faq():
    """创建增强的FAQ数据"""
    faq_data = [
        {
            "问题关键词": "退货,退款,退换,申请退货,怎么退",
            "标准问题": "如何申请退货退款？",
            "回复内容": "您可以在订单页面点击'申请退货'，或联系客服办理退货退款。退货商品需保持原包装完整，7天内可无理由退货。",
            "分类": "售后服务",
            "状态": "启用"
        },
        {
            "问题关键词": "发货,物流,快递,什么时候发货,多久发货",
            "标准问题": "什么时候发货？",
            "回复内容": "我们会在您付款后24小时内安排发货，节假日可能会有延迟。您可以在订单详情中查看物流信息。",
            "分类": "物流配送",
            "状态": "启用"
        },
        {
            "问题关键词": "价格,优惠,折扣,活动,促销,便宜",
            "标准问题": "有什么优惠活动吗？",
            "回复内容": "我们经常有各种优惠活动，建议您关注我们的官方公众号获取最新优惠信息。新用户首单可享受9折优惠。",
            "分类": "优惠活动",
            "状态": "启用"
        },
        {
            "问题关键词": "质量,品质,保修,保证,质保",
            "标准问题": "产品质量如何保证？",
            "回复内容": "我们所有产品均经过严格质检，提供1年质保服务。如有质量问题，支持免费维修或更换。",
            "分类": "产品质量",
            "状态": "启用"
        },
        {
            "问题关键词": "支付,付款,微信,支付宝,银行卡",
            "标准问题": "支持哪些付款方式？",
            "回复内容": "我们支持微信支付、支付宝、银行卡等多种付款方式，支付安全便捷。",
            "分类": "支付方式",
            "状态": "启用"
        },
        {
            "问题关键词": "客服,联系,人工,电话,在线客服",
            "标准问题": "如何联系客服？",
            "回复内容": "您可以通过以下方式联系我们：\n- 在线客服：工作日9:00-18:00\n- 客服电话：400-123-4567\n- 客服邮箱：<EMAIL>",
            "分类": "客服服务",
            "状态": "启用"
        }
    ]
    
    df = pd.DataFrame(faq_data)
    faq_file = os.path.join(os.path.dirname(__file__), 'data', 'faq_enhanced.xlsx')
    df.to_excel(faq_file, index=False)
    print(f"✅ 增强FAQ数据已保存到: {faq_file}")
    return df

def create_enhanced_products():
    """创建增强的产品数据"""
    products_data = [
        {
            "产品名称": "智能手机A1",
            "产品关键词": "手机,智能手机,A1,通话,拍照,安卓",
            "产品描述": "6.1寸全面屏，128GB存储，5000mAh大电池，支持快充。高清摄像头，流畅运行体验。",
            "价格": 2999.0,
            "分类": "数码产品",
            "产品图片": "images/phone_a1.jpg",
            "详细信息": "屏幕：6.1寸OLED\n存储：128GB\n电池：5000mAh\n摄像头：4800万像素\n系统：Android 13",
            "库存状态": "有货",
            "状态": "上架"
        },
        {
            "产品名称": "无线蓝牙耳机B2",
            "产品关键词": "耳机,蓝牙耳机,无线耳机,B2,音乐,降噪",
            "产品描述": "降噪蓝牙耳机，30小时续航，IPX7防水。高品质音效，舒适佩戴。",
            "价格": 399.0,
            "分类": "音频设备",
            "产品图片": "images/earphone_b2.jpg",
            "详细信息": "续航：30小时\n防水：IPX7\n降噪：主动降噪\n连接：蓝牙5.0\n重量：5g",
            "库存状态": "有货",
            "状态": "上架"
        },
        {
            "产品名称": "智能手表C3",
            "产品关键词": "手表,智能手表,C3,运动,健康,监测",
            "产品描述": "健康监测，运动追踪，7天续航，支持通话。全天候健康守护。",
            "价格": 1299.0,
            "分类": "智能穿戴",
            "产品图片": "images/watch_c3.jpg",
            "详细信息": "屏幕：1.4寸彩屏\n续航：7天\n防水：5ATM\n传感器：心率、血氧\n功能：通话、支付",
            "库存状态": "有货",
            "状态": "上架"
        },
        {
            "产品名称": "笔记本电脑D4",
            "产品关键词": "笔记本,电脑,D4,办公,学习,轻薄",
            "产品描述": "轻薄本，14寸屏幕，16GB内存，512GB固态硬盘。高效办公，便携设计。",
            "价格": 5999.0,
            "分类": "电脑设备",
            "产品图片": "images/laptop_d4.jpg",
            "详细信息": "屏幕：14寸2K\n处理器：Intel i7\n内存：16GB\n存储：512GB SSD\n重量：1.3kg",
            "库存状态": "有货",
            "状态": "上架"
        },
        {
            "产品名称": "无线充电器E5",
            "产品关键词": "充电器,无线充电,E5,快充,充电板",
            "产品描述": "15W快速无线充电，支持多种设备，带LED指示灯。安全便捷充电。",
            "价格": 99.0,
            "分类": "充电配件",
            "产品图片": "images/charger_e5.jpg",
            "详细信息": "功率：15W\n兼容：iPhone/Android\n指示灯：LED\n材质：铝合金\n认证：Qi标准",
            "库存状态": "有货",
            "状态": "上架"
        },
        {
            "产品名称": "游戏鼠标F6",
            "产品关键词": "鼠标,游戏鼠标,F6,游戏,电竞,RGB",
            "产品描述": "电竞游戏鼠标，12000DPI，RGB灯效，人体工学设计。游戏利器。",
            "价格": 199.0,
            "分类": "电脑配件",
            "产品图片": "images/mouse_f6.jpg",
            "详细信息": "DPI：12000\n按键：7个可编程\n灯效：RGB\n连接：有线USB\n重量：85g",
            "库存状态": "有货",
            "状态": "上架"
        }
    ]
    
    df = pd.DataFrame(products_data)
    products_file = os.path.join(os.path.dirname(__file__), 'data', 'products_enhanced.xlsx')
    df.to_excel(products_file, index=False)
    print(f"✅ 增强产品数据已保存到: {products_file}")
    return df

def create_images_folder():
    """创建图片文件夹"""
    images_dir = os.path.join(os.path.dirname(__file__), 'data', 'images')
    os.makedirs(images_dir, exist_ok=True)
    
    # 创建示例图片占位符
    placeholder_files = [
        'phone_a1.jpg',
        'earphone_b2.jpg', 
        'watch_c3.jpg',
        'laptop_d4.jpg',
        'charger_e5.jpg',
        'mouse_f6.jpg'
    ]
    
    for filename in placeholder_files:
        filepath = os.path.join(images_dir, filename)
        if not os.path.exists(filepath):
            # 创建占位符文件
            with open(filepath, 'w') as f:
                f.write(f"# 产品图片占位符: {filename}\n")
                f.write("# 请替换为实际的产品图片文件\n")
    
    print(f"✅ 图片文件夹已创建: {images_dir}")
    print("📷 请将实际产品图片放入该文件夹")

def main():
    """主函数"""
    print("创建增强的数据结构")
    print("=" * 50)
    
    # 创建增强的FAQ数据
    faq_df = create_enhanced_faq()
    print(f"FAQ数据: {len(faq_df)} 条记录")
    
    # 创建增强的产品数据
    products_df = create_enhanced_products()
    print(f"产品数据: {len(products_df)} 条记录")
    
    # 创建图片文件夹
    create_images_folder()
    
    print("\n" + "=" * 50)
    print("数据结构增强完成！")
    print("=" * 50)
    print("新增功能:")
    print("✅ 产品关键词匹配")
    print("✅ 产品图片支持")
    print("✅ 详细产品信息")
    print("✅ 库存状态管理")
    print("✅ 分类管理")

if __name__ == "__main__":
    main()
