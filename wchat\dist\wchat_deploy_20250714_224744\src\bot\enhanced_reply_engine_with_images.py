#!/usr/bin/env python3
"""
增强的回复引擎（支持图片）
支持FAQ、产品库和AI回复的智能组合，包含产品图片发送功能
"""
import os
import sys
import logging
from typing import Dict, Optional, Tuple, List

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.join(current_dir, '..', '..')
sys.path.insert(0, project_root)

from src.database.enhanced_reader import EnhancedFAQReader, EnhancedProductReader
from src.bot.enhanced_product_image_handler import EnhancedProductImageHandler
from src.utils.logger import get_logger

logger = get_logger(__name__)

class EnhancedReplyEngineWithImages:
    """增强的回复引擎（支持图片）"""
    
    def __init__(self):
        self.data_dir = os.path.join(project_root, 'data')
        
        # 初始化数据读取器
        faq_file = os.path.join(self.data_dir, 'faq.xlsx')
        products_file = os.path.join(self.data_dir, 'products.xlsx')
        
        self.faq_reader = EnhancedFAQReader(faq_file)
        self.product_reader = EnhancedProductReader(products_file)
        self.image_handler = EnhancedProductImageHandler()
        
        # 产品相关关键词
        self.product_keywords = [
            '手机', '耳机', '电脑', '笔记本', '鼠标', '键盘', '充电器', '手表',
            '推荐', '价格', '多少钱', '怎么样', '好用吗', '性能', '配置',
            '买', '购买', '选择', '对比', '哪个好', '哪款'
        ]
        
        logger.info("增强回复引擎（支持图片）初始化完成")
    
    def generate_reply_with_images(self, message: str, sender: str = "", context: Dict = None) -> Tuple[str, List[str]]:
        """
        生成回复（包含图片）
        
        Returns:
            Tuple[str, List[str]]: (文本回复, 图片路径列表)
        """
        try:
            message = message.strip()
            if not message:
                return "您好！有什么可以帮助您的吗？", []
            
            logger.info(f"处理消息: {message}")
            
            # 1. 首先尝试FAQ匹配
            faq_result = self.faq_reader.search_faq(message)
            if faq_result and faq_result['score'] > 0.7:
                logger.info(f"FAQ匹配成功: {faq_result['question']} (分数: {faq_result['score']:.2f})")
                return faq_result['answer'], []
            
            # 2. 检查是否是产品相关查询
            if self._is_product_query(message):
                # 首先尝试精确匹配
                products = self.product_reader.search_products_exact(message)

                # 如果精确匹配没有结果，再尝试模糊匹配
                if not products:
                    products = self.product_reader.search_products(message)

                if products:
                    logger.info(f"产品匹配成功: 找到 {len(products)} 个产品")
                    # 限制推荐数量，避免刷屏
                    display_products = products[:2]
                    
                    # 使用增强的图片处理器生成回复和图片
                    reply, image_paths = self.image_handler.format_product_reply_with_images(display_products)
                    
                    logger.info(f"产品回复生成: 文本 {len(reply)} 字符, 图片 {len(image_paths)} 张")
                    return reply, image_paths
            
            # 3. 如果FAQ有低分匹配，使用它
            if faq_result and faq_result['score'] > 0.5:
                logger.info(f"FAQ低分匹配: {faq_result['question']} (分数: {faq_result['score']:.2f})")
                return faq_result['answer'], []
            
            # 4. 尝试AI回复（如果配置了的话）
            try:
                from config import config
                if config.reply.use_ai_fallback and config.ai.enabled and config.ai.api_key:
                    from src.ai.llm_service import LLMService

                    llm_service = LLMService(
                        api_key=config.ai.api_key,
                        base_url=config.ai.base_url,
                        model=config.ai.model,
                        max_tokens=config.ai.max_tokens,
                        temperature=config.ai.temperature,
                        system_prompt=config.ai.system_prompt
                    )

                    ai_reply = llm_service.generate_reply(message, sender)
                    if ai_reply and ai_reply.strip():
                        logger.info("使用AI回复")
                        return ai_reply, []
            except Exception as e:
                logger.debug(f"AI回复失败: {e}")

            # 5. 无法回复时保持沉默，让人工处理
            logger.info("无法匹配，保持沉默")
            return "", []  # 返回空字符串，不回复
            
        except Exception as e:
            logger.error(f"生成回复失败: {e}")
            return "", []  # 异常时也保持沉默，让人工处理
    
    def generate_reply(self, message: str, sender: str = "", context: Dict = None) -> str:
        """
        生成回复（仅文本，兼容原有接口）
        """
        reply, _ = self.generate_reply_with_images(message, sender, context)
        return reply
    
    def _is_product_query(self, message: str) -> bool:
        """判断是否是产品查询"""
        message_lower = message.lower()
        
        # 检查产品关键词
        for keyword in self.product_keywords:
            if keyword in message_lower:
                return True
        
        # 检查是否包含具体产品名称
        if self.product_reader.data is not None and not self.product_reader.data.empty:
            for _, row in self.product_reader.data.iterrows():
                product_name = str(row['产品名称']).lower()
                
                if product_name in message_lower:
                    return True
                
                # 检查产品关键词（如果有的话）
                if '产品关键词' in row:
                    keywords = str(row['产品关键词']).lower().split(',')
                    for keyword in keywords:
                        keyword = keyword.strip()
                        if keyword and keyword in message_lower:
                            return True
        
        return False
    
    def get_product_images(self, message: str) -> List[str]:
        """
        获取产品相关的图片路径列表
        """
        try:
            if self._is_product_query(message):
                products = self.product_reader.search_products(message)
                if products:
                    # 限制图片数量
                    display_products = products[:2]
                    return self.image_handler.get_products_images(display_products)
        except Exception as e:
            logger.error(f"获取产品图片失败: {e}")
        
        return []
    
    def reload_data(self):
        """重新加载数据"""
        try:
            self.faq_reader.load_data()
            self.product_reader.load_data()
            logger.info("数据重新加载完成")
        except Exception as e:
            logger.error(f"数据重新加载失败: {e}")

# 为了兼容性，创建一个全局实例
_engine_instance = None

def get_enhanced_reply_engine_with_images():
    """获取增强回复引擎实例（单例模式）"""
    global _engine_instance
    if _engine_instance is None:
        _engine_instance = EnhancedReplyEngineWithImages()
    return _engine_instance
