"""
微信客服机器人主程序
"""
import os
import sys
import signal
import time
from pathlib import Path

# 添加项目根目录到Python路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

from src.bot.wechat_handler import WeChatHandler
from src.utils.logger import get_logger
from config import config

logger = get_logger("main")

# 全局变量
wechat_handler = None
running = True


def signal_handler(signum, frame):
    """信号处理器"""
    global running, wechat_handler
    
    logger.info("收到退出信号，正在关闭程序...")
    running = False
    
    if wechat_handler:
        wechat_handler.stop_listening()
    
    logger.info("程序已退出")
    sys.exit(0)


def check_dependencies():
    """检查依赖和配置"""
    logger.info("检查依赖和配置...")
    
    # 检查必要的目录
    data_dir = current_dir / "data"
    if not data_dir.exists():
        data_dir.mkdir(parents=True)
        logger.info("创建data目录")
    
    logs_dir = data_dir / "logs"
    if not logs_dir.exists():
        logs_dir.mkdir(parents=True)
        logger.info("创建logs目录")
    
    # 检查Excel文件
    faq_file = current_dir / config.database.faq_file
    products_file = current_dir / config.database.products_file
    
    if not faq_file.exists():
        logger.warning(f"FAQ文件不存在: {faq_file}")
        logger.info("请运行 python data/create_sample_data.py 创建示例数据")
    
    if not products_file.exists():
        logger.warning(f"产品文件不存在: {products_file}")
        logger.info("请运行 python data/create_sample_data.py 创建示例数据")
    
    # 检查监听列表
    if not config.wechat.listen_list:
        logger.warning("监听列表为空，请在配置中添加要监听的聊天对象")
    
    # 检查AI配置
    if config.ai.enabled and not config.ai.api_key:
        logger.warning("AI功能已启用但未配置API密钥")
    
    logger.info("依赖检查完成")


def print_banner():
    """打印启动横幅"""
    banner = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                    微信客服回复机器人 (WChat)                    ║
    ║                                                              ║
    ║  功能特点:                                                    ║
    ║  • 智能FAQ问答                                               ║
    ║  • 产品信息推荐                                              ║
    ║  • AI辅助回复                                                ║
    ║  • Web配置界面                                               ║
    ║                                                              ║
    ║  基于KouriChat项目简化开发                                    ║
    ╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)


def print_status():
    """打印状态信息"""
    logger.info("=" * 60)
    logger.info("微信客服机器人状态")
    logger.info("=" * 60)
    
    # 配置信息
    logger.info(f"监听列表: {config.wechat.listen_list}")
    logger.info(f"自动回复: {'启用' if config.wechat.auto_reply else '禁用'}")
    logger.info(f"回复延迟: {config.wechat.reply_delay}秒")
    
    # 数据库信息
    logger.info(f"FAQ文件: {config.database.faq_file}")
    logger.info(f"产品文件: {config.database.products_file}")
    logger.info(f"相似度阈值: {config.database.similarity_threshold}")
    
    # AI信息
    if config.ai.enabled:
        logger.info(f"AI模型: {config.ai.model}")
        logger.info(f"API地址: {config.ai.base_url}")
        logger.info(f"API密钥: {'已配置' if config.ai.api_key else '未配置'}")
    else:
        logger.info("AI功能: 已禁用")
    
    logger.info("=" * 60)


def main():
    """主函数"""
    global wechat_handler, running
    
    # 注册信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    try:
        # 打印启动信息
        print_banner()
        logger.info("启动微信客服机器人...")
        
        # 检查依赖
        check_dependencies()
        
        # 打印状态
        print_status()
        
        # 创建微信处理器
        wechat_handler = WeChatHandler()
        
        # 开始监听
        logger.info("正在启动微信消息监听...")
        wechat_handler.start_listening()
        
        if wechat_handler.running:
            logger.info("✅ 微信客服机器人启动成功！")
            logger.info("💡 提示:")
            logger.info("   - 按 Ctrl+C 退出程序")
            logger.info("   - 运行 python web_config.py 打开配置界面")
            logger.info("   - 确保微信PC版保持登录状态")
            
            # 主循环
            while running and wechat_handler.running:
                try:
                    time.sleep(1)
                except KeyboardInterrupt:
                    break
        else:
            logger.error("❌ 微信客服机器人启动失败！")
            logger.error("请检查:")
            logger.error("   1. 微信PC版是否已登录")
            logger.error("   2. 监听列表是否正确配置")
            logger.error("   3. 网络连接是否正常")
    
    except Exception as e:
        logger.error(f"程序运行异常: {e}")
    
    finally:
        # 清理资源
        if wechat_handler:
            wechat_handler.stop_listening()
        
        logger.info("程序已退出")


if __name__ == "__main__":
    main()
