"""
简化的机器人启动脚本
绕过模块导入问题
"""
import os
import sys
import time
import threading
import json

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def load_config():
    """加载配置"""
    config_path = os.path.join(current_dir, 'config', 'config.json')
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"加载配置失败: {e}")
        return None

def test_imports():
    """测试模块导入"""
    print("测试模块导入...")
    
    try:
        import colorlog
        print("✅ colorlog 导入成功")
    except ImportError as e:
        print(f"❌ colorlog 导入失败: {e}")
        return False
    
    try:
        from wxauto import WeChat
        print("✅ wxauto 导入成功")
    except ImportError as e:
        print(f"❌ wxauto 导入失败: {e}")
        return False
    
    try:
        import pandas
        print("✅ pandas 导入成功")
    except ImportError as e:
        print(f"❌ pandas 导入失败: {e}")
        return False
    
    try:
        import jieba
        print("✅ jieba 导入成功")
    except ImportError as e:
        print(f"❌ jieba 导入失败: {e}")
        return False
    
    return True

def simple_bot():
    """简化的机器人"""
    print("\n" + "=" * 50)
    print("启动简化版微信客服机器人")
    print("=" * 50)
    
    # 加载配置
    config = load_config()
    if not config:
        return
    
    listen_list = config.get('wechat', {}).get('listen_list', [])
    listen_all = config.get('wechat', {}).get('listen_all', False)
    auto_reply = config.get('wechat', {}).get('auto_reply', True)
    reply_delay = config.get('wechat', {}).get('reply_delay', 2)

    print(f"监听模式: {'全部消息' if listen_all else '指定列表'}")
    if not listen_all:
        print(f"监听列表: {listen_list}")
    print(f"自动回复: {auto_reply}")
    print(f"回复延迟: {reply_delay}秒")
    
    try:
        from wxauto import WeChat
        
        # 连接微信
        wx = WeChat()
        print(f"\n✅ 微信连接成功，用户: {wx.nickname}")
        
        # 简化的FAQ数据
        faq_data = {
            "如何退货": "您可以在订单页面点击'申请退货'，或联系客服办理退货退款。退货商品需保持原包装完整，7天内可无理由退货。",
            "什么时候发货": "我们会在您付款后24小时内安排发货，节假日可能会有延迟。您可以在订单详情中查看物流信息。",
            "有什么优惠": "我们经常有各种优惠活动，建议您关注我们的官方公众号获取最新优惠信息。新用户首单可享受9折优惠。",
            "联系客服": "您可以通过以下方式联系我们：\n- 在线客服：工作日9:00-18:00\n- 客服电话：400-123-4567\n- 客服邮箱：<EMAIL>",
            "退款": "退款将在我们收到退货商品后3-5个工作日内处理，退款金额将原路返回到您的付款账户。",
            "物流": "我们支持全国包邮，一般3-7个工作日送达。您可以在订单页面查看详细的物流跟踪信息。"
        }
        
        def find_answer(question):
            """查找答案"""
            question = question.strip()
            
            # 精确匹配
            if question in faq_data:
                return faq_data[question]
            
            # 模糊匹配
            for key, value in faq_data.items():
                if any(word in question for word in key.split()) or any(word in key for word in question.split()):
                    return value
            
            return "抱歉，我暂时无法理解您的问题。您可以尝试问我关于退货、发货、优惠等问题，或联系人工客服获得帮助。"
        
        print("\n开始监听消息...")
        print("按 Ctrl+C 停止机器人")
        
        processed_messages = set()
        
        try:
            while True:
                # 获取新消息
                msgs = wx.GetNewMessage()
                if msgs:
                    for msg in msgs:
                        try:
                            sender = getattr(msg, 'sender', '')
                            content = getattr(msg, 'content', '')
                            msg_type = type(msg).__name__
                            msg_time = getattr(msg, 'time', time.time())
                            
                            # 创建消息ID避免重复处理
                            msg_id = f"{sender}_{msg_time}_{content[:20]}"
                            if msg_id in processed_messages:
                                continue
                            processed_messages.add(msg_id)
                            
                            # 过滤消息
                            if sender == "self" or 'Self' in msg_type:
                                continue
                            
                            if 'System' in msg_type:
                                continue
                            
                            if not content or not sender:
                                continue
                            
                            # 检查是否应该处理消息
                            should_process = False

                            if listen_all:
                                # 全部监听模式：处理所有消息（除了自己的和系统消息）
                                if sender not in ['self', wx.nickname, 'system', 'System', '']:
                                    should_process = True
                            elif not listen_list:
                                # 监听列表为空：处理所有消息（除了自己的）
                                if sender not in ['self', wx.nickname]:
                                    should_process = True
                            else:
                                # 按监听列表过滤
                                for chat_name in listen_list:
                                    if chat_name in sender or sender in chat_name:
                                        should_process = True
                                        break
                            
                            if not should_process:
                                continue
                            
                            print(f"\n收到消息:")
                            print(f"  发送者: {sender}")
                            print(f"  内容: {content}")
                            print(f"  类型: {msg_type}")
                            
                            if auto_reply and 'Text' in msg_type:
                                # 生成回复
                                reply = find_answer(content)
                                print(f"  生成回复: {reply[:100]}...")
                                
                                # 延迟发送
                                if reply_delay > 0:
                                    time.sleep(reply_delay)
                                
                                # 发送回复
                                try:
                                    # 尝试切换到发送者聊天窗口
                                    if wx.ChatWith(sender):
                                        wx.SendMsg(reply)
                                        print(f"  ✅ 回复已发送")
                                    else:
                                        print(f"  ❌ 无法切换到聊天窗口: {sender}")
                                except Exception as e:
                                    print(f"  ❌ 发送回复失败: {e}")
                            
                        except Exception as e:
                            print(f"处理消息失败: {e}")
                
                time.sleep(1)
                
        except KeyboardInterrupt:
            print("\n\n机器人已停止")
        
    except Exception as e:
        print(f"❌ 机器人启动失败: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主函数"""
    print("微信客服机器人 - 简化版")
    print("=" * 50)
    
    # 测试模块导入
    if not test_imports():
        print("\n❌ 模块导入测试失败，请检查依赖安装")
        print("尝试运行: pip install colorlog wxauto pandas jieba")
        return
    
    print("\n✅ 所有模块导入成功")
    
    # 启动机器人
    simple_bot()

if __name__ == "__main__":
    main()
