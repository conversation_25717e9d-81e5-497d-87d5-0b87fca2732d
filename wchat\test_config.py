#!/usr/bin/env python3
"""
测试配置加载
"""
import sys
import os

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

try:
    from config import config
    print("✅ 配置加载成功")
    print(f"微信配置: {config.wechat}")
    print(f"监听列表: {config.wechat.listen_list}")
    print(f"监听所有: {getattr(config.wechat, 'listen_all', '字段不存在')}")
    print(f"自动回复: {config.wechat.auto_reply}")
    print(f"回复延迟: {config.wechat.reply_delay}")
except Exception as e:
    print(f"❌ 配置加载失败: {e}")
    import traceback
    traceback.print_exc()
