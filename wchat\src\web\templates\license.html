{% extends "base.html" %}

{% block title %}许可证管理 - 私域自动化{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-key me-2"></i>
        许可证管理
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <button type="button" class="btn btn-outline-primary btn-sm" onclick="refreshLicenseInfo()">
            <i class="fas fa-sync-alt me-1"></i>刷新状态
        </button>
    </div>
</div>

<!-- 硬件信息卡片 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-microchip me-2"></i>
                    硬件信息
                </h5>
            </div>
            <div class="card-body">
                <div id="hardware-info-loading" class="text-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                    <p class="mt-2">正在获取硬件信息...</p>
                </div>
                <div id="hardware-info-content" style="display: none;">
                    <div class="row">
                        <div class="col-md-6">
                            <strong>硬件ID:</strong>
                            <div class="input-group mt-1">
                                <input type="text" class="form-control font-monospace" id="hardware-id" readonly>
                                <button class="btn btn-outline-secondary" type="button" onclick="copyToClipboard('hardware-id')">
                                    <i class="fas fa-copy"></i>
                                </button>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <strong>系统平台:</strong>
                            <p class="mt-1 mb-0" id="platform-info"></p>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-md-6">
                            <strong>机器类型:</strong>
                            <p class="mt-1 mb-0" id="machine-info"></p>
                        </div>
                        <div class="col-md-6">
                            <strong>计算机名:</strong>
                            <p class="mt-1 mb-0" id="node-info"></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 许可证状态卡片 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-certificate me-2"></i>
                    许可证状态
                </h5>
            </div>
            <div class="card-body">
                <div id="license-status-loading" class="text-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                    <p class="mt-2">正在检查许可证状态...</p>
                </div>
                <div id="license-status-content" style="display: none;">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="text-center">
                                <div id="license-status-badge" class="badge fs-6 mb-2"></div>
                                <p class="text-muted mb-0">许可证状态</p>
                            </div>
                        </div>
                        <div class="col-md-4" id="expire-info" style="display: none;">
                            <div class="text-center">
                                <div class="fs-5 fw-bold" id="expire-date"></div>
                                <p class="text-muted mb-0">到期日期</p>
                            </div>
                        </div>
                        <div class="col-md-4" id="days-info" style="display: none;">
                            <div class="text-center">
                                <div class="fs-5 fw-bold" id="days-remaining"></div>
                                <p class="text-muted mb-0">剩余天数</p>
                            </div>
                        </div>
                    </div>
                    <div id="license-error" class="alert alert-danger mt-3" style="display: none;">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <span id="license-error-message"></span>
                    </div>
                    <div id="license-features" class="mt-3" style="display: none;">
                        <h6>授权功能:</h6>
                        <div id="features-list"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 许可证操作卡片 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-cogs me-2"></i>
                    许可证操作
                </h5>
            </div>
            <div class="card-body">
                <!-- 安装许可证 -->
                <div class="mb-4">
                    <h6>安装许可证</h6>
                    <div class="input-group">
                        <input type="text" class="form-control" id="license-key" placeholder="请输入许可证密钥">
                        <button class="btn btn-primary" type="button" onclick="installLicense()">
                            <i class="fas fa-download me-1"></i>安装
                        </button>
                    </div>
                    <small class="text-muted">请联系管理员获取适用于当前硬件的许可证密钥</small>
                </div>
                
                <!-- 移除许可证 -->
                <div>
                    <h6>移除许可证</h6>
                    <button class="btn btn-danger" onclick="removeLicense()">
                        <i class="fas fa-trash me-1"></i>移除许可证
                    </button>
                    <small class="text-muted d-block mt-1">⚠️ 此操作将删除当前许可证文件</small>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// 页面加载时获取信息
document.addEventListener('DOMContentLoaded', function() {
    loadHardwareInfo();
    loadLicenseStatus();
});

// 获取硬件信息
function loadHardwareInfo() {
    fetch('/api/license/hardware-info')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const info = data.data;
                document.getElementById('hardware-id').value = info.hardware_id;
                document.getElementById('platform-info').textContent = info.platform;
                document.getElementById('machine-info').textContent = info.machine;
                document.getElementById('node-info').textContent = info.node;
                
                document.getElementById('hardware-info-loading').style.display = 'none';
                document.getElementById('hardware-info-content').style.display = 'block';
            } else {
                showError('获取硬件信息失败: ' + data.error);
            }
        })
        .catch(error => {
            showError('获取硬件信息失败: ' + error.message);
        });
}

// 获取许可证状态
function loadLicenseStatus() {
    fetch('/api/license/status')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const info = data.data;
                updateLicenseStatus(info);
                
                document.getElementById('license-status-loading').style.display = 'none';
                document.getElementById('license-status-content').style.display = 'block';
            } else {
                showError('获取许可证状态失败: ' + data.error);
            }
        })
        .catch(error => {
            showError('获取许可证状态失败: ' + error.message);
        });
}

// 更新许可证状态显示
function updateLicenseStatus(info) {
    const statusBadge = document.getElementById('license-status-badge');
    const errorDiv = document.getElementById('license-error');
    const errorMessage = document.getElementById('license-error-message');
    const expireInfo = document.getElementById('expire-info');
    const daysInfo = document.getElementById('days-info');
    const featuresDiv = document.getElementById('license-features');
    
    if (info.valid) {
        statusBadge.className = 'badge bg-success fs-6 mb-2';
        statusBadge.textContent = '有效';
        
        document.getElementById('expire-date').textContent = info.expire_date;
        document.getElementById('days-remaining').textContent = info.days_remaining + ' 天';
        
        expireInfo.style.display = 'block';
        daysInfo.style.display = 'block';
        errorDiv.style.display = 'none';
        
        // 显示授权功能
        if (info.features && Object.keys(info.features).length > 0) {
            const featuresList = document.getElementById('features-list');
            featuresList.innerHTML = '';
            
            for (const [feature, enabled] of Object.entries(info.features)) {
                const badge = document.createElement('span');
                badge.className = enabled ? 'badge bg-success me-2' : 'badge bg-secondary me-2';
                badge.innerHTML = (enabled ? '<i class="fas fa-check me-1"></i>' : '<i class="fas fa-times me-1"></i>') + feature;
                featuresList.appendChild(badge);
            }
            
            featuresDiv.style.display = 'block';
        }
    } else {
        statusBadge.className = 'badge bg-danger fs-6 mb-2';
        statusBadge.textContent = '无效';
        
        expireInfo.style.display = 'none';
        daysInfo.style.display = 'none';
        featuresDiv.style.display = 'none';
        
        if (info.error) {
            errorMessage.textContent = info.error;
            errorDiv.style.display = 'block';
        }
    }
}

// 安装许可证
function installLicense() {
    const licenseKey = document.getElementById('license-key').value.trim();
    
    if (!licenseKey) {
        showError('请输入许可证密钥');
        return;
    }
    
    const button = event.target;
    const originalText = button.innerHTML;
    button.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>安装中...';
    button.disabled = true;
    
    fetch('/api/license/install', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            license_key: licenseKey
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showSuccess(data.message);
            document.getElementById('license-key').value = '';
            setTimeout(() => {
                loadLicenseStatus();
            }, 1000);
        } else {
            showError(data.error);
        }
    })
    .catch(error => {
        showError('安装许可证失败: ' + error.message);
    })
    .finally(() => {
        button.innerHTML = originalText;
        button.disabled = false;
    });
}

// 移除许可证
function removeLicense() {
    if (!confirm('确定要移除当前许可证吗？此操作不可撤销。')) {
        return;
    }
    
    const button = event.target;
    const originalText = button.innerHTML;
    button.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>移除中...';
    button.disabled = true;
    
    fetch('/api/license/remove', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showSuccess(data.message);
            setTimeout(() => {
                loadLicenseStatus();
            }, 1000);
        } else {
            showError(data.error);
        }
    })
    .catch(error => {
        showError('移除许可证失败: ' + error.message);
    })
    .finally(() => {
        button.innerHTML = originalText;
        button.disabled = false;
    });
}

// 刷新许可证信息
function refreshLicenseInfo() {
    document.getElementById('hardware-info-loading').style.display = 'block';
    document.getElementById('hardware-info-content').style.display = 'none';
    document.getElementById('license-status-loading').style.display = 'block';
    document.getElementById('license-status-content').style.display = 'none';
    
    loadHardwareInfo();
    loadLicenseStatus();
}

// 复制到剪贴板
function copyToClipboard(elementId) {
    const element = document.getElementById(elementId);
    element.select();
    element.setSelectionRange(0, 99999);
    document.execCommand('copy');
    
    showSuccess('已复制到剪贴板');
}

// 显示成功消息
function showSuccess(message) {
    // 这里可以使用 Bootstrap Toast 或其他通知组件
    alert('✅ ' + message);
}

// 显示错误消息
function showError(message) {
    // 这里可以使用 Bootstrap Toast 或其他通知组件
    alert('❌ ' + message);
}
</script>
{% endblock %}
