#!/usr/bin/env python3
"""
精确产品匹配器
基于产品名称的精确匹配，避免误匹配
"""
import jieba
from typing import List, Dict, Set, Optional

class ExactProductMatcher:
    """精确产品匹配器"""
    
    def __init__(self):
        # 产品类型关键词映射
        self.product_type_keywords = {
            # 电子产品
            "手机": ["手机", "智能手机", "phone"],
            "电脑": ["电脑", "笔记本", "台式机", "计算机", "pc"],
            "平板": ["平板", "tablet", "ipad"],
            "手表": ["手表", "手环", "watch", "智能手表"],
            "耳机": ["耳机", "耳麦", "headphone", "earphone"],
            "音响": ["音响", "音箱", "speaker"],
            "相机": ["相机", "摄像头", "camera"],
            
            # 配件
            "鼠标": ["鼠标", "mouse"],
            "键盘": ["键盘", "keyboard"],
            "充电器": ["充电器", "充电线", "charger"],
            "数据线": ["数据线", "连接线", "cable"],
            
            # 家居
            "沙发": ["沙发", "sofa"],
            "桌子": ["桌子", "桌", "table", "desk"],
            "椅子": ["椅子", "chair"],
            
            # 服装
            "衬衫": ["衬衫", "shirt"],
            "t恤": ["t恤", "tshirt", "t-shirt"],
            "裤子": ["裤子", "pants", "trousers"],
            "鞋子": ["鞋子", "鞋", "shoes"],
            
            # 食品
            "水果": ["水果", "fruit"],
            "饮料": ["饮料", "drink", "beverage"],
            "零食": ["零食", "snack"],
        }
        
        # 反向映射：关键词 -> 产品类型
        self.keyword_to_type = {}
        for product_type, keywords in self.product_type_keywords.items():
            for keyword in keywords:
                self.keyword_to_type[keyword.lower()] = product_type
    
    def calculate_product_score(self, query: str, product_data: Dict) -> float:
        """
        精确匹配计算分数
        
        Args:
            query: 用户查询
            product_data: 产品数据
            
        Returns:
            float: 匹配分数 (0-1)
        """
        query = query.lower().strip()
        product_name = str(product_data.get('产品名称', '')).lower()
        
        if not product_name:
            return 0.0
        
        # 1. 直接包含匹配（最高优先级）
        if query in product_name:
            return self._calculate_direct_match_score(query, product_name)
        
        # 2. 分词精确匹配
        query_words = set(jieba.lcut(query))
        name_words = set(jieba.lcut(product_name))
        
        # 查找共同词汇
        common_words = query_words & name_words
        if common_words:
            return self._calculate_word_match_score(query_words, name_words, common_words)
        
        # 3. 产品类型匹配
        query_type = self._identify_product_type(query)
        product_type = self._identify_product_type(product_name)
        
        if query_type and product_type and query_type == product_type:
            return 0.8  # 类型匹配，但不是直接匹配
        
        # 4. 无匹配
        return 0.0
    
    def _calculate_direct_match_score(self, query: str, product_name: str) -> float:
        """计算直接匹配分数"""
        if query == product_name:
            return 1.0  # 完全匹配
        elif product_name.startswith(query):
            return 0.95  # 前缀匹配
        elif product_name.endswith(query):
            return 0.9   # 后缀匹配
        else:
            return 0.85  # 包含匹配
    
    def _calculate_word_match_score(self, query_words: Set[str], name_words: Set[str], common_words: Set[str]) -> float:
        """计算分词匹配分数"""
        # 计算匹配度
        query_match_ratio = len(common_words) / len(query_words)
        name_match_ratio = len(common_words) / len(name_words)
        
        # 综合匹配度
        overall_match = (query_match_ratio + name_match_ratio) / 2
        
        # 如果查询词完全匹配，给高分
        if query_match_ratio == 1.0:
            return min(0.9, overall_match + 0.1)
        
        return overall_match * 0.8  # 部分匹配降权
    
    def _identify_product_type(self, text: str) -> Optional[str]:
        """识别产品类型"""
        text = text.lower()
        
        # 直接关键词匹配
        for keyword, product_type in self.keyword_to_type.items():
            if keyword in text:
                return product_type
        
        return None
    
    def is_exact_match(self, query: str, product_name: str) -> bool:
        """判断是否为精确匹配"""
        query = query.lower().strip()
        product_name = product_name.lower().strip()
        
        # 直接包含
        if query in product_name:
            return True
        
        # 分词匹配
        query_words = set(jieba.lcut(query))
        name_words = set(jieba.lcut(product_name))
        
        # 查询词在产品名称中完全匹配
        return query_words.issubset(name_words)
    
    def filter_exact_matches(self, query: str, products: List[Dict]) -> List[Dict]:
        """过滤出精确匹配的产品"""
        exact_matches = []
        
        for product in products:
            product_name = str(product.get('产品名称', ''))
            if self.is_exact_match(query, product_name):
                score = self.calculate_product_score(query, product)
                product_copy = product.copy()
                product_copy['score'] = score
                exact_matches.append(product_copy)
        
        # 按分数排序
        exact_matches.sort(key=lambda x: x['score'], reverse=True)
        return exact_matches
    
    def get_product_type_summary(self) -> Dict[str, List[str]]:
        """获取支持的产品类型总结"""
        return self.product_type_keywords.copy()
    
    def add_product_type(self, product_type: str, keywords: List[str]):
        """添加新的产品类型"""
        self.product_type_keywords[product_type] = keywords
        
        # 更新反向映射
        for keyword in keywords:
            self.keyword_to_type[keyword.lower()] = product_type
