#!/usr/bin/env python3
"""
完整的产品图片功能测试
包括产品查询、图片发送等
"""
import os
import sys
from pathlib import Path

# 添加项目路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def test_product_image_handler():
    """测试产品图片处理器"""
    print("=" * 60)
    print("          测试产品图片处理器")
    print("=" * 60)
    
    try:
        from src.bot.product_image_handler import ProductImageHandler
        
        # 创建图片处理器
        image_handler = ProductImageHandler()
        
        # 1. 验证图片文件
        print("1. 验证图片文件:")
        validation_result = image_handler.validate_image_files()
        for product_name, exists in validation_result.items():
            status = "✅" if exists else "❌"
            print(f"   {status} {product_name}")
        
        # 2. 检查缺失的图片
        missing_images = image_handler.get_missing_images()
        if missing_images:
            print(f"\n❌ 缺失的图片文件: {missing_images}")
        else:
            print(f"\n✅ 所有图片文件都存在")
        
        # 3. 获取图片详细信息
        print(f"\n2. 图片详细信息:")
        image_info = image_handler.get_image_info()
        for product_name, info in image_info.items():
            print(f"   产品: {product_name}")
            print(f"   文件: {info['filename']}")
            print(f"   大小: {info['size']} bytes")
            print(f"   状态: {'存在' if info['exists'] else '缺失'}")
            print()
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_enhanced_product_reply():
    """测试增强的产品回复功能"""
    print("=" * 60)
    print("          测试增强产品回复")
    print("=" * 60)
    
    try:
        from src.database.enhanced_reader import EnhancedProductReader
        from src.bot.product_image_handler import ProductImageHandler
        from config import config
        
        # 创建产品读取器和图片处理器
        product_reader = EnhancedProductReader(config.database.products_file)
        image_handler = ProductImageHandler()
        
        print(f"产品数据加载: {len(product_reader.data)} 条记录")
        
        # 测试查询
        test_queries = [
            "推荐一款手机",
            "有什么耳机",
            "笔记本电脑",
            "充电器多少钱"
        ]
        
        for query in test_queries:
            print(f"\n" + "-" * 50)
            print(f"用户查询: '{query}'")
            
            # 搜索产品
            products = product_reader.search_products(query)
            if products:
                # 限制显示数量
                display_products = products[:2]
                
                # 格式化回复（包含图片）
                reply, image_paths = image_handler.format_product_reply_with_images(display_products)
                
                print(f"\n机器人回复:")
                print(reply)
                
                if image_paths:
                    print(f"\n📷 需要发送的图片 ({len(image_paths)} 张):")
                    for i, img_path in enumerate(image_paths, 1):
                        print(f"   {i}. {os.path.basename(img_path)}")
                        # 检查文件
                        if os.path.exists(img_path):
                            file_size = os.path.getsize(img_path)
                            print(f"      ✅ 文件存在 ({file_size} bytes)")
                        else:
                            print(f"      ❌ 文件不存在")
                else:
                    print(f"\n📷 无图片需要发送")
            else:
                print(f"   ❌ 未找到相关产品")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_mock_wechat_interaction():
    """模拟微信交互测试"""
    print("=" * 60)
    print("          模拟微信交互测试")
    print("=" * 60)
    
    try:
        from src.database.enhanced_reader import EnhancedProductReader
        from src.bot.product_image_handler import ProductImageHandler
        from config import config
        
        # 创建组件
        product_reader = EnhancedProductReader(config.database.products_file)
        image_handler = ProductImageHandler()
        
        # 模拟用户消息
        user_messages = [
            "我想买个手机",
            "有什么好的耳机推荐",
            "笔记本电脑价格"
        ]
        
        for msg in user_messages:
            print(f"\n" + "=" * 40)
            print(f"👤 用户: {msg}")
            
            # 检查是否是产品查询
            if any(keyword in msg for keyword in ['手机', '耳机', '笔记本', '电脑', '充电器', '鼠标']):
                # 搜索产品
                products = product_reader.search_products(msg)
                
                if products:
                    # 格式化回复
                    reply, image_paths = image_handler.format_product_reply_with_images(products[:1])  # 只显示1个
                    
                    print(f"🤖 机器人: {reply}")
                    
                    # 模拟发送图片
                    if image_paths:
                        print(f"\n📤 发送图片:")
                        for img_path in image_paths:
                            print(f"   正在发送: {os.path.basename(img_path)}")
                            if os.path.exists(img_path):
                                print(f"   ✅ 图片发送成功")
                            else:
                                print(f"   ❌ 图片发送失败 - 文件不存在")
                else:
                    print(f"🤖 机器人: 抱歉，没有找到相关产品。")
            else:
                print(f"🤖 机器人: 您好！有什么可以帮助您的吗？")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 开始产品图片功能完整测试")
    print()
    
    # 运行所有测试
    tests = [
        ("产品图片处理器", test_product_image_handler),
        ("增强产品回复", test_enhanced_product_reply),
        ("模拟微信交互", test_mock_wechat_interaction)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n🔍 运行测试: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
            if result:
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 总结
    print(f"\n" + "=" * 60)
    print(f"          测试总结")
    print(f"=" * 60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{status} {test_name}")
    
    print(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试都通过了！产品图片功能工作正常。")
    else:
        print("⚠️ 部分测试失败，请检查相关功能。")

if __name__ == "__main__":
    main()
