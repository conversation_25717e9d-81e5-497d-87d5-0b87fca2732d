#!/usr/bin/env python3
"""
私域自动化系统 - 网络配置工具
用于配置远程访问、安全设置等
"""

import json
import os
import sys
import socket
import requests
from pathlib import Path

def get_local_ip():
    """获取本机IP地址"""
    try:
        # 连接到外部地址获取本机IP
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        local_ip = s.getsockname()[0]
        s.close()
        return local_ip
    except:
        return "127.0.0.1"

def get_public_ip():
    """获取公网IP地址"""
    try:
        response = requests.get("https://api.ipify.org", timeout=5)
        return response.text.strip()
    except:
        try:
            response = requests.get("https://ifconfig.me", timeout=5)
            return response.text.strip()
        except:
            return None

def load_config():
    """加载配置文件"""
    config_file = Path("config/config.json")
    if config_file.exists():
        with open(config_file, 'r', encoding='utf-8') as f:
            return json.load(f)
    return None

def save_config(config_data):
    """保存配置文件"""
    config_file = Path("config/config.json")
    with open(config_file, 'w', encoding='utf-8') as f:
        json.dump(config_data, f, indent=2, ensure_ascii=False)

def show_current_config():
    """显示当前配置"""
    config_data = load_config()
    if not config_data:
        print("❌ 配置文件不存在")
        return
    
    web_config = config_data.get('web', {})
    
    print("\n" + "=" * 60)
    print("           当前网络配置")
    print("=" * 60)
    print(f"监听地址: {web_config.get('host', '127.0.0.1')}")
    print(f"监听端口: {web_config.get('port', 5000)}")
    print(f"外部URL: {web_config.get('external_url', '未设置')}")
    print(f"HTTPS: {'启用' if web_config.get('enable_https', False) else '禁用'}")
    
    allowed_ips = web_config.get('allowed_ips', [])
    if allowed_ips:
        print(f"IP白名单: {', '.join(allowed_ips)}")
    else:
        print("IP白名单: 未设置（允许所有IP）")
    
    print("\n" + "=" * 60)
    print("           网络信息")
    print("=" * 60)
    print(f"本机IP: {get_local_ip()}")
    
    public_ip = get_public_ip()
    if public_ip:
        print(f"公网IP: {public_ip}")
    else:
        print("公网IP: 获取失败")

def configure_remote_access():
    """配置远程访问"""
    config_data = load_config()
    if not config_data:
        print("❌ 配置文件不存在")
        return
    
    print("\n" + "=" * 60)
    print("           配置远程访问")
    print("=" * 60)
    
    # 配置监听地址
    print("\n1. 监听地址配置")
    print("   0.0.0.0 - 允许所有网络接口访问（推荐）")
    print("   127.0.0.1 - 仅本机访问")
    
    current_host = config_data['web'].get('host', '127.0.0.1')
    new_host = input(f"请输入监听地址 (当前: {current_host}): ").strip()
    if new_host:
        config_data['web']['host'] = new_host
    
    # 配置端口
    print("\n2. 端口配置")
    current_port = config_data['web'].get('port', 5000)
    new_port = input(f"请输入端口号 (当前: {current_port}): ").strip()
    if new_port and new_port.isdigit():
        config_data['web']['port'] = int(new_port)
    
    # 配置外部URL
    print("\n3. 外部访问URL")
    print("   如果使用域名或公网IP，请输入完整URL")
    print("   例如: https://yourdomain.com:5000")
    
    current_url = config_data['web'].get('external_url', '')
    new_url = input(f"请输入外部URL (当前: {current_url or '未设置'}): ").strip()
    if new_url:
        config_data['web']['external_url'] = new_url
    
    save_config(config_data)
    print("\n✅ 远程访问配置已保存")

def configure_security():
    """配置安全设置"""
    config_data = load_config()
    if not config_data:
        print("❌ 配置文件不存在")
        return
    
    print("\n" + "=" * 60)
    print("           配置安全设置")
    print("=" * 60)
    
    # IP白名单配置
    print("\n1. IP白名单配置")
    print("   可以输入单个IP或CIDR格式的网段")
    print("   例如: ************* 或 ***********/24")
    print("   留空表示允许所有IP访问")
    
    current_ips = config_data['web'].get('allowed_ips', [])
    print(f"   当前白名单: {', '.join(current_ips) if current_ips else '未设置'}")
    
    choice = input("\n是否要修改IP白名单? (y/n): ").strip().lower()
    if choice == 'y':
        new_ips = []
        print("请输入允许访问的IP地址（每行一个，输入空行结束）:")
        while True:
            ip = input("IP地址: ").strip()
            if not ip:
                break
            new_ips.append(ip)
        
        config_data['web']['allowed_ips'] = new_ips
        print(f"✅ IP白名单已更新: {', '.join(new_ips) if new_ips else '允许所有IP'}")
    
    # 密码配置
    print("\n2. 密码配置")
    choice = input("是否要修改登录密码? (y/n): ").strip().lower()
    if choice == 'y':
        new_password = input("请输入新的普通用户密码: ").strip()
        if new_password:
            config_data['web']['password'] = new_password
            print("✅ 普通用户密码已更新")
        
        new_admin_password = input("请输入新的管理员密码: ").strip()
        if new_admin_password:
            config_data['web']['admin_password'] = new_admin_password
            print("✅ 管理员密码已更新")
    
    save_config(config_data)
    print("\n✅ 安全设置已保存")

def test_network():
    """测试网络连接"""
    config_data = load_config()
    if not config_data:
        print("❌ 配置文件不存在")
        return
    
    web_config = config_data.get('web', {})
    host = web_config.get('host', '127.0.0.1')
    port = web_config.get('port', 5000)
    
    print("\n" + "=" * 60)
    print("           网络连接测试")
    print("=" * 60)
    
    # 测试端口是否可用
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(1)
        result = sock.connect_ex((host if host != '0.0.0.0' else '127.0.0.1', port))
        sock.close()
        
        if result == 0:
            print(f"✅ 端口 {port} 已被占用（可能是服务正在运行）")
        else:
            print(f"✅ 端口 {port} 可用")
    except Exception as e:
        print(f"❌ 端口测试失败: {e}")
    
    # 显示访问地址
    print(f"\n本地访问地址:")
    print(f"  http://127.0.0.1:{port}")
    print(f"  http://localhost:{port}")
    
    local_ip = get_local_ip()
    if local_ip != "127.0.0.1":
        print(f"\n局域网访问地址:")
        print(f"  http://{local_ip}:{port}")
    
    external_url = web_config.get('external_url')
    if external_url:
        print(f"\n外部访问地址:")
        print(f"  {external_url}")
    
    public_ip = get_public_ip()
    if public_ip and host == '0.0.0.0':
        print(f"\n公网访问地址（需要路由器端口映射）:")
        print(f"  http://{public_ip}:{port}")

def show_firewall_help():
    """显示防火墙配置帮助"""
    print("\n" + "=" * 60)
    print("           防火墙配置指南")
    print("=" * 60)
    
    config_data = load_config()
    port = config_data.get('web', {}).get('port', 5000) if config_data else 5000
    
    print(f"\nWindows防火墙配置:")
    print(f"1. 打开Windows防火墙设置")
    print(f"2. 点击'高级设置'")
    print(f"3. 选择'入站规则' -> '新建规则'")
    print(f"4. 选择'端口' -> '下一步'")
    print(f"5. 选择'TCP'，输入端口号: {port}")
    print(f"6. 选择'允许连接' -> '下一步'")
    print(f"7. 应用到所有配置文件 -> '下一步'")
    print(f"8. 输入规则名称: '私域自动化系统' -> '完成'")
    
    print(f"\n路由器端口映射配置:")
    print(f"1. 登录路由器管理界面")
    print(f"2. 找到'端口映射'或'虚拟服务器'设置")
    print(f"3. 添加新规则:")
    print(f"   - 服务名称: 私域自动化系统")
    print(f"   - 外部端口: {port}")
    print(f"   - 内部端口: {port}")
    print(f"   - 内部IP: {get_local_ip()}")
    print(f"   - 协议: TCP")
    print(f"4. 保存并重启路由器")

def main():
    """主菜单"""
    while True:
        print("\n" + "=" * 60)
        print("           私域自动化系统 - 网络配置工具")
        print("=" * 60)
        print("1. 查看当前配置")
        print("2. 配置远程访问")
        print("3. 配置安全设置")
        print("4. 测试网络连接")
        print("5. 防火墙配置指南")
        print("0. 退出")
        print("=" * 60)
        
        choice = input("请选择操作 (0-5): ").strip()
        
        if choice == '1':
            show_current_config()
        elif choice == '2':
            configure_remote_access()
        elif choice == '3':
            configure_security()
        elif choice == '4':
            test_network()
        elif choice == '5':
            show_firewall_help()
        elif choice == '0':
            print("\n👋 感谢使用网络配置工具！")
            break
        else:
            print("❌ 无效选择，请重新输入")

if __name__ == "__main__":
    main()
