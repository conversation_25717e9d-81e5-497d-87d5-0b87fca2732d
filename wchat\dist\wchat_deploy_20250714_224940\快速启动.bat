@echo off
chcp 65001 >nul

REM 获取脚本所在目录
set SCRIPT_DIR=%~dp0
cd /d "%SCRIPT_DIR%"

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    微信客服机器人快速启动                      ║
echo ║                                                              ║
echo ║  当前目录: %SCRIPT_DIR%                                      ║
echo ║                                                              ║
echo ║  1. 安装依赖包                                               ║
echo ║  2. 启动Web配置界面                                          ║
echo ║  3. 启动机器人                                               ║
echo ║  4. 测试基础功能                                             ║
echo ║  5. 打开演示页面                                             ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

:menu
echo 请选择操作：
echo [1] 安装依赖包
echo [2] 启动Web配置界面
echo [3] 启动机器人
echo [4] 测试基础功能
echo [5] 打开演示页面
echo [6] 退出
echo.
set /p choice=请输入选择 (1-6):

if "%choice%"=="1" goto install
if "%choice%"=="2" goto webconfig
if "%choice%"=="3" goto runbot
if "%choice%"=="4" goto test
if "%choice%"=="5" goto demo
if "%choice%"=="6" goto exit
echo 无效选择，请重新输入
goto menu

:install
echo.
echo ========================================
echo 正在安装依赖包...
echo ========================================
echo 当前目录: %CD%
echo.
python install_deps.py
if %errorlevel% neq 0 (
    echo.
    echo ❌ 依赖包安装失败！
    echo 请检查Python环境和网络连接
) else (
    echo.
    echo ✅ 依赖包安装完成！
)
echo.
pause
goto menu

:webconfig
echo.
echo ========================================
echo 正在启动Web配置界面...
echo ========================================
echo 访问地址: http://127.0.0.1:5000
echo 默认密码: admin123
echo 当前目录: %CD%
echo.
echo 提示: 启动后会自动打开浏览器
echo 按 Ctrl+C 可以停止服务器
echo.
python web_config.py
if %errorlevel% neq 0 (
    echo.
    echo ❌ Web配置界面启动失败！
    echo 请检查依赖包是否正确安装
)
echo.
pause
goto menu

:runbot
echo.
echo ========================================
echo 正在启动微信客服机器人...
echo ========================================
echo 重要提示:
echo 1. 请确保微信PC版已登录
echo 2. 请先在Web配置界面中设置监听列表
echo 3. 按 Ctrl+C 可以停止机器人
echo 当前目录: %CD%
echo.
python run.py
if %errorlevel% neq 0 (
    echo.
    echo ❌ 机器人启动失败！
    echo 请检查微信是否已登录和配置是否正确
)
echo.
pause
goto menu

:test
echo.
echo ========================================
echo 正在运行基础功能测试...
echo ========================================
echo 当前目录: %CD%
echo.
python test_basic.py
if %errorlevel% neq 0 (
    echo.
    echo ❌ 测试运行失败！
) else (
    echo.
    echo ✅ 测试完成！
)
echo.
pause
goto menu

:demo
echo.
echo ========================================
echo 正在打开演示页面...
echo ========================================
echo 演示页面路径: %CD%\demo.html
echo.
start "" "%CD%\demo.html"
if %errorlevel% neq 0 (
    echo ❌ 无法打开演示页面
    echo 请手动打开: %CD%\demo.html
) else (
    echo ✅ 演示页面已在浏览器中打开
)
echo.
pause
goto menu

:exit
echo.
echo ========================================
echo 感谢使用微信客服机器人！
echo ========================================
echo 项目地址: %CD%
echo 如有问题，请查看项目文档
echo.
pause
exit
