#!/usr/bin/env python3
"""
调试回复逻辑
"""
import sys
from pathlib import Path

# 添加项目路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def test_faq_matching():
    """测试FAQ匹配"""
    print("=" * 60)
    print("          测试FAQ匹配")
    print("=" * 60)
    
    try:
        from src.database.enhanced_reader import EnhancedFAQReader
        from src.database.matcher import ContentMatcher
        from config import config
        
        # 创建FAQ读取器
        faq_reader = EnhancedFAQReader(config.database.faq_file)
        print(f"✅ FAQ数据加载: {len(faq_reader.data)} 条记录")
        
        # 显示FAQ数据
        print("\nFAQ数据预览:")
        for i, row in faq_reader.data.head(3).iterrows():
            print(f"  {i+1}. 问题: {row.get('标准问题', 'N/A')}")
            print(f"     回复: {row.get('回复内容', 'N/A')[:50]}...")
        
        # 创建匹配器
        matcher = ContentMatcher(config.database.similarity_threshold)
        
        # 测试消息
        test_messages = [
            "手机",
            "耳机", 
            "如何退货",
            "退款",
            "发货",
            "价格"
        ]
        
        print(f"\n测试FAQ匹配 (相似度阈值: {config.database.similarity_threshold}):")
        for msg in test_messages:
            print(f"\n测试消息: '{msg}'")
            
            # 搜索FAQ
            faq_list = faq_reader.search_faq(msg)
            print(f"  搜索结果: {len(faq_list)} 条")
            
            if faq_list:
                # 计算相似度
                matches = matcher.match_faq(msg, faq_list)
                if matches:
                    best_match, score = matches[0]
                    print(f"  ✅ 最佳匹配: {best_match.get('标准问题', 'N/A')} (相似度: {score:.3f})")
                    print(f"     回复: {best_match.get('回复内容', 'N/A')[:50]}...")
                else:
                    print(f"  ❌ 无匹配 (相似度低于阈值)")
            else:
                print(f"  ❌ 搜索无结果")
        
        return True
        
    except Exception as e:
        print(f"❌ FAQ匹配测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_product_matching():
    """测试产品匹配"""
    print("\n" + "=" * 60)
    print("          测试产品匹配")
    print("=" * 60)
    
    try:
        from src.database.enhanced_reader import EnhancedProductReader
        from src.database.matcher import ContentMatcher
        from config import config
        
        # 创建产品读取器
        product_reader = EnhancedProductReader(config.database.products_file)
        print(f"✅ 产品数据加载: {len(product_reader.data)} 条记录")
        
        # 显示产品数据
        print("\n产品数据预览:")
        for i, row in product_reader.data.head(3).iterrows():
            print(f"  {i+1}. 产品: {row.get('产品名称', 'N/A')}")
            print(f"     描述: {row.get('产品描述', 'N/A')[:50]}...")
        
        # 创建匹配器
        matcher = ContentMatcher(config.database.similarity_threshold)
        
        # 测试消息
        test_messages = [
            "手机",
            "耳机",
            "智能手表",
            "蓝牙",
            "价格",
            "推荐"
        ]
        
        print(f"\n测试产品匹配 (相似度阈值: {config.database.similarity_threshold}):")
        for msg in test_messages:
            print(f"\n测试消息: '{msg}'")
            
            # 搜索产品
            product_list = product_reader.search_products(msg)
            print(f"  搜索结果: {len(product_list)} 条")
            
            if product_list:
                # 计算相似度
                matches = matcher.match_products(msg, product_list)
                if matches:
                    best_match, score = matches[0]
                    print(f"  ✅ 最佳匹配: {best_match.get('产品名称', 'N/A')} (相似度: {score:.3f})")
                    print(f"     描述: {best_match.get('产品描述', 'N/A')[:50]}...")
                else:
                    print(f"  ❌ 无匹配 (相似度低于阈值)")
            else:
                print(f"  ❌ 搜索无结果")
        
        return True
        
    except Exception as e:
        print(f"❌ 产品匹配测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_reply_engine():
    """测试回复引擎"""
    print("\n" + "=" * 60)
    print("          测试回复引擎")
    print("=" * 60)
    
    try:
        from src.bot.reply_engine import ReplyEngine
        
        # 创建回复引擎
        reply_engine = ReplyEngine()
        print("✅ 回复引擎创建成功")
        
        # 测试消息
        test_messages = [
            "手机",
            "如何退货",
            "有什么耳机推荐",
            "价格多少",
            "你好"
        ]
        
        print("\n测试回复生成:")
        for msg in test_messages:
            print(f"\n测试消息: '{msg}'")
            print("-" * 40)
            
            try:
                reply = reply_engine.generate_reply(msg, "测试用户")
                print(f"回复: {reply}")
            except Exception as e:
                print(f"❌ 回复生成失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 回复引擎测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("开始调试回复逻辑...")
    
    # 测试FAQ匹配
    if test_faq_matching():
        print("\n✅ FAQ匹配测试完成")
    else:
        print("\n❌ FAQ匹配测试失败")
        return
    
    # 测试产品匹配
    if test_product_matching():
        print("\n✅ 产品匹配测试完成")
    else:
        print("\n❌ 产品匹配测试失败")
        return
    
    # 测试回复引擎
    if test_reply_engine():
        print("\n✅ 回复引擎测试完成")
    else:
        print("\n❌ 回复引擎测试失败")
    
    print("\n" + "=" * 60)
    print("调试完成！")
    print("=" * 60)
    
    print("\n分析说明:")
    print("1. 检查FAQ和产品数据是否正确加载")
    print("2. 检查搜索功能是否正常工作")
    print("3. 检查相似度计算是否合理")
    print("4. 检查回复优先级逻辑是否正确")

if __name__ == "__main__":
    main()
