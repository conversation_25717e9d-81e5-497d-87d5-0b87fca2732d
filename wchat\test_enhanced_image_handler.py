#!/usr/bin/env python3
"""
测试增强版产品图片处理器
"""
import os
import sys
import pandas as pd
from pathlib import Path

# 添加项目路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def test_enhanced_image_handler():
    """测试增强版图片处理器"""
    print("🔧 测试增强版产品图片处理器")
    print("=" * 60)
    
    try:
        from src.bot.enhanced_product_image_handler import EnhancedProductImageHandler
        
        # 创建增强版处理器
        handler = EnhancedProductImageHandler()
        
        # 1. 测试映射状态
        print("1. 图片映射状态:")
        status = handler.get_mapping_status()
        print(f"   图片目录: {status['images_dir']} ({'存在' if status['images_dir_exists'] else '不存在'})")
        print(f"   配置文件: {status['config_file']} ({'存在' if status['config_file_exists'] else '不存在'})")
        print(f"   默认映射: {status['default_mappings']} 个")
        print(f"   自定义映射: {status['custom_mappings']} 个")
        print(f"   可用图片: {len(status['available_images'])} 张")
        
        if status['available_images']:
            print("   图片文件:")
            for img in status['available_images']:
                print(f"     - {img}")
        
        # 2. 测试产品代码提取
        print(f"\n2. 产品代码提取测试:")
        test_names = [
            "智能手机A1",
            "智能手机A1 Pro",
            "蓝牙耳机B-2",
            "笔记本电脑D_4",
            "充电器E 5",
            "新产品X99"
        ]
        
        for name in test_names:
            code = handler.extract_product_code(name)
            print(f"   '{name}' → 代码: {code}")
        
        # 3. 测试关键词提取
        print(f"\n3. 关键词提取测试:")
        for name in test_names:
            keywords = handler.extract_keywords(name)
            print(f"   '{name}' → 关键词: {keywords}")
        
        # 4. 测试图片匹配
        print(f"\n4. 图片匹配测试:")
        test_products = [
            {"产品名称": "智能手机A1", "分类": "数码电子"},
            {"产品名称": "智能手机A1 Pro", "分类": "数码电子"},
            {"产品名称": "蓝牙耳机B2", "分类": "数码配件"},
            {"产品名称": "新产品X1", "分类": "数码电子"},
            {"产品名称": "智能音箱", "分类": "智能家居"}
        ]
        
        for product in test_products:
            name = product["产品名称"]
            category = product["分类"]
            image_path = handler.get_product_image_path(name, category)
            
            if image_path:
                filename = os.path.basename(image_path)
                exists = "✅" if os.path.exists(image_path) else "❌"
                print(f"   '{name}' → {filename} {exists}")
            else:
                print(f"   '{name}' → 无匹配图片")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_auto_mapping():
    """测试自动映射功能"""
    print("\n🤖 测试自动映射功能")
    print("=" * 60)
    
    try:
        from src.bot.enhanced_product_image_handler import EnhancedProductImageHandler
        
        handler = EnhancedProductImageHandler()
        
        # 创建测试产品数据
        test_products = [
            {"产品名称": "智能手机A1", "分类": "数码电子", "价格": "2999"},
            {"产品名称": "智能手机A1 Pro", "分类": "数码电子", "价格": "3299"},
            {"产品名称": "蓝牙耳机B2", "分类": "数码配件", "价格": "399"},
            {"产品名称": "新款耳机X1", "分类": "数码配件", "价格": "599"},
            {"产品名称": "智能音箱Y1", "分类": "智能家居", "价格": "299"}
        ]
        
        print("测试产品列表:")
        for product in test_products:
            print(f"   - {product['产品名称']} ({product['分类']})")
        
        # 生成自动映射
        auto_mapping = handler.auto_map_products(test_products)
        
        print(f"\n自动映射结果:")
        if auto_mapping:
            for product_name, image_file in auto_mapping.items():
                print(f"   '{product_name}' → {image_file}")
        else:
            print("   无自动映射结果")
        
        # 测试更新映射配置
        print(f"\n更新映射配置:")
        success = handler.update_mapping_for_new_products(test_products)
        if success:
            print("   ✅ 映射配置更新成功")
        else:
            print("   ❌ 映射配置更新失败")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_product_replacement_compatibility():
    """测试产品替换兼容性"""
    print("\n🔄 测试产品替换兼容性")
    print("=" * 60)
    
    try:
        from src.bot.enhanced_product_image_handler import EnhancedProductImageHandler
        
        handler = EnhancedProductImageHandler()
        
        # 模拟不同的产品替换场景
        scenarios = [
            {
                "name": "完全新产品",
                "products": [
                    {"产品名称": "智能音箱X1", "分类": "智能家居"},
                    {"产品名称": "无线键盘Y2", "分类": "电脑配件"}
                ]
            },
            {
                "name": "产品名称变化",
                "products": [
                    {"产品名称": "智能手机A1 Pro", "分类": "数码电子"},
                    {"产品名称": "蓝牙耳机B2", "分类": "数码配件"}
                ]
            },
            {
                "name": "混合场景",
                "products": [
                    {"产品名称": "智能手机A1", "分类": "数码电子"},  # 原有产品
                    {"产品名称": "新款手机Z1", "分类": "数码电子"},   # 新产品
                    {"产品名称": "游戏鼠标F6", "分类": "电脑配件"}   # 原有产品
                ]
            }
        ]
        
        for scenario in scenarios:
            print(f"\n场景: {scenario['name']}")
            products = scenario['products']
            
            # 测试图片匹配
            matched_count = 0
            for product in products:
                name = product['产品名称']
                category = product['分类']
                image_path = handler.get_product_image_path(name, category)
                
                if image_path and os.path.exists(image_path):
                    filename = os.path.basename(image_path)
                    print(f"   ✅ {name} → {filename}")
                    matched_count += 1
                else:
                    print(f"   ❌ {name} → 无匹配图片")
            
            match_rate = (matched_count / len(products)) * 100
            print(f"   匹配率: {matched_count}/{len(products)} ({match_rate:.1f}%)")
            
            # 测试回复生成
            reply, image_paths = handler.format_product_reply_with_images(products)
            print(f"   回复长度: {len(reply)} 字符")
            print(f"   图片数量: {len(image_paths)} 张")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_configuration_management():
    """测试配置管理功能"""
    print("\n⚙️ 测试配置管理功能")
    print("=" * 60)
    
    try:
        from src.bot.enhanced_product_image_handler import EnhancedProductImageHandler
        
        handler = EnhancedProductImageHandler()
        
        # 测试保存自定义映射
        test_mapping = {
            "测试产品A": "test_image_a.jpg",
            "测试产品B": "test_image_b.jpg"
        }
        
        print("保存测试映射配置:")
        success = handler.save_mapping_config(test_mapping)
        if success:
            print("   ✅ 配置保存成功")
        else:
            print("   ❌ 配置保存失败")
        
        # 测试加载配置
        print("\n加载映射配置:")
        loaded_mapping = handler.load_mapping_config()
        print(f"   加载的映射数量: {len(loaded_mapping)}")
        
        for product_name, image_file in loaded_mapping.items():
            print(f"   '{product_name}' → {image_file}")
        
        # 清理测试配置文件
        if os.path.exists(handler.config_file):
            os.remove(handler.config_file)
            print(f"\n✅ 已清理测试配置文件")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 增强版产品图片处理器测试")
    print()
    
    # 运行所有测试
    tests = [
        ("基础功能", test_enhanced_image_handler),
        ("自动映射", test_auto_mapping),
        ("替换兼容性", test_product_replacement_compatibility),
        ("配置管理", test_configuration_management)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n🔍 运行测试: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
            if result:
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 总结
    print(f"\n" + "=" * 60)
    print(f"          测试总结")
    print(f"=" * 60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{status} {test_name}")
    
    print(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 增强版图片处理器功能完全正常！")
        print("\n💡 主要改进:")
        print("   1. 支持多种产品代码格式匹配")
        print("   2. 基于关键词的智能图片匹配")
        print("   3. 产品分类默认图片支持")
        print("   4. 自定义映射配置管理")
        print("   5. 自动映射生成功能")
    else:
        print("⚠️ 部分功能存在问题，请检查后再使用。")

if __name__ == "__main__":
    main()
