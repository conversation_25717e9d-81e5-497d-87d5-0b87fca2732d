#!/usr/bin/env python3
"""
测试修改后的回复格式
"""
import os
import sys
from pathlib import Path

# 添加项目路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def test_single_product_reply():
    """测试单个产品回复格式"""
    print("📱 测试单个产品回复格式")
    print("=" * 60)
    
    try:
        from src.bot.enhanced_reply_engine_with_images import EnhancedReplyEngineWithImages
        
        reply_engine = EnhancedReplyEngineWithImages()
        
        # 测试单个产品查询
        single_product_queries = [
            "手表",
            "手机", 
            "电脑",
            "鼠标",
            "耳机"
        ]
        
        for query in single_product_queries:
            print(f"\n查询: '{query}'")
            reply, image_paths = reply_engine.generate_reply_with_images(query)
            
            if reply:
                print(f"回复:")
                print(reply)
                print(f"图片数量: {len(image_paths)}")
                
                # 检查格式
                if reply.startswith("推荐你这款产品："):
                    print("✅ 开头格式正确")
                else:
                    print("❌ 开头格式错误")
                
                if "这款怎么样？" not in reply:
                    print("✅ 结尾格式正确（无多余询问）")
                else:
                    print("❌ 结尾格式错误（仍有询问）")
            else:
                print("❌ 无回复")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_multiple_products_reply():
    """测试多个产品回复格式"""
    print("\n📦 测试多个产品回复格式")
    print("=" * 60)
    
    try:
        from src.bot.enhanced_product_image_handler import EnhancedProductImageHandler
        
        image_handler = EnhancedProductImageHandler()
        
        # 模拟多个产品的情况
        multiple_products = [
            {
                "产品名称": "智能手机A1",
                "价格": "2999",
                "产品描述": "6.1寸全面屏，128GB存储",
                "分类": "数码电子",
                "详细信息": "处理器：骁龙888\n内存：8GB"
            },
            {
                "产品名称": "智能手表C3", 
                "价格": "1299",
                "产品描述": "健康监测，运动追踪",
                "分类": "智能穿戴",
                "详细信息": "屏幕：1.4寸彩屏\n续航：7天"
            }
        ]
        
        print("模拟多产品回复:")
        reply, image_paths = image_handler.format_product_reply_with_images(multiple_products)
        
        print(reply)
        print(f"图片数量: {len(image_paths)}")
        
        # 检查格式
        if "为您推荐 2 款相关产品：" in reply:
            print("✅ 多产品开头格式正确")
        else:
            print("❌ 多产品开头格式错误")
        
        if "看看你喜欢哪款～" in reply:
            print("✅ 多产品结尾格式正确")
        else:
            print("❌ 多产品结尾格式错误")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_reply_format_comparison():
    """对比修改前后的回复格式"""
    print("\n⚖️ 对比修改前后的回复格式")
    print("=" * 60)
    
    try:
        from src.bot.enhanced_product_image_handler import EnhancedProductImageHandler
        
        image_handler = EnhancedProductImageHandler()
        
        # 单个产品测试
        single_product = [{
            "产品名称": "笔记本电脑D4",
            "价格": "5999", 
            "产品描述": "轻薄本，14寸屏幕，16GB内存，512GB固态硬盘",
            "分类": "电脑办公",
            "详细信息": "处理器：Intel i7-12700H\n内存：16GB DDR4\n硬盘：512GB SSD\n显卡：集成显卡\n屏幕：14寸2K屏\n重量：1.5kg"
        }]
        
        print("修改后的单产品回复:")
        reply, _ = image_handler.format_product_reply_with_images(single_product)
        print(reply)
        
        print("\n" + "-" * 40)
        print("格式分析:")
        print("✅ 开头: '推荐你这款产品：' (更自然)")
        print("✅ 结尾: 无多余询问 (不刻意)")
        print("✅ 内容: 完整的产品信息")
        
        return True
        
    except Exception as e:
        print(f"❌ 对比测试失败: {e}")
        return False

def test_edge_cases():
    """测试边缘情况"""
    print("\n🧪 测试边缘情况")
    print("=" * 60)
    
    try:
        from src.bot.enhanced_product_image_handler import EnhancedProductImageHandler
        
        image_handler = EnhancedProductImageHandler()
        
        # 测试无产品情况
        print("1. 无产品情况:")
        reply, image_paths = image_handler.format_product_reply_with_images([])
        print(f"回复: {reply}")
        print(f"图片数量: {len(image_paths)}")
        
        # 测试产品信息不完整的情况
        print("\n2. 产品信息不完整:")
        incomplete_product = [{
            "产品名称": "测试产品",
            "价格": "",  # 空价格
            "产品描述": "",  # 空描述
            "分类": "测试分类"
            # 缺少详细信息
        }]
        
        reply, image_paths = image_handler.format_product_reply_with_images(incomplete_product)
        print(f"回复: {reply}")
        print(f"图片数量: {len(image_paths)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 边缘情况测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 回复格式修改测试")
    print()
    
    # 运行测试
    tests = [
        ("单产品回复", test_single_product_reply),
        ("多产品回复", test_multiple_products_reply),
        ("格式对比", test_reply_format_comparison),
        ("边缘情况", test_edge_cases)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n🔍 运行测试: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
            if result:
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 总结
    print(f"\n" + "=" * 60)
    print(f"          回复格式测试总结")
    print(f"=" * 60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{status} {test_name}")
    
    print(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 回复格式修改成功！")
        print("\n💡 格式改进:")
        print("   1. ✅ 单产品开头: '推荐你这款产品：' (更自然)")
        print("   2. ✅ 单产品结尾: 无多余询问 (不刻意)")
        print("   3. ✅ 多产品开头: '为您推荐X款相关产品：' (保持原样)")
        print("   4. ✅ 多产品结尾: '看看你喜欢哪款～' (保持原样)")
        print("\n🚀 现在回复更加自然了！")
    else:
        print("⚠️ 部分格式需要调整")

if __name__ == "__main__":
    main()
