"""
简化的Web测试版本
测试Web配置界面的基本功能
"""
import os
import sys

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

try:
    from flask import Flask, render_template_string, jsonify
    from config import config
    from src.bot.reply_engine import ReplyEngine
    
    app = Flask(__name__)
    app.secret_key = 'test-secret-key'
    
    # 初始化回复引擎
    reply_engine = ReplyEngine()
    
    # 简单的HTML模板
    SIMPLE_TEMPLATE = """
    <!DOCTYPE html>
    <html lang="zh-CN">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>微信客服机器人 - 测试界面</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 40px; background-color: #f5f5f5; }
            .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
            .header { text-align: center; color: #333; margin-bottom: 30px; }
            .stats { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 30px; }
            .stat-card { background: #f8f9fa; padding: 20px; border-radius: 8px; text-align: center; }
            .stat-number { font-size: 2em; font-weight: bold; color: #007bff; }
            .stat-label { color: #666; margin-top: 5px; }
            .test-section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 8px; }
            .test-input { width: 100%; padding: 10px; margin: 10px 0; border: 1px solid #ddd; border-radius: 4px; }
            .test-button { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; }
            .test-button:hover { background: #0056b3; }
            .result { margin-top: 15px; padding: 15px; background: #e9ecef; border-radius: 4px; }
            .success { color: #28a745; }
            .error { color: #dc3545; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>🤖 微信客服机器人</h1>
                <p>测试界面 - 验证核心功能</p>
            </div>
            
            <div class="stats">
                <div class="stat-card">
                    <div class="stat-number">{{ stats.faq_count }}</div>
                    <div class="stat-label">FAQ条目</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">{{ stats.product_count }}</div>
                    <div class="stat-label">产品数量</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">{{ stats.faq_categories|length }}</div>
                    <div class="stat-label">FAQ分类</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">{{ stats.product_categories|length }}</div>
                    <div class="stat-label">产品分类</div>
                </div>
            </div>
            
            <div class="test-section">
                <h3>💬 智能回复测试</h3>
                <p>输入问题，测试机器人的回复能力：</p>
                <input type="text" id="testQuestion" class="test-input" placeholder="例如：如何退货、什么时候发货、有什么优惠..." value="如何退货">
                <button class="test-button" onclick="testReply()">测试回复</button>
                <div id="replyResult" class="result" style="display:none;"></div>
            </div>
            
            <div class="test-section">
                <h3>📊 系统状态</h3>
                <p><strong>AI服务状态：</strong> 
                    <span class="{{ 'success' if stats.ai_available else 'error' }}">
                        {{ '✅ 可用' if stats.ai_available else '❌ 不可用' }}
                    </span>
                </p>
                <p><strong>FAQ分类：</strong> {{ ', '.join(stats.faq_categories) }}</p>
                <p><strong>产品分类：</strong> {{ ', '.join(stats.product_categories) }}</p>
            </div>
            
            <div class="test-section">
                <h3>🔧 快速测试</h3>
                <button class="test-button" onclick="testQuickQuestions()">测试常见问题</button>
                <div id="quickTestResult" class="result" style="display:none;"></div>
            </div>
        </div>
        
        <script>
            async function testReply() {
                const question = document.getElementById('testQuestion').value;
                const resultDiv = document.getElementById('replyResult');
                
                if (!question.trim()) {
                    alert('请输入问题');
                    return;
                }
                
                resultDiv.style.display = 'block';
                resultDiv.innerHTML = '正在生成回复...';
                
                try {
                    const response = await fetch('/api/test_reply', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ question: question })
                    });
                    
                    const data = await response.json();
                    
                    if (data.success) {
                        resultDiv.innerHTML = `
                            <strong>问题：</strong>${question}<br>
                            <strong>回复：</strong>${data.reply}<br>
                            <strong>来源：</strong>${data.source}
                        `;
                    } else {
                        resultDiv.innerHTML = `<span class="error">错误：${data.error}</span>`;
                    }
                } catch (error) {
                    resultDiv.innerHTML = `<span class="error">请求失败：${error.message}</span>`;
                }
            }
            
            async function testQuickQuestions() {
                const questions = ['如何退货', '什么时候发货', '有什么优惠', '产品质量怎么样'];
                const resultDiv = document.getElementById('quickTestResult');
                
                resultDiv.style.display = 'block';
                resultDiv.innerHTML = '正在测试...';
                
                let results = '<h4>快速测试结果：</h4>';
                
                for (const question of questions) {
                    try {
                        const response = await fetch('/api/test_reply', {
                            method: 'POST',
                            headers: { 'Content-Type': 'application/json' },
                            body: JSON.stringify({ question: question })
                        });
                        
                        const data = await response.json();
                        
                        if (data.success) {
                            results += `
                                <div style="margin: 10px 0; padding: 10px; border-left: 3px solid #007bff;">
                                    <strong>Q:</strong> ${question}<br>
                                    <strong>A:</strong> ${data.reply.substring(0, 100)}...<br>
                                    <small>来源: ${data.source}</small>
                                </div>
                            `;
                        } else {
                            results += `<div style="color: red;">问题"${question}"测试失败</div>`;
                        }
                    } catch (error) {
                        results += `<div style="color: red;">问题"${question}"请求失败</div>`;
                    }
                }
                
                resultDiv.innerHTML = results;
            }
        </script>
    </body>
    </html>
    """
    
    @app.route('/')
    def index():
        """首页"""
        try:
            stats = reply_engine.get_statistics()
            return render_template_string(SIMPLE_TEMPLATE, stats=stats)
        except Exception as e:
            return f"错误: {str(e)}"
    
    @app.route('/api/test_reply', methods=['POST'])
    def test_reply():
        """测试回复API"""
        try:
            from flask import request
            data = request.get_json()
            question = data.get('question', '')
            
            if not question:
                return jsonify({'success': False, 'error': '问题不能为空'})
            
            # 生成回复
            reply = reply_engine.generate_reply(question)
            
            # 判断回复来源
            source = "未知"
            if "FAQ" in str(reply_engine._try_faq_match(question) or ""):
                source = "FAQ库"
            elif reply_engine._try_product_match(question):
                source = "产品库"
            elif reply_engine.llm_service and reply_engine.llm_service.is_available():
                source = "AI生成"
            else:
                source = "默认回复"
            
            return jsonify({
                'success': True,
                'reply': reply,
                'source': source
            })
            
        except Exception as e:
            return jsonify({'success': False, 'error': str(e)})
    
    @app.route('/api/stats')
    def api_stats():
        """获取统计信息API"""
        try:
            stats = reply_engine.get_statistics()
            return jsonify(stats)
        except Exception as e:
            return jsonify({'error': str(e)})
    
    if __name__ == '__main__':
        print("=" * 60)
        print("🤖 微信客服机器人 - Web测试界面")
        print("=" * 60)
        print("访问地址: http://127.0.0.1:5000")
        print("功能: 测试回复引擎和数据库功能")
        print("按 Ctrl+C 退出")
        print("=" * 60)
        
        app.run(host='127.0.0.1', port=5000, debug=True)

except ImportError as e:
    print(f"导入失败: {e}")
    print("请确保已安装所有依赖包：python install_deps.py")
except Exception as e:
    print(f"启动失败: {e}")
