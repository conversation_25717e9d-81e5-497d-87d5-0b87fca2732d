#!/usr/bin/env python3
"""
实时监控微信PC端性能
"""
import sys
import time
import psutil
import os
from pathlib import Path

# 添加项目路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def clear_screen():
    """清屏"""
    os.system('cls' if os.name == 'nt' else 'clear')

def find_wechat_processes():
    """查找微信进程"""
    processes = []
    for proc in psutil.process_iter(['pid', 'name', 'cpu_percent', 'memory_info', 'create_time']):
        try:
            if 'wechat' in proc.info['name'].lower() or 'weixin' in proc.info['name'].lower():
                processes.append(proc)
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            continue
    return processes

def get_process_info(proc):
    """获取进程详细信息"""
    try:
        cpu_percent = proc.cpu_percent()
        memory_info = proc.memory_info()
        memory_mb = memory_info.rss / 1024 / 1024
        
        # 计算运行时间
        create_time = proc.create_time()
        run_time = time.time() - create_time
        run_hours = int(run_time // 3600)
        run_minutes = int((run_time % 3600) // 60)
        
        return {
            'pid': proc.pid,
            'name': proc.info['name'],
            'cpu_percent': cpu_percent,
            'memory_mb': memory_mb,
            'run_time': f"{run_hours}h {run_minutes}m",
            'status': proc.status()
        }
    except (psutil.NoSuchProcess, psutil.AccessDenied):
        return None

def check_wechat_responsiveness():
    """检查微信响应性"""
    try:
        import wxauto
        start_time = time.time()
        wx = wxauto.WeChat()
        response_time = time.time() - start_time
        
        if wx:
            return {
                'connected': True,
                'response_time': response_time,
                'status': 'normal' if response_time < 2 else 'slow'
            }
        else:
            return {
                'connected': False,
                'response_time': response_time,
                'status': 'failed'
            }
    except Exception as e:
        return {
            'connected': False,
            'response_time': 0,
            'status': 'error',
            'error': str(e)
        }

def monitor_realtime():
    """实时监控"""
    print("开始实时监控微信性能...")
    print("按 Ctrl+C 停止监控")
    
    try:
        while True:
            clear_screen()
            
            print("=" * 80)
            print("                    微信PC端实时性能监控")
            print("=" * 80)
            print(f"监控时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
            
            # 系统整体性能
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            
            print(f"\n系统性能:")
            print(f"  CPU使用率: {cpu_percent:.1f}%")
            print(f"  内存使用率: {memory.percent:.1f}% ({memory.used/1024/1024/1024:.1f}GB / {memory.total/1024/1024/1024:.1f}GB)")
            
            # 微信进程信息
            wechat_processes = find_wechat_processes()
            
            if wechat_processes:
                print(f"\n微信进程 ({len(wechat_processes)} 个):")
                print("-" * 80)
                print(f"{'PID':<8} {'进程名':<20} {'CPU%':<8} {'内存(MB)':<12} {'运行时间':<12} {'状态':<10}")
                print("-" * 80)
                
                total_cpu = 0
                total_memory = 0
                
                for proc in wechat_processes:
                    info = get_process_info(proc)
                    if info:
                        print(f"{info['pid']:<8} {info['name']:<20} {info['cpu_percent']:<8.1f} {info['memory_mb']:<12.1f} {info['run_time']:<12} {info['status']:<10}")
                        total_cpu += info['cpu_percent']
                        total_memory += info['memory_mb']
                
                print("-" * 80)
                print(f"{'总计':<8} {'':<20} {total_cpu:<8.1f} {total_memory:<12.1f}")
                
                # 性能评估
                print(f"\n性能评估:")
                if total_cpu < 5:
                    cpu_status = "✅ 正常"
                elif total_cpu < 15:
                    cpu_status = "⚠️  偏高"
                else:
                    cpu_status = "❌ 过高"
                
                if total_memory < 500:
                    memory_status = "✅ 正常"
                elif total_memory < 1000:
                    memory_status = "⚠️  偏高"
                else:
                    memory_status = "❌ 过高"
                
                print(f"  CPU使用: {cpu_status}")
                print(f"  内存使用: {memory_status}")
                
            else:
                print(f"\n❌ 未检测到微信进程")
                print("请检查微信PC端是否正常运行")
            
            # 微信响应性测试
            print(f"\n微信响应性测试:")
            responsiveness = check_wechat_responsiveness()
            
            if responsiveness['connected']:
                if responsiveness['status'] == 'normal':
                    status_icon = "✅"
                else:
                    status_icon = "⚠️ "
                print(f"  连接状态: {status_icon} 已连接")
                print(f"  响应时间: {responsiveness['response_time']:.2f}秒")
            else:
                print(f"  连接状态: ❌ 连接失败")
                if 'error' in responsiveness:
                    print(f"  错误信息: {responsiveness['error']}")
            
            # 建议
            print(f"\n监控建议:")
            if wechat_processes and total_cpu > 10:
                print("  • CPU使用率较高，建议检查是否有大量消息处理")
            if wechat_processes and total_memory > 800:
                print("  • 内存使用较高，建议重启微信释放内存")
            if not responsiveness['connected']:
                print("  • 微信连接失败，可能影响机器人功能")
            if wechat_processes and responsiveness['connected']:
                print("  • 微信运行正常，可以安全使用机器人")
            
            print(f"\n按 Ctrl+C 停止监控")
            
            # 等待5秒后刷新
            time.sleep(5)
            
    except KeyboardInterrupt:
        print(f"\n监控已停止")

def quick_check():
    """快速检查微信状态"""
    print("=" * 60)
    print("          微信PC端快速状态检查")
    print("=" * 60)
    
    # 检查进程
    wechat_processes = find_wechat_processes()
    
    if not wechat_processes:
        print("❌ 未检测到微信进程")
        print("建议: 启动微信PC端")
        return False
    
    print(f"✅ 检测到 {len(wechat_processes)} 个微信进程")
    
    # 检查性能
    total_cpu = 0
    total_memory = 0
    
    for proc in wechat_processes:
        info = get_process_info(proc)
        if info:
            total_cpu += info['cpu_percent']
            total_memory += info['memory_mb']
    
    print(f"CPU使用率: {total_cpu:.1f}%")
    print(f"内存使用: {total_memory:.1f}MB")
    
    # 检查响应性
    print("检查微信响应性...")
    responsiveness = check_wechat_responsiveness()
    
    if responsiveness['connected']:
        print(f"✅ 微信连接正常 (响应时间: {responsiveness['response_time']:.2f}秒)")
        
        # 综合评估
        if total_cpu < 10 and total_memory < 600 and responsiveness['response_time'] < 2:
            print("\n🎉 微信状态优秀，适合运行机器人")
            return True
        elif total_cpu < 20 and total_memory < 1000 and responsiveness['response_time'] < 5:
            print("\n⚠️  微信状态良好，可以运行机器人但需监控")
            return True
        else:
            print("\n❌ 微信状态不佳，建议优化后再运行机器人")
            return False
    else:
        print("❌ 微信连接失败")
        return False

def main():
    """主函数"""
    print("微信PC端性能监控工具")
    print("1. 快速检查")
    print("2. 实时监控")
    
    choice = input("请选择 (1/2): ").strip()
    
    if choice == "1":
        if quick_check():
            print("\n可以安全启动机器人:")
            print("python quick_start.py")
    elif choice == "2":
        monitor_realtime()
    else:
        print("无效选择")

if __name__ == "__main__":
    main()
