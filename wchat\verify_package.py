#!/usr/bin/env python3
"""
验证WChat部署包完整性
"""
import os
import sys
import zipfile
import json
from pathlib import Path

def verify_package():
    """验证部署包"""
    print("🔍 WChat部署包验证工具")
    print("=" * 60)
    
    # 查找最新的部署包
    dist_dir = Path(__file__).parent / "dist"
    if not dist_dir.exists():
        print("❌ 未找到dist目录")
        return False
    
    zip_files = list(dist_dir.glob("wchat_deploy_*.zip"))
    if not zip_files:
        print("❌ 未找到部署包")
        return False
    
    # 选择最新的包
    latest_zip = max(zip_files, key=lambda x: x.stat().st_mtime)
    print(f"📦 验证包: {latest_zip.name}")
    print(f"📁 大小: {latest_zip.stat().st_size / (1024*1024):.1f} MB")
    
    # 验证ZIP文件
    try:
        with zipfile.ZipFile(latest_zip, 'r') as zipf:
            file_list = zipf.namelist()
            print(f"📋 包含文件: {len(file_list)} 个")
            
            # 检查必需文件
            required_files = [
                "quick_start.py",
                "requirements.txt", 
                "config/config.json",
                "data/faq.xlsx",
                "data/products.xlsx",
                "install.bat",
                "install.sh",
                "使用说明.md"
            ]
            
            print(f"\n✅ 必需文件检查:")
            missing_files = []
            for req_file in required_files:
                # 查找匹配的文件（考虑目录前缀）
                matches = [f for f in file_list if f.endswith(req_file)]
                if matches:
                    print(f"  ✅ {req_file}")
                else:
                    print(f"  ❌ {req_file}")
                    missing_files.append(req_file)
            
            if missing_files:
                print(f"\n❌ 缺少文件: {missing_files}")
                return False
            
            # 检查源代码目录
            src_files = [f for f in file_list if "/src/" in f]
            print(f"\n📁 源代码文件: {len(src_files)} 个")
            
            # 检查数据文件
            data_files = [f for f in file_list if "/data/" in f]
            print(f"📁 数据文件: {len(data_files)} 个")
            
            # 检查图片文件
            image_files = [f for f in file_list if f.endswith(('.jpg', '.jpeg', '.png', '.gif'))]
            print(f"🖼️ 图片文件: {len(image_files)} 个")
            
            print(f"\n✅ ZIP文件验证通过")
            
    except Exception as e:
        print(f"❌ ZIP文件验证失败: {e}")
        return False
    
    # 验证解压目录（如果存在）
    extract_dir = latest_zip.parent / latest_zip.stem
    if extract_dir.exists():
        print(f"\n📂 验证解压目录: {extract_dir.name}")
        
        # 检查配置文件
        config_file = extract_dir / "config" / "config.json"
        if config_file.exists():
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                
                # 检查API密钥是否已清理
                api_key = config.get('ai', {}).get('api_key', '')
                if not api_key:
                    print(f"  ✅ API密钥已清理")
                else:
                    print(f"  ⚠️ API密钥未清理")
                
                # 检查配置完整性
                required_sections = ['wechat', 'ai', 'database', 'reply', 'web']
                for section in required_sections:
                    if section in config:
                        print(f"  ✅ 配置节: {section}")
                    else:
                        print(f"  ❌ 缺少配置节: {section}")
                
            except Exception as e:
                print(f"  ❌ 配置文件验证失败: {e}")
        
        # 检查启动脚本
        start_scripts = [
            "quick_start.py",
            "install.bat", 
            "install.sh"
        ]
        
        for script in start_scripts:
            script_path = extract_dir / script
            if script_path.exists():
                print(f"  ✅ 启动脚本: {script}")
            else:
                print(f"  ❌ 缺少脚本: {script}")
    
    return True

def create_deployment_summary():
    """创建部署总结"""
    print(f"\n📋 创建部署总结...")
    
    dist_dir = Path(__file__).parent / "dist"
    zip_files = list(dist_dir.glob("wchat_deploy_*.zip"))
    
    if not zip_files:
        return
    
    latest_zip = max(zip_files, key=lambda x: x.stat().st_mtime)
    
    summary = f"""# WChat机器人部署包总结

## 📦 部署包信息
- **文件名**: {latest_zip.name}
- **大小**: {latest_zip.stat().st_size / (1024*1024):.1f} MB
- **创建时间**: {latest_zip.stat().st_mtime}
- **位置**: {latest_zip.absolute()}

## 🎯 功能特点
- ✅ AI智能聊天 (硅基流动API)
- ✅ 产品推荐 + 图片发送
- ✅ 语音消息转文字
- ✅ FAQ自动回复
- ✅ Web配置界面
- ✅ 实时监控面板

## 🚀 部署步骤
1. 复制 `{latest_zip.name}` 到目标电脑
2. 解压到任意目录
3. 运行 `install.bat` (Windows) 或 `install.sh` (Linux)
4. 配置API密钥到 `config/config.json`
5. 运行 `python quick_start.py` 启动

## 📋 系统要求
- Python 3.8+
- 2GB+ 可用内存
- 稳定网络连接
- 微信PC版

## 🔧 配置要点
- 硅基流动API密钥 (必需)
- 微信自动回复设置
- 语音转文字功能
- Web界面访问密码

## 📞 技术支持
- 查看 `使用说明.md` 获取详细说明
- 检查 `data/logs/` 目录查看运行日志
- 访问 http://localhost:5000 进行Web配置

**部署包已准备就绪，可以在其他电脑上使用！** 🎉
"""
    
    summary_file = dist_dir / "部署总结.md"
    with open(summary_file, 'w', encoding='utf-8') as f:
        f.write(summary)
    
    print(f"  ✅ {summary_file.name}")

def main():
    """主函数"""
    try:
        success = verify_package()
        
        if success:
            create_deployment_summary()
            
            print(f"\n" + "=" * 60)
            print(f"          验证完成！")
            print(f"=" * 60)
            print(f"✅ 部署包验证通过")
            print(f"✅ 所有必需文件完整")
            print(f"✅ 配置文件格式正确")
            print(f"✅ 启动脚本齐全")
            print(f"\n🎉 WChat机器人部署包已准备就绪！")
            print(f"\n📋 下一步:")
            print(f"   1. 将ZIP文件复制到目标电脑")
            print(f"   2. 按照使用说明进行部署")
            print(f"   3. 配置API密钥后启动使用")
        else:
            print(f"\n❌ 验证失败，请检查打包过程")
            
    except Exception as e:
        print(f"❌ 验证异常: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
