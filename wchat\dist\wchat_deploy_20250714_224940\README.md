# 微信客服回复机器人 (WChat)

## 项目简介

基于原KouriChat项目简化而来的微信客服回复机器人，专注于客服场景，具备以下核心功能：

- 🤖 智能客服回复：优先使用FAQ库和产品库内容
- 📊 Excel数据库：FAQ问答库和产品信息库
- 🧠 AI辅助回复：库中无匹配内容时使用大模型
- 🌐 Web配置界面：简洁的配置管理界面
- 💬 微信集成：无缝接入微信消息处理

## 项目架构

```
wchat/
├── README.md                   # 项目说明
├── requirements.txt            # 依赖包
├── run.py                     # 主程序入口
├── config/
│   ├── __init__.py
│   ├── config.json            # 配置文件
│   └── config_template.json   # 配置模板
├── data/
│   ├── faq.xlsx              # FAQ问答库
│   ├── products.xlsx         # 产品信息库
│   └── logs/                 # 日志文件
├── src/
│   ├── __init__.py
│   ├── bot/                  # 机器人核心
│   │   ├── __init__.py
│   │   ├── wechat_handler.py # 微信消息处理
│   │   └── reply_engine.py   # 回复引擎
│   ├── database/             # 数据库模块
│   │   ├── __init__.py
│   │   ├── excel_reader.py   # Excel读取
│   │   └── matcher.py        # 内容匹配
│   ├── ai/                   # AI服务
│   │   ├── __init__.py
│   │   └── llm_service.py    # 大模型服务
│   ├── web/                  # Web界面
│   │   ├── __init__.py
│   │   ├── app.py           # Flask应用
│   │   ├── static/          # 静态文件
│   │   └── templates/       # 模板文件
│   └── utils/               # 工具模块
│       ├── __init__.py
│       └── logger.py        # 日志工具
└── web_config.py            # Web配置启动
```

## 快速开始

### 环境准备

1. Python 3.8+
2. 微信PC版（需要登录状态）
3. AI API密钥（DeepSeek、OpenAI等）

### 安装依赖

```bash
pip install -r requirements.txt
```

### 配置设置

```bash
# 启动Web配置界面
python web_config.py
```

### 运行机器人

```bash
python run.py
```

## 功能特点

### 智能回复优先级

1. **FAQ库匹配**：优先匹配FAQ问答库中的内容
2. **产品库匹配**：查找产品信息库中的相关信息
3. **AI辅助回复**：当库中无匹配内容时，使用大模型生成回复

### Excel数据库格式

#### FAQ库 (faq.xlsx)
| 问题关键词 | 标准问题 | 回复内容 | 分类 | 状态 |
|-----------|---------|---------|------|------|
| 退货,退款 | 如何申请退货退款？ | 您可以在订单页面... | 售后 | 启用 |

#### 产品库 (products.xlsx)
| 产品名称 | 产品描述 | 价格 | 分类 | 详细信息 | 状态 |
|---------|---------|------|------|---------|------|
| 产品A | 高质量产品A | 99.99 | 电子产品 | 详细规格... | 上架 |

## 配置说明

主要配置项：
- 微信监听列表
- AI模型设置
- 回复策略配置
- 数据库文件路径

## 注意事项

- 确保微信PC版处于登录状态
- Excel文件格式需要严格按照模板
- 建议定期备份配置和数据文件
