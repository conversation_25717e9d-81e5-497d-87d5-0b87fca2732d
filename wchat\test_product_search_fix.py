#!/usr/bin/env python3
"""
测试产品搜索修复效果
"""
import os
import sys
from pathlib import Path

# 添加项目路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def test_product_search_fix():
    """测试产品搜索修复效果"""
    print("🔧 测试产品搜索修复效果")
    print("=" * 60)
    
    try:
        from src.bot.enhanced_reply_engine_with_images import EnhancedReplyEngineWithImages
        
        # 创建回复引擎
        reply_engine = EnhancedReplyEngineWithImages()
        
        # 测试场景
        test_scenarios = [
            {
                "query": "电脑",
                "expected": ["笔记本电脑D4"],
                "not_expected": ["游戏鼠标F6", "智能手机A1"],
                "description": "用户问电脑，应该只推荐电脑产品，不推荐配件"
            },
            {
                "query": "推荐一款电脑",
                "expected": ["笔记本电脑D4"],
                "not_expected": ["游戏鼠标F6"],
                "description": "用户要电脑推荐，应该只推荐电脑"
            },
            {
                "query": "鼠标",
                "expected": ["游戏鼠标F6"],
                "not_expected": ["笔记本电脑D4"],
                "description": "用户问鼠标，应该推荐鼠标产品"
            },
            {
                "query": "手机",
                "expected": ["智能手机A1"],
                "not_expected": ["游戏鼠标F6", "笔记本电脑D4"],
                "description": "用户问手机，应该推荐手机产品"
            },
            {
                "query": "耳机",
                "expected": ["无线蓝牙耳机B2"],
                "not_expected": ["游戏鼠标F6", "笔记本电脑D4"],
                "description": "用户问耳机，应该推荐耳机产品"
            }
        ]
        
        all_passed = True
        
        for i, scenario in enumerate(test_scenarios, 1):
            print(f"\n场景 {i}: {scenario['description']}")
            print(f"查询: '{scenario['query']}'")
            
            # 生成回复
            reply, image_paths = reply_engine.generate_reply_with_images(scenario['query'])
            
            if reply:
                print(f"回复: {reply[:100]}...")
                
                # 检查期望的产品是否出现
                expected_found = []
                for expected in scenario['expected']:
                    if expected in reply:
                        expected_found.append(expected)
                        print(f"  ✅ 找到期望产品: {expected}")
                    else:
                        print(f"  ❌ 未找到期望产品: {expected}")
                        all_passed = False
                
                # 检查不期望的产品是否出现
                unexpected_found = []
                for not_expected in scenario['not_expected']:
                    if not_expected in reply:
                        unexpected_found.append(not_expected)
                        print(f"  ❌ 意外出现产品: {not_expected}")
                        all_passed = False
                    else:
                        print(f"  ✅ 正确排除产品: {not_expected}")
                
                # 检查图片
                print(f"  📷 图片数量: {len(image_paths)}")
                for img_path in image_paths:
                    filename = os.path.basename(img_path)
                    print(f"    - {filename}")
                
                # 场景结果
                if len(expected_found) == len(scenario['expected']) and len(unexpected_found) == 0:
                    print(f"  🎉 场景 {i} 通过")
                else:
                    print(f"  ❌ 场景 {i} 失败")
                    all_passed = False
            else:
                print(f"  ❌ 无回复")
                all_passed = False
        
        # 总结
        print(f"\n" + "=" * 60)
        print(f"          修复效果总结")
        print(f"=" * 60)
        
        if all_passed:
            print("🎉 所有测试场景都通过了！")
            print("✅ 产品搜索逻辑修复成功")
            print("✅ 用户问'电脑'不再推荐鼠标")
            print("✅ 产品分类匹配更加精确")
        else:
            print("⚠️ 部分测试场景失败，需要进一步调整")
        
        return all_passed
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_edge_cases():
    """测试边缘情况"""
    print("\n🧪 测试边缘情况")
    print("=" * 60)
    
    try:
        from src.database.enhanced_reader import EnhancedProductReader
        from config import config
        
        product_reader = EnhancedProductReader(config.database.products_file)
        
        # 边缘情况测试
        edge_cases = [
            "电脑配件",      # 明确要配件
            "电脑鼠标",      # 组合查询
            "游戏电脑",      # 特定类型电脑
            "办公电脑",      # 特定用途电脑
            "电脑键盘",      # 另一种配件
        ]
        
        for query in edge_cases:
            print(f"\n查询: '{query}'")
            products = product_reader.search_products(query)
            
            if products:
                print(f"  找到 {len(products)} 个产品:")
                for product in products:
                    print(f"    - {product['产品名称']} (分数: {product['score']:.3f})")
            else:
                print(f"  无匹配产品")
        
        return True
        
    except Exception as e:
        print(f"❌ 边缘情况测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 产品搜索修复验证")
    print()
    
    # 运行测试
    tests = [
        ("修复效果", test_product_search_fix),
        ("边缘情况", test_edge_cases)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n🔍 运行测试: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
            if result:
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 总结
    print(f"\n" + "=" * 60)
    print(f"          最终总结")
    print(f"=" * 60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{status} {test_name}")
    
    print(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 产品搜索逻辑修复完成！")
        print("\n💡 修复内容:")
        print("   1. 严格区分主产品和配件产品")
        print("   2. 用户问'电脑'只推荐电脑，不推荐配件")
        print("   3. 用户问'鼠标'才推荐鼠标配件")
        print("   4. 提高产品分类匹配的精确度")
        print("\n🚀 现在产品推荐更加准确了！")
    else:
        print("⚠️ 部分功能仍需优化，请检查相关逻辑。")

if __name__ == "__main__":
    main()
