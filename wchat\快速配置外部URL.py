#!/usr/bin/env python3
"""
私域自动化系统 - 快速配置外部URL工具
"""

import json
import requests
import socket
from pathlib import Path

def get_public_ip():
    """获取公网IP"""
    try:
        response = requests.get("https://api.ipify.org", timeout=10)
        return response.text.strip()
    except:
        try:
            response = requests.get("https://ifconfig.me", timeout=10)
            return response.text.strip()
        except:
            return None

def get_local_ip():
    """获取本机IP"""
    try:
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        ip = s.getsockname()[0]
        s.close()
        return ip
    except:
        return "127.0.0.1"

def load_config():
    """加载配置"""
    config_file = Path("config/config.json")
    if config_file.exists():
        with open(config_file, 'r', encoding='utf-8') as f:
            return json.load(f)
    return None

def save_config(config):
    """保存配置"""
    config_file = Path("config/config.json")
    with open(config_file, 'w', encoding='utf-8') as f:
        json.dump(config, f, indent=2, ensure_ascii=False)

def main():
    print("=" * 60)
    print("           快速配置外部URL工具")
    print("=" * 60)
    print()
    
    # 获取网络信息
    print("🌐 正在获取网络信息...")
    local_ip = get_local_ip()
    public_ip = get_public_ip()
    
    print(f"   本机IP: {local_ip}")
    if public_ip:
        print(f"   公网IP: {public_ip}")
    else:
        print("   公网IP: 获取失败")
    print()
    
    # 加载当前配置
    config = load_config()
    if not config:
        print("❌ 配置文件不存在")
        return
    
    current_url = config.get('web', {}).get('external_url', '')
    port = config.get('web', {}).get('port', 5000)
    
    print(f"📋 当前配置:")
    print(f"   端口: {port}")
    print(f"   外部URL: {current_url or '未设置'}")
    print()
    
    # 提供配置选项
    print("🔧 配置选项:")
    print("1. 使用公网IP配置")
    print("2. 使用域名配置")
    print("3. 使用动态域名配置")
    print("4. 手动输入URL")
    print("5. 清除外部URL配置")
    print("0. 退出")
    print()
    
    choice = input("请选择配置方式 (0-5): ").strip()
    
    new_url = ""
    
    if choice == "1":
        # 使用公网IP
        if public_ip:
            new_url = f"http://{public_ip}:{port}"
            print(f"✅ 将设置为: {new_url}")
        else:
            print("❌ 无法获取公网IP")
            return
    
    elif choice == "2":
        # 使用域名
        domain = input("请输入您的域名 (如: yourdomain.com): ").strip()
        if domain:
            protocol = input("使用HTTPS? (y/n, 默认HTTP): ").strip().lower()
            protocol = "https" if protocol == 'y' else "http"
            new_url = f"{protocol}://{domain}:{port}"
            print(f"✅ 将设置为: {new_url}")
        else:
            print("❌ 域名不能为空")
            return
    
    elif choice == "3":
        # 使用动态域名
        print("\n常用动态域名服务:")
        print("- No-IP: yourhost.no-ip.org")
        print("- DynDNS: yourhost.ddns.net")
        print("- DuckDNS: yourhost.duckdns.org")
        
        ddns_host = input("\n请输入动态域名 (如: myhost.no-ip.org): ").strip()
        if ddns_host:
            protocol = input("使用HTTPS? (y/n, 默认HTTP): ").strip().lower()
            protocol = "https" if protocol == 'y' else "http"
            new_url = f"{protocol}://{ddns_host}:{port}"
            print(f"✅ 将设置为: {new_url}")
        else:
            print("❌ 动态域名不能为空")
            return
    
    elif choice == "4":
        # 手动输入
        new_url = input("请输入完整的外部URL (如: http://example.com:5000): ").strip()
        if not new_url:
            print("❌ URL不能为空")
            return
        print(f"✅ 将设置为: {new_url}")
    
    elif choice == "5":
        # 清除配置
        new_url = ""
        print("✅ 将清除外部URL配置")
    
    elif choice == "0":
        print("👋 退出配置")
        return
    
    else:
        print("❌ 无效选择")
        return
    
    # 确认配置
    if new_url:
        print(f"\n📝 新的外部URL: {new_url}")
    else:
        print(f"\n📝 将清除外部URL配置")
    
    confirm = input("确认保存配置? (y/n): ").strip().lower()
    if confirm != 'y':
        print("❌ 配置已取消")
        return
    
    # 保存配置
    config['web']['external_url'] = new_url
    save_config(config)
    
    print("✅ 外部URL配置已保存")
    
    # 显示访问信息
    print("\n🌐 访问地址:")
    print(f"   本地访问: http://127.0.0.1:{port}")
    print(f"   局域网访问: http://{local_ip}:{port}")
    if new_url:
        print(f"   外部访问: {new_url}")
    
    print("\n💡 提醒:")
    print("   1. 确保防火墙已开放端口")
    print("   2. 配置路由器端口映射")
    print("   3. 重启服务使配置生效")
    
    if new_url and not new_url.startswith('https://'):
        print("   4. 生产环境建议使用HTTPS")

if __name__ == "__main__":
    main()
