#!/usr/bin/env python3
"""
测试修复后的产品回复
"""
import sys
from pathlib import Path

# 添加项目路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def test_product_replies():
    """测试产品回复"""
    print("=" * 60)
    print("          测试修复后的产品回复")
    print("=" * 60)
    
    try:
        from src.bot.enhanced_reply_engine import EnhancedReplyEngine
        
        # 创建回复引擎
        reply_engine = EnhancedReplyEngine()
        print("✅ 增强回复引擎创建成功")
        
        # 测试不同的产品查询
        test_cases = [
            {
                "query": "手机",
                "description": "单个产品推荐测试"
            },
            {
                "query": "电脑",
                "description": "多个产品推荐测试"
            },
            {
                "query": "耳机",
                "description": "耳机产品推荐测试"
            },
            {
                "query": "智能手表",
                "description": "手表产品推荐测试"
            },
            {
                "query": "平板电脑",
                "description": "无匹配产品测试"
            }
        ]
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n测试 {i}: {test_case['query']}")
            print(f"说明: {test_case['description']}")
            print("-" * 50)
            
            try:
                reply = reply_engine.generate_reply(test_case['query'])
                print(f"回复:\n{reply}")
                
                # 检查回复质量
                issues = []
                if "联系客服" in reply:
                    issues.append("❌ 仍包含'联系客服'")
                if "为您找到" in reply:
                    issues.append("❌ 仍使用'为您找到'")
                if "为您推荐" in reply:
                    issues.append("✅ 使用'为您推荐'")
                if "哪款" in reply or "感兴趣" in reply or "怎么样" in reply:
                    issues.append("✅ 包含互动询问")
                
                if issues:
                    print("\n检查结果:")
                    for issue in issues:
                        print(f"  {issue}")
                
            except Exception as e:
                print(f"❌ 测试失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 产品回复测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_faq_replies():
    """测试FAQ回复是否也需要修复"""
    print("\n" + "=" * 60)
    print("          测试FAQ回复")
    print("=" * 60)
    
    try:
        from src.bot.enhanced_reply_engine import EnhancedReplyEngine
        
        # 创建回复引擎
        reply_engine = EnhancedReplyEngine()
        
        # 测试FAQ查询
        faq_queries = [
            "如何退货",
            "什么时候发货",
            "有什么优惠",
            "怎么联系客服"
        ]
        
        for query in faq_queries:
            print(f"\n测试FAQ: {query}")
            print("-" * 30)
            
            try:
                reply = reply_engine.generate_reply(query)
                print(f"回复: {reply}")
                
                # 检查是否有不必要的客服引导
                if "联系客服" in reply and query != "怎么联系客服":
                    print("⚠️  FAQ回复中包含'联系客服'，可能需要优化")
                
            except Exception as e:
                print(f"❌ FAQ测试失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ FAQ回复测试失败: {e}")
        return False

def test_direct_product_format():
    """直接测试产品格式化函数"""
    print("\n" + "=" * 60)
    print("          直接测试产品格式化函数")
    print("=" * 60)
    
    try:
        from src.database.enhanced_reader import format_product_reply
        
        # 测试单个产品
        single_product = [{
            'name': '智能手机A1',
            'price': '2999',
            'description': '高性能智能手机，拍照清晰',
            'stock': '有货',
            'details': '6.1英寸屏幕，128GB存储'
        }]
        
        print("单个产品格式化测试:")
        reply = format_product_reply(single_product)
        print(reply)
        
        # 测试多个产品
        multiple_products = [
            {
                'name': '笔记本电脑D4',
                'price': '4999',
                'description': '轻薄便携，办公首选',
                'stock': '有货',
                'details': '14英寸，8GB内存'
            },
            {
                'name': '游戏电脑G1',
                'price': '7999',
                'description': '高性能游戏电脑',
                'stock': '有货',
                'details': '16英寸，16GB内存'
            }
        ]
        
        print("\n" + "-" * 50)
        print("多个产品格式化测试:")
        reply = format_product_reply(multiple_products)
        print(reply)
        
        # 测试无产品
        print("\n" + "-" * 50)
        print("无产品格式化测试:")
        reply = format_product_reply([])
        print(reply)
        
        return True
        
    except Exception as e:
        print(f"❌ 直接格式化测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("开始测试修复后的产品回复...")
    
    # 测试产品回复
    if test_product_replies():
        print("\n✅ 产品回复测试完成")
    else:
        print("\n❌ 产品回复测试失败")
        return
    
    # 测试FAQ回复
    if test_faq_replies():
        print("\n✅ FAQ回复测试完成")
    else:
        print("\n❌ FAQ回复测试失败")
    
    # 测试直接格式化
    if test_direct_product_format():
        print("\n✅ 直接格式化测试完成")
    else:
        print("\n❌ 直接格式化测试失败")
    
    print("\n" + "=" * 60)
    print("测试完成！")
    print("=" * 60)
    
    print("\n修复内容:")
    print("1. ✅ 去掉了'联系客服'的提示")
    print("2. ✅ 改进开场白：'为您找到' → '为您推荐'")
    print("3. ✅ 增加互动询问：'请问您对哪款产品感兴趣？'")
    print("4. ✅ 优化无产品时的回复")
    print("5. ✅ 根据产品数量调整回复风格")

if __name__ == "__main__":
    main()
