<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}私域自动化{% endblock %}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        .sidebar {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .sidebar .nav-link {
            color: rgba(255, 255, 255, 0.8);
            border-radius: 8px;
            margin: 2px 0;
        }
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            color: white;
            background-color: rgba(255, 255, 255, 0.1);
        }
        .main-content {
            background-color: #f8f9fa;
            min-height: 100vh;
        }
        .card {
            border: none;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
        }
        .btn-primary:hover {
            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
        }
        .navbar-brand {
            font-weight: bold;
            color: white !important;
        }
        .alert {
            border-radius: 8px;
        }
    </style>
    
    {% block extra_css %}{% endblock %}
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="position-sticky pt-3">
                    <div class="text-center mb-4">
                        <h4 class="navbar-brand">
                            <i class="fas fa-robot me-2"></i>
                            WChat
                        </h4>
                        <small class="text-white-50">私域自动化</small>
                    </div>
                    
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link {% if request and request.endpoint == 'dashboard' %}active{% endif %}" href="{{ url_for('dashboard') }}">
                                <i class="fas fa-tachometer-alt me-2"></i>
                                仪表板
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if request and request.endpoint == 'config_page' %}active{% endif %}" href="{{ url_for('config_page') }}">
                                <i class="fas fa-cog me-2"></i>
                                基础配置
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if request and request.endpoint == 'faq_page' %}active{% endif %}" href="{{ url_for('faq_page') }}">
                                <i class="fas fa-question-circle me-2"></i>
                                FAQ管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if request and request.endpoint == 'products_page' %}active{% endif %}" href="{{ url_for('products_page') }}">
                                <i class="fas fa-box me-2"></i>
                                产品管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if request and request.endpoint == 'data_management' %}active{% endif %}" href="{{ url_for('data_management') }}">
                                <i class="fas fa-database me-2"></i>
                                数据管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if request and request.endpoint == 'license_management' %}active{% endif %}" href="{{ url_for('license_management') }}">
                                <i class="fas fa-key me-2"></i>
                                许可证管理
                            </a>
                        </li>
                        {% if session.get('is_admin') %}
                        <li class="nav-item">
                            <a class="nav-link {% if request and request.endpoint == 'license_generator_page' %}active{% endif %}" href="{{ url_for('license_generator_page') }}">
                                <i class="fas fa-certificate me-2"></i>
                                许可证生成器
                                <span class="badge bg-warning text-dark ms-1">管理员</span>
                            </a>
                        </li>
                        {% endif %}
                        <li class="nav-item mt-3">
                            <div class="nav-link text-muted">
                                <i class="fas fa-user me-2"></i>
                                当前用户:
                                {% if session.get('is_admin') %}
                                    <span class="badge bg-warning text-dark">管理员</span>
                                {% else %}
                                    <span class="badge bg-secondary">普通用户</span>
                                {% endif %}
                            </div>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link text-warning" href="{{ url_for('logout') }}">
                                <i class="fas fa-sign-out-alt me-2"></i>
                                退出登录
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>
            
            <!-- 主内容区 -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 main-content">
                <div class="pt-3 pb-2 mb-3">
                    {% with messages = get_flashed_messages(with_categories=true) %}
                        {% if messages %}
                            {% for category, message in messages %}
                                <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                                    {{ message }}
                                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                </div>
                            {% endfor %}
                        {% endif %}
                    {% endwith %}
                    
                    {% block content %}{% endblock %}
                </div>
            </main>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    
    {% block extra_js %}{% endblock %}
</body>
</html>
