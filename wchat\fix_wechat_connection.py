#!/usr/bin/env python3
"""
修复微信连接问题
"""
import os
import sys
from pathlib import Path

# 添加项目路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def test_wechat_connection():
    """测试微信连接"""
    print("🔧 测试微信连接")
    print("=" * 50)
    
    try:
        import wxauto
        from wxauto import WeChat
        
        print("1. wxauto库检查")
        print(f"   ✅ wxauto版本: {wxauto.__version__ if hasattr(wxauto, '__version__') else '未知'}")
        print(f"   ✅ wxauto路径: {wxauto.__file__}")
        
        print("\n2. 微信连接测试")
        try:
            wx = WeChat()
            print(f"   ✅ 微信对象创建成功: {type(wx)}")
            
            # 检查微信状态
            try:
                nickname = wx.nickname
                if nickname:
                    print(f"   ✅ 微信已登录，用户: {nickname}")
                    return True
                else:
                    print(f"   ❌ 微信未登录或获取用户信息失败")
                    return False
            except Exception as e:
                print(f"   ❌ 获取微信状态失败: {e}")
                return False
                
        except Exception as e:
            print(f"   ❌ 微信对象创建失败: {e}")
            return False
            
    except ImportError as e:
        print(f"❌ wxauto导入失败: {e}")
        return False

def check_wechat_process():
    """检查微信进程"""
    print("\n🔍 检查微信进程")
    print("=" * 50)
    
    try:
        import psutil
        
        wechat_processes = []
        for proc in psutil.process_iter(['pid', 'name', 'exe']):
            try:
                if 'wechat' in proc.info['name'].lower():
                    wechat_processes.append(proc.info)
            except:
                continue
        
        if wechat_processes:
            print("找到微信进程:")
            for proc in wechat_processes:
                print(f"   ✅ PID: {proc['pid']}, 名称: {proc['name']}")
            return True
        else:
            print("❌ 未找到微信进程")
            print("💡 请确保微信PC版已启动并登录")
            return False
            
    except ImportError:
        print("⚠️ psutil未安装，无法检查进程")
        print("💡 可以手动检查微信是否运行")
        return True
    except Exception as e:
        print(f"❌ 进程检查失败: {e}")
        return False

def test_wechat_handler():
    """测试微信处理器"""
    print("\n🤖 测试微信处理器")
    print("=" * 50)
    
    try:
        from src.bot.wechat_handler import WeChatHandler
        
        print("1. 创建微信处理器")
        handler = WeChatHandler()
        print(f"   ✅ 处理器创建成功: {type(handler)}")
        
        print("\n2. 初始化微信连接")
        success = handler.initialize_wechat()
        
        if success:
            print(f"   ✅ 微信连接初始化成功")
            print(f"   ✅ 微信对象: {type(handler.wx)}")
            
            if hasattr(handler.wx, 'nickname'):
                print(f"   ✅ 当前用户: {handler.wx.nickname}")
            
            return True
        else:
            print(f"   ❌ 微信连接初始化失败")
            print(f"   ❌ 微信对象: {handler.wx}")
            return False
            
    except Exception as e:
        print(f"❌ 微信处理器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def provide_solutions():
    """提供解决方案"""
    print("\n💡 解决方案")
    print("=" * 50)
    
    solutions = [
        {
            "问题": "微信未启动",
            "解决方案": [
                "启动微信PC版",
                "确保微信已登录",
                "不要使用微信网页版"
            ]
        },
        {
            "问题": "微信版本不兼容",
            "解决方案": [
                "更新微信PC版到最新版本",
                "或降级到兼容版本",
                "检查wxauto的兼容性说明"
            ]
        },
        {
            "问题": "权限问题",
            "解决方案": [
                "以管理员身份运行Python脚本",
                "关闭杀毒软件的实时保护",
                "添加Python和微信到白名单"
            ]
        },
        {
            "问题": "wxauto库问题",
            "解决方案": [
                "重新安装wxauto: pip uninstall wxauto && pip install wxauto",
                "尝试不同版本的wxauto",
                "检查Python版本兼容性"
            ]
        }
    ]
    
    for solution in solutions:
        print(f"\n🔧 {solution['问题']}:")
        for step in solution['解决方案']:
            print(f"   • {step}")

def create_wechat_connection_fix():
    """创建微信连接修复代码"""
    print("\n🔧 创建微信连接修复代码")
    print("=" * 50)
    
    fix_code = '''
# 在 wechat_handler.py 的 initialize_wechat 方法中添加更强的错误处理

def initialize_wechat(self) -> bool:
    """初始化微信连接 - 增强版"""
    if not WXAUTO_AVAILABLE:
        logger.error("wxauto库未安装，请运行: pip install wxauto")
        return False

    try:
        logger.info("正在初始化微信连接...")
        
        # 检查微信进程
        import psutil
        wechat_running = False
        for proc in psutil.process_iter(['name']):
            if 'wechat' in proc.info['name'].lower():
                wechat_running = True
                break
        
        if not wechat_running:
            logger.error("微信进程未运行，请启动微信PC版并登录")
            return False
        
        # 创建微信对象
        self.wx = WeChat()
        
        if self.wx is None:
            logger.error("微信对象创建失败")
            return False
        
        # 等待微信初始化
        import time
        time.sleep(2)
        
        # 检查微信状态
        try:
            nickname = getattr(self.wx, 'nickname', None)
            if nickname:
                logger.info(f"微信连接成功，当前用户: {nickname}")
                return True
            else:
                logger.error("微信未登录或获取用户信息失败")
                return False
        except Exception as e:
            logger.error(f"微信状态检查失败: {e}")
            return False
            
    except Exception as e:
        logger.error(f"微信连接初始化异常: {e}")
        self.wx = None
        return False

# 在消息监听循环中添加连接检查
def _listen_messages(self):
    """监听微信消息 - 增强版"""
    logger.info("开始监听微信消息...")
    
    while not self.stop_event.is_set():
        try:
            # 检查微信连接状态
            if self.wx is None:
                logger.warning("微信连接丢失，尝试重新连接...")
                if not self.initialize_wechat():
                    logger.error("微信重连失败，等待5秒后重试")
                    time.sleep(5)
                    continue
            
            # 获取新消息
            msgs = None
            try:
                if hasattr(self.wx, 'GetNewMessage'):
                    msgs = self.wx.GetNewMessage()
                else:
                    logger.error("微信对象缺少 GetNewMessage 方法")
                    time.sleep(1)
                    continue
                    
            except Exception as e:
                logger.debug(f"获取消息失败: {e}")
                # 检查是否是连接问题
                if "NoneType" in str(e):
                    logger.warning("微信连接异常，尝试重新初始化")
                    self.wx = None
                    continue
                
            # 处理消息...
            
        except Exception as e:
            logger.error(f"消息监听异常: {e}")
            time.sleep(1)
'''
    
    print("微信连接修复代码:")
    print(fix_code)

def main():
    """主函数"""
    print("🔧 微信连接问题诊断和修复")
    print()
    
    tests = [
        ("微信连接测试", test_wechat_connection),
        ("微信进程检查", check_wechat_process),
        ("微信处理器测试", test_wechat_handler),
        ("解决方案", provide_solutions),
        ("修复代码", create_wechat_connection_fix)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"🔍 执行: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 失败: {e}")
            results.append((test_name, False))
    
    # 总结
    print(f"\n" + "=" * 60)
    print(f"          微信连接诊断总结")
    print(f"=" * 60)
    
    connection_tests = [name for name, result in results[:3] if result]
    
    if len(connection_tests) >= 2:
        print("✅ 微信连接基本正常")
        print("\n💡 如果仍有问题，可能是:")
        print("   - 微信版本兼容性问题")
        print("   - 权限或安全软件干扰")
        print("   - 微信状态不稳定")
    else:
        print("❌ 微信连接存在问题")
        print("\n🔧 立即行动:")
        print("   1. 确保微信PC版已启动并登录")
        print("   2. 以管理员身份运行Python脚本")
        print("   3. 检查微信版本兼容性")
        print("   4. 重新安装wxauto库")
    
    print(f"\n🚀 下一步:")
    print(f"   1. 按照解决方案检查微信状态")
    print(f"   2. 重新启动机器人程序")
    print(f"   3. 观察微信连接日志")

if __name__ == "__main__":
    main()
