2025-07-17 10:43:56,667 - web_app - INFO - 使用增强回复引擎和数据读取器
2025-07-17 10:43:57,144 - web_app - INFO - Web应用初始化完成
2025-07-17 10:46:08,273 - web_app - INFO - 使用增强回复引擎和数据读取器
2025-07-17 10:46:08,578 - web_app - INFO - Web应用初始化完成
2025-07-17 11:02:44,922 - web_app - INFO - 使用增强回复引擎和数据读取器
2025-07-17 11:02:45,252 - web_app - INFO - Web应用初始化完成
2025-07-17 11:13:38,559 - web_app - INFO - 使用增强回复引擎和数据读取器
2025-07-17 11:13:38,843 - web_app - INFO - Web应用初始化完成
2025-07-17 11:27:28,772 - web_app - INFO - 使用增强回复引擎和数据读取器
2025-07-17 11:27:29,058 - web_app - INFO - Web应用初始化完成
2025-07-17 11:30:34,803 - web_app - INFO - 使用增强回复引擎和数据读取器
2025-07-17 11:30:35,087 - web_app - INFO - Web应用初始化完成
2025-07-17 11:38:38,993 - web_app - INFO - 配置对象类型: <class 'config.Config'>
2025-07-17 11:38:38,994 - web_app - INFO - 系统配置: WeChatSettings(listen_list=[], listen_all=False, auto_reply=True, reply_delay=2, voice_to_text=True, voice_reply_enabled=True)
2025-07-17 11:38:38,994 - web_app - INFO - 系统配置类型: <class 'config.WeChatSettings'>
2025-07-17 11:38:38,994 - web_app - INFO - listen_all属性: True
2025-07-17 11:38:38,995 - web_app - INFO - listen_all值: False
2025-07-17 11:41:25,364 - web_app - INFO - 配置对象类型: <class 'config.Config'>
2025-07-17 11:41:25,364 - web_app - INFO - 系统配置: WeChatSettings(listen_list=[], listen_all=False, auto_reply=True, reply_delay=2, voice_to_text=True, voice_reply_enabled=True)
2025-07-17 11:41:25,365 - web_app - INFO - 系统配置类型: <class 'config.WeChatSettings'>
2025-07-17 11:41:25,365 - web_app - INFO - listen_all属性: True
2025-07-17 11:41:25,366 - web_app - INFO - listen_all值: False
2025-07-17 11:41:31,794 - web_app - INFO - 配置对象类型: <class 'config.Config'>
2025-07-17 11:41:31,794 - web_app - INFO - 系统配置: WeChatSettings(listen_list=[], listen_all=False, auto_reply=True, reply_delay=2, voice_to_text=True, voice_reply_enabled=True)
2025-07-17 11:41:31,795 - web_app - INFO - 系统配置类型: <class 'config.WeChatSettings'>
2025-07-17 11:41:31,795 - web_app - INFO - listen_all属性: True
2025-07-17 11:41:31,795 - web_app - INFO - listen_all值: False
2025-07-17 11:42:14,449 - web_app - INFO - 配置已保存并重新加载
2025-07-17 11:42:31,455 - web_app - INFO - 配置已保存并重新加载
2025-07-17 11:44:05,017 - web_app - INFO - 配置对象类型: <class 'config.Config'>
2025-07-17 11:44:05,018 - web_app - INFO - 系统配置: WeChatSettings(listen_list=[], listen_all=False, auto_reply=True, reply_delay=2, voice_to_text=True, voice_reply_enabled=True)
2025-07-17 11:44:05,018 - web_app - INFO - 系统配置类型: <class 'config.WeChatSettings'>
2025-07-17 11:44:05,019 - web_app - INFO - listen_all属性: True
2025-07-17 11:44:05,019 - web_app - INFO - listen_all值: False
2025-07-17 11:44:25,704 - web_app - INFO - 配置已保存并重新加载
2025-07-17 11:44:28,788 - web_app - INFO - 配置对象类型: <class 'config.Config'>
2025-07-17 11:44:28,789 - web_app - INFO - 系统配置: WeChatSettings(listen_list=[], listen_all=False, auto_reply=True, reply_delay=2, voice_to_text=True, voice_reply_enabled=True)
2025-07-17 11:44:28,789 - web_app - INFO - 系统配置类型: <class 'config.WeChatSettings'>
2025-07-17 11:44:28,790 - web_app - INFO - listen_all属性: True
2025-07-17 11:44:28,790 - web_app - INFO - listen_all值: False
