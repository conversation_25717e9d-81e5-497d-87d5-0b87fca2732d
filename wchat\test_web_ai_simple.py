#!/usr/bin/env python3
"""
简单的Web AI状态测试
"""
import requests

def test_web_ai_status():
    """测试Web AI状态"""
    try:
        session = requests.Session()
        
        # 登录
        login_data = {'password': 'admin123'}
        login_response = session.post('http://localhost:5000/login', data=login_data)
        print(f"登录状态: {login_response.status_code}")
        
        if login_response.status_code == 200:
            # 获取统计信息API
            stats_response = session.get('http://localhost:5000/api/stats')
            print(f"统计API状态: {stats_response.status_code}")
            
            if stats_response.status_code == 200:
                stats = stats_response.json()
                print(f"AI可用状态: {stats.get('ai_available', False)}")
                print(f"增强模式: {stats.get('enhanced_mode', False)}")
                print(f"FAQ数量: {stats.get('faq_count', 0)}")
                print(f"产品数量: {stats.get('product_count', 0)}")
                
                # 测试AI连接
                ai_test_response = session.post('http://localhost:5000/api/test_ai')
                print(f"AI测试状态: {ai_test_response.status_code}")
                
                if ai_test_response.status_code == 200:
                    ai_result = ai_test_response.json()
                    print(f"AI测试结果: {ai_result}")
                else:
                    print(f"AI测试失败: {ai_test_response.text}")
                    
            else:
                print(f"获取统计信息失败: {stats_response.text}")
        else:
            print("登录失败")
            
    except Exception as e:
        print(f"测试失败: {e}")

if __name__ == "__main__":
    test_web_ai_status()
