#!/usr/bin/env python3
"""
修复语音识别 - 使用替代方案
"""
import os
import sys
from pathlib import Path

# 添加项目路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def analyze_current_issue():
    """分析当前问题"""
    print("🔍 分析当前语音识别问题")
    print("=" * 60)
    
    print("从日志分析得出的问题:")
    print("1. ✅ 语音消息被正确识别为语音类型")
    print("2. ✅ 语音配置已正确启用")
    print("3. ❌ 微信API不支持语音转文字 (GetVoiceText/VoiceToText)")
    print("4. ❌ 系统错误地将 '[语音]2秒,未播放' 当作转换结果")
    print("5. ✅ 微信PC端界面支持语音转文字功能")
    
    print(f"\n核心问题:")
    print("微信PC版本的API不提供语音转文字功能，但界面支持")
    
    print(f"\n可能的解决方案:")
    print("1. 🔧 集成第三方语音识别API")
    print("2. 🔧 使用微信界面的语音转文字结果")
    print("3. 🔧 引导用户手动转换语音")
    print("4. 🔧 暂时禁用语音处理，提示用户使用文字")

def create_voice_message_filter():
    """创建语音消息过滤器"""
    print("\n🔧 创建语音消息过滤器")
    print("=" * 60)
    
    filter_code = '''
def _is_voice_placeholder(self, content: str) -> bool:
    """
    检查是否为语音占位符文本
    
    Args:
        content: 消息内容
        
    Returns:
        bool: 是否为语音占位符
    """
    if not isinstance(content, str):
        return False
    
    # 常见的语音占位符模式
    voice_patterns = [
        "[语音]",
        "语音消息",
        "未播放",
        "秒",
        "voice message",
        "audio message"
    ]
    
    content_lower = content.lower()
    
    # 检查是否包含语音相关关键词
    voice_keywords_count = 0
    for pattern in voice_patterns:
        if pattern.lower() in content_lower:
            voice_keywords_count += 1
    
    # 如果包含多个语音关键词，很可能是占位符
    if voice_keywords_count >= 2:
        return True
    
    # 检查特定模式
    if "[语音]" in content and ("秒" in content or "未播放" in content):
        return True
    
    return False

def _convert_voice_to_text_enhanced(self, voice_content: str, sender: str) -> Optional[str]:
    """
    增强的语音转文字方法
    """
    logger.info(f"开始语音转文字 - 内容: {voice_content}, 发送者: {sender}")
    
    try:
        # 首先检查是否为语音占位符
        if self._is_voice_placeholder(voice_content):
            logger.warning(f"检测到语音占位符，无法转换: {voice_content}")
            return None
        
        # 方法1: 微信API (已知不可用，但保留检查)
        if hasattr(self.wx, 'GetVoiceText'):
            try:
                text = self.wx.GetVoiceText(voice_content)
                if text and text.strip() and not self._is_voice_placeholder(text):
                    logger.info(f"微信API转换成功: {text}")
                    return text.strip()
            except Exception as e:
                logger.debug(f"微信API转换失败: {e}")
        
        # 方法2: 检查是否已经是有效文字内容
        if isinstance(voice_content, str) and len(voice_content) > 0:
            # 排除文件路径和占位符
            if (not self._looks_like_file_path(voice_content) and 
                not self._is_voice_placeholder(voice_content) and
                len(voice_content) > 5):  # 至少5个字符
                logger.info(f"内容似乎已是有效文字: {voice_content}")
                return voice_content
        
        # 方法3: 第三方语音识别 (待实现)
        # TODO: 集成百度/腾讯/阿里云语音识别
        
        logger.warning(f"所有语音转文字方法都失败")
        return None
        
    except Exception as e:
        logger.error(f"语音转文字异常: {e}")
        return None
'''
    
    print("增强的语音处理代码:")
    print(filter_code)
    
    return filter_code

def create_third_party_voice_api():
    """创建第三方语音识别API集成方案"""
    print("\n🌐 第三方语音识别API集成方案")
    print("=" * 60)
    
    print("推荐的第三方语音识别服务:")
    
    print(f"\n1. 百度语音识别")
    print(f"   - 免费额度: 每日50,000次")
    print(f"   - 准确率: 95%+")
    print(f"   - 支持格式: wav, pcm, amr, m4a")
    print(f"   - 文档: https://ai.baidu.com/tech/speech")
    
    print(f"\n2. 腾讯云语音识别")
    print(f"   - 免费额度: 每月10小时")
    print(f"   - 准确率: 95%+")
    print(f"   - 支持格式: wav, mp3, m4a, flv, mp4")
    print(f"   - 文档: https://cloud.tencent.com/product/asr")
    
    print(f"\n3. 阿里云语音识别")
    print(f"   - 免费额度: 每月2小时")
    print(f"   - 准确率: 96%+")
    print(f"   - 支持格式: wav, mp3, aac, m4a")
    print(f"   - 文档: https://ai.aliyun.com/nls")
    
    # 百度语音识别示例代码
    baidu_example = '''
# 百度语音识别集成示例
from aip import AipSpeech

class BaiduVoiceRecognition:
    def __init__(self, app_id, api_key, secret_key):
        self.client = AipSpeech(app_id, api_key, secret_key)
    
    def recognize_voice(self, voice_file_path):
        """识别语音文件"""
        try:
            # 读取语音文件
            with open(voice_file_path, 'rb') as fp:
                voice_data = fp.read()
            
            # 调用百度API
            result = self.client.asr(voice_data, 'wav', 16000, {
                'dev_pid': 1537,  # 普通话(支持简单的英文识别)
            })
            
            if result.get('err_no') == 0:
                return result.get('result', [''])[0]
            else:
                return None
                
        except Exception as e:
            logger.error(f"百度语音识别失败: {e}")
            return None

# 在语音转文字方法中集成
def _convert_voice_to_text_with_baidu(self, voice_content: str, sender: str) -> Optional[str]:
    """使用百度API进行语音转文字"""
    
    # 如果有百度API配置
    if hasattr(config, 'baidu_voice') and config.baidu_voice.enabled:
        try:
            baidu_client = BaiduVoiceRecognition(
                config.baidu_voice.app_id,
                config.baidu_voice.api_key,
                config.baidu_voice.secret_key
            )
            
            # 假设voice_content是文件路径
            if os.path.exists(voice_content):
                text = baidu_client.recognize_voice(voice_content)
                if text:
                    logger.info(f"百度语音识别成功: {text}")
                    return text
                    
        except Exception as e:
            logger.error(f"百度语音识别异常: {e}")
    
    return None
'''
    
    print(f"\n百度语音识别集成示例:")
    print(baidu_example)

def create_user_guidance_solution():
    """创建用户引导解决方案"""
    print("\n👥 用户引导解决方案")
    print("=" * 60)
    
    print("由于微信API限制，可以引导用户使用以下方式:")
    
    print(f"\n方案1: 智能提示")
    guidance_code = '''
def _handle_voice_message_guidance(self, sender: str) -> str:
    """处理语音消息的用户引导"""
    
    guidance_messages = [
        "收到您的语音消息了！为了更好地为您服务，建议您：",
        "1. 📝 直接发送文字消息",
        "2. 🎤 在微信中点击语音消息旁的转文字按钮",
        "3. 💬 重新用文字描述您的需求",
        "",
        "这样我就能更准确地理解并帮助您了！😊"
    ]
    
    return "\\n".join(guidance_messages)
'''
    
    print("智能引导代码:")
    print(guidance_code)
    
    print(f"\n方案2: 快捷回复")
    quick_replies = [
        "收到语音消息，请用文字重新描述一下您的需求 😊",
        "语音没听清，麻烦您打字说一下想了解什么产品？",
        "为了更好地帮助您，请用文字告诉我您的需求 📝"
    ]
    
    for i, reply in enumerate(quick_replies, 1):
        print(f"  {i}. {reply}")

def create_immediate_fix():
    """创建立即修复方案"""
    print("\n🔧 立即修复方案")
    print("=" * 60)
    
    print("立即可以实施的修复:")
    
    print(f"\n1. 修复语音占位符识别")
    print(f"   - 检测 '[语音]X秒,未播放' 格式")
    print(f"   - 避免将占位符当作转换结果")
    
    print(f"\n2. 添加用户友好的语音处理")
    print(f"   - 检测到语音消息时给出引导")
    print(f"   - 提示用户使用文字或微信转文字功能")
    
    print(f"\n3. 配置选项")
    print(f"   - 添加语音处理策略配置")
    print(f"   - 允许用户选择处理方式")
    
    fix_code = '''
# 在 _convert_voice_to_text 方法中添加
def _convert_voice_to_text(self, voice_content: str, sender: str) -> Optional[str]:
    logger.info(f"开始语音转文字 - 内容: {voice_content}, 发送者: {sender}")
    
    # 检查是否为语音占位符
    if self._is_voice_placeholder(voice_content):
        logger.warning(f"检测到语音占位符，返回引导消息")
        return "VOICE_GUIDANCE_NEEDED"  # 特殊标记
    
    # ... 其他转换方法 ...
    
    return None

# 在消息处理中
if msg_type in ['voice', 'audio']:
    text_content = self._convert_voice_to_text(content, sender)
    if text_content == "VOICE_GUIDANCE_NEEDED":
        # 发送引导消息
        guidance = self._get_voice_guidance_message()
        self._send_reply(guidance, sender)
        return
    elif text_content:
        content = text_content
        logger.info(f"语音转文字成功: {content}")
    else:
        logger.warning("语音转文字失败，忽略消息")
        return
'''
    
    print("立即修复代码:")
    print(fix_code)

def main():
    """主函数"""
    print("🎤 语音识别替代方案")
    print()
    
    steps = [
        ("问题分析", analyze_current_issue),
        ("语音过滤器", create_voice_message_filter),
        ("第三方API", create_third_party_voice_api),
        ("用户引导", create_user_guidance_solution),
        ("立即修复", create_immediate_fix)
    ]
    
    for step_name, step_func in steps:
        print(f"\n🔍 {step_name}")
        try:
            step_func()
        except Exception as e:
            print(f"❌ {step_name} 失败: {e}")
    
    print(f"\n" + "=" * 60)
    print(f"          语音识别解决方案总结")
    print(f"=" * 60)
    
    print("🎯 问题根源:")
    print("  微信PC版API不支持语音转文字功能")
    
    print("\n💡 解决方案优先级:")
    print("  1. 🔧 立即修复: 添加语音占位符检测和用户引导")
    print("  2. 🌐 短期方案: 集成第三方语音识别API")
    print("  3. 🚀 长期方案: 等待微信API更新或使用其他技术")
    
    print("\n🚀 推荐行动:")
    print("  1. 立即实施语音占位符过滤")
    print("  2. 添加用户友好的引导消息")
    print("  3. 考虑集成百度语音识别API")

if __name__ == "__main__":
    main()
