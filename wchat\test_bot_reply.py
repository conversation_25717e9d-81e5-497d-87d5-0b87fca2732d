#!/usr/bin/env python3
"""
测试机器人回复
"""
from src.bot.enhanced_reply_engine import EnhancedReplyEngine
from config import config

def test_bot_reply():
    """测试机器人回复"""
    print("=" * 60)
    print("          测试机器人回复")
    print("=" * 60)
    
    # 创建回复引擎
    reply_engine = EnhancedReplyEngine()
    
    # 测试消息
    test_messages = [
        "能退货么",
        "退款",
        "什么时候发货",
        "有优惠吗",
        "质量怎么样",
        "怎么付款",
        "联系客服"
    ]
    
    for msg in test_messages:
        print(f"\n用户: {msg}")
        reply = reply_engine.generate_reply(msg)
        if reply:
            print(f"机器人: {reply}")
        else:
            print("机器人: (保持沉默)")

if __name__ == "__main__":
    test_bot_reply()
