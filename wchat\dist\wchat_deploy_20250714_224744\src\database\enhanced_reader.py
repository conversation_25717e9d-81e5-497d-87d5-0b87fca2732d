"""
增强的数据库读取器
支持关键词匹配、产品图片和智能搜索
"""
import os
import pandas as pd
import jieba
from fuzzywuzzy import fuzz
from typing import List, Dict, Optional, Tuple
from src.database.universal_product_matcher import UniversalProductMatcher
from src.database.exact_product_matcher import ExactProductMatcher

class EnhancedFAQReader:
    """增强的FAQ读取器"""
    
    def __init__(self, file_path: str):
        self.file_path = file_path
        self.data = None
        self.load_data()
    
    def load_data(self):
        """加载FAQ数据"""
        try:
            if os.path.exists(self.file_path):
                self.data = pd.read_excel(self.file_path)
                print(f"✅ 加载FAQ数据: {len(self.data)} 条记录")
            else:
                print(f"❌ FAQ文件不存在: {self.file_path}")
                self.data = pd.DataFrame()
        except Exception as e:
            print(f"❌ 加载FAQ数据失败: {e}")
            self.data = pd.DataFrame()
    
    def search_faq(self, question: str, threshold: float = 0.6) -> Optional[Dict]:
        """搜索FAQ"""
        if self.data.empty:
            return None
        
        question = question.strip().lower()
        best_match = None
        best_score = 0
        
        for _, row in self.data.iterrows():
            if row['状态'] != '启用':
                continue
            
            # 关键词匹配
            keywords = str(row['问题关键词']).lower().split(',')
            keyword_score = 0
            max_keyword_score = 0

            for keyword in keywords:
                keyword = keyword.strip()
                if keyword in question:
                    keyword_score += 1
                    max_keyword_score = max(max_keyword_score, 1.0)  # 完全匹配得1分
                else:
                    # 模糊匹配
                    fuzzy_score = fuzz.partial_ratio(keyword, question) / 100
                    if fuzzy_score > 0.8:
                        keyword_score += fuzzy_score
                        max_keyword_score = max(max_keyword_score, fuzzy_score)

            # 标准问题匹配
            standard_question = str(row['标准问题']).lower()
            question_score = fuzz.ratio(question, standard_question) / 100

            # 综合评分：使用最高的单个关键词匹配分数，而不是平均分
            # 这样只要匹配到任何一个关键词就能得到高分
            keyword_final_score = max_keyword_score if max_keyword_score > 0 else (keyword_score / len(keywords) if len(keywords) > 0 else 0)
            total_score = max(keyword_final_score, question_score)
            
            if total_score > best_score and total_score >= threshold:
                best_score = total_score
                best_match = {
                    'question': row['标准问题'],
                    'answer': row['回复内容'],
                    'category': row['分类'],
                    'score': total_score
                }
        
        return best_match

    def get_categories(self) -> List[str]:
        """获取所有FAQ分类"""
        if self.data is None or self.data.empty:
            return []

        categories = self.data['分类'].dropna().unique().tolist()
        return categories


class EnhancedProductReader:
    """增强的产品读取器"""
    
    def __init__(self, file_path: str):
        self.file_path = file_path
        self.data = None
        self.matcher = UniversalProductMatcher()  # 使用通用匹配器
        self.exact_matcher = ExactProductMatcher()  # 使用精确匹配器
        self.load_data()
    
    def load_data(self):
        """加载产品数据"""
        try:
            if os.path.exists(self.file_path):
                self.data = pd.read_excel(self.file_path)
                print(f"✅ 加载产品数据: {len(self.data)} 条记录")
            else:
                print(f"❌ 产品文件不存在: {self.file_path}")
                self.data = pd.DataFrame()
        except Exception as e:
            print(f"❌ 加载产品数据失败: {e}")
            self.data = pd.DataFrame()
    
    def search_products(self, query: str, threshold: float = 0.7, max_results: int = 3) -> List[Dict]:
        """搜索产品"""
        if self.data.empty:
            return []
        
        query = query.strip().lower()
        results = []
        
        for _, row in self.data.iterrows():
            if row['状态'] != '上架':
                continue
            
            score = self._calculate_product_score(query, row)
            
            if score >= threshold:
                product_info = {
                    '产品名称': row['产品名称'],
                    '产品描述': row['产品描述'],
                    '价格': row['价格'],
                    '分类': row['分类'],
                    '产品图片': row.get('产品图片', ''),
                    '详细信息': row.get('详细信息', ''),
                    '库存状态': row.get('库存状态', '有货'),
                    'score': score,
                    # 保持兼容性，同时提供英文字段名
                    'name': row['产品名称'],
                    'description': row['产品描述'],
                    'price': row['价格'],
                    'category': row['分类'],
                    'image': row.get('产品图片', ''),
                    'details': row.get('详细信息', ''),
                    'stock': row.get('库存状态', '有货')
                }
                results.append(product_info)
        
        # 按评分排序
        results.sort(key=lambda x: x['score'], reverse=True)
        return results[:max_results]

    def search_products_exact(self, query: str, max_results: int = 3) -> List[Dict]:
        """
        精确搜索产品（只匹配产品名称）

        Args:
            query: 搜索查询
            max_results: 最大返回结果数

        Returns:
            List[Dict]: 匹配的产品列表
        """
        if self.data is None or self.data.empty:
            return []

        # 只考虑上架的产品
        active_products = self.data[self.data['状态'] == '上架']

        results = []
        for _, row in active_products.iterrows():
            # 使用精确匹配器
            product_data = {
                '产品名称': row.get('产品名称', ''),
                '产品描述': row.get('产品描述', ''),
                '分类': row.get('分类', ''),
                '产品关键词': row.get('产品关键词', ''),
            }

            score = self.exact_matcher.calculate_product_score(query, product_data)

            if score > 0.7:  # 精确匹配的阈值
                product_dict = row.to_dict()
                product_dict['score'] = score
                results.append(product_dict)

        # 按分数排序
        results.sort(key=lambda x: x['score'], reverse=True)
        return results[:max_results]
    
    def _calculate_product_score(self, query: str, row) -> float:
        """计算产品匹配分数（使用通用匹配器）"""
        # 将pandas行转换为字典
        product_data = {
            '产品名称': row.get('产品名称', ''),
            '产品描述': row.get('产品描述', ''),
            '分类': row.get('分类', ''),
            '产品关键词': row.get('产品关键词', ''),
            '价格': row.get('价格', ''),
            '详细信息': row.get('详细信息', ''),
            '库存状态': row.get('库存状态', '有货')
        }

        # 使用通用匹配器计算分数
        return self.matcher.calculate_product_score(query, product_data)
    
    def get_product_by_name(self, name: str) -> Optional[Dict]:
        """根据产品名称获取产品信息"""
        if self.data.empty:
            return None
        
        for _, row in self.data.iterrows():
            if str(row['产品名称']).lower() == name.lower():
                return {
                    'name': row['产品名称'],
                    'description': row['产品描述'],
                    'price': row['价格'],
                    'category': row['分类'],
                    'image': row.get('产品图片', ''),
                    'details': row.get('详细信息', ''),
                    'stock': row.get('库存状态', '有货')
                }
        return None
    
    def get_categories(self) -> List[str]:
        """获取所有产品分类"""
        if self.data.empty:
            return []
        
        categories = self.data['分类'].dropna().unique().tolist()
        return categories
    
    def get_products_by_category(self, category: str) -> List[Dict]:
        """根据分类获取产品"""
        if self.data.empty:
            return []
        
        products = []
        for _, row in self.data.iterrows():
            if str(row['分类']).lower() == category.lower() and row['状态'] == '上架':
                products.append({
                    'name': row['产品名称'],
                    'description': row['产品描述'],
                    'price': row['价格'],
                    'image': row.get('产品图片', ''),
                    'stock': row.get('库存状态', '有货')
                })
        
        return products


def format_product_reply(products: List[Dict]) -> str:
    """格式化产品回复"""
    if not products:
        return "抱歉，没有找到相关产品。您可以描述更具体的需求，比如想要什么类型的产品、价格范围等，我会为您推荐合适的产品。"

    # 根据产品数量调整开场白
    if len(products) == 1:
        reply = f"为您推荐这款产品：\n\n"
    else:
        reply = f"为您推荐 {len(products)} 款相关产品：\n\n"

    for i, product in enumerate(products, 1):
        reply += f"🛍️ {i}. {product['name']}\n"
        reply += f"💰 价格：¥{product['price']}\n"
        reply += f"📝 描述：{product['description']}\n"

        if product.get('stock'):
            reply += f"📦 库存：{product['stock']}\n"

        if product.get('details'):
            reply += f"ℹ️ 详情：{product['details']}\n"

        reply += "\n"

    # 根据产品数量调整结尾，使用更有感情但不急于推销的表达
    if len(products) == 1:
        reply += "这款怎么样？"
    else:
        reply += "看看你喜欢哪款～"

    return reply


def test_enhanced_readers():
    """测试增强的读取器"""
    print("测试增强的数据读取器")
    print("=" * 50)
    
    # 测试FAQ
    faq_file = os.path.join(os.path.dirname(__file__), '..', '..', 'data', 'faq_enhanced.xlsx')
    faq_reader = EnhancedFAQReader(faq_file)
    
    test_questions = ["怎么退货", "什么时候发货", "有优惠吗"]
    for question in test_questions:
        result = faq_reader.search_faq(question)
        print(f"\n问题: {question}")
        if result:
            print(f"答案: {result['answer']}")
            print(f"分数: {result['score']:.2f}")
        else:
            print("未找到匹配答案")
    
    # 测试产品
    products_file = os.path.join(os.path.dirname(__file__), '..', '..', 'data', 'products_enhanced.xlsx')
    product_reader = EnhancedProductReader(products_file)
    
    test_queries = ["手机", "耳机", "充电器"]
    for query in test_queries:
        results = product_reader.search_products(query)
        print(f"\n搜索: {query}")
        if results:
            for product in results:
                print(f"- {product['name']} (¥{product['price']}) 分数: {product['score']:.2f}")
        else:
            print("未找到相关产品")


if __name__ == "__main__":
    test_enhanced_readers()
