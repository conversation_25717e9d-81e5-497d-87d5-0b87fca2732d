#!/usr/bin/env python3
"""
仅启动Web管理界面
"""
import os
import sys
import subprocess
import webbrowser
import time
import threading
from pathlib import Path

def main():
    """主函数"""
    print("=" * 60)
    print("          启动Web管理界面")
    print("=" * 60)
    
    # 设置当前目录
    current_dir = Path(__file__).parent
    
    # 添加到Python路径
    if str(current_dir) not in sys.path:
        sys.path.insert(0, str(current_dir))
    
    print("正在启动Web服务器...")
    
    try:
        # 切换到Web目录
        web_dir = current_dir / "src" / "web"
        if not web_dir.exists():
            print("错误: 未找到Web应用目录")
            input("按回车键退出...")
            return
        
        original_dir = os.getcwd()
        os.chdir(web_dir)
        
        # 设置环境变量
        env = os.environ.copy()
        env['PYTHONPATH'] = str(current_dir)
        
        # 启动Flask应用
        process = subprocess.Popen(
            [sys.executable, "app.py"],
            cwd=web_dir,
            env=env
        )
        
        print("Web服务器启动中...")
        time.sleep(3)
        
        # 在后台线程中打开浏览器
        def open_browser():
            time.sleep(2)
            try:
                webbrowser.open("http://localhost:5000")
                print("✅ 浏览器已打开")
            except:
                print("请手动访问: http://localhost:5000")
        
        browser_thread = threading.Thread(target=open_browser)
        browser_thread.daemon = True
        browser_thread.start()
        
        print("\n" + "=" * 60)
        print("Web管理界面启动成功!")
        print("访问地址: http://localhost:5000")
        print("默认账号: admin")
        print("默认密码: admin123")
        print("=" * 60)
        print("按 Ctrl+C 停止服务器")
        print("=" * 60)
        
        # 等待进程
        try:
            process.wait()
        except KeyboardInterrupt:
            print("\n正在停止服务器...")
            process.terminate()
            try:
                process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                process.kill()
            print("服务器已停止")
        
    except Exception as e:
        print(f"启动失败: {e}")
        input("按回车键退出...")
    finally:
        print("感谢使用 WChat 智能客服系统!")

if __name__ == "__main__":
    main()
