# 远程许可证管理使用指南

## 🌐 概述

私域自动化系统现在支持通过Web界面远程管理许可证，您可以通过浏览器方便地查看硬件信息、检查许可证状态、安装和移除许可证。

## 🚀 快速开始

### 1. 启动Web配置界面

有以下几种方式启动Web配置界面：

#### 方式一：使用批处理文件
```bash
# 双击运行
wchat\快速启动.bat
# 然后选择 [2] 启动Web配置界面
```

#### 方式二：直接启动Web服务
```bash
cd wchat
python web_config.py
```

#### 方式三：完整系统启动
```bash
# 双击运行
启动.bat
# 系统会同时启动Web界面和机器人
```

### 2. 访问许可证管理

1. 打开浏览器访问：`http://127.0.0.1:5000`
2. 使用默认密码登录：`admin123`
3. 在左侧导航菜单中点击"许可证管理"

## 📋 功能说明

### 硬件信息查看

- **硬件ID**：系统自动生成的唯一硬件标识符
- **系统平台**：操作系统类型（Windows/Linux/Mac）
- **机器类型**：处理器架构信息
- **计算机名**：当前计算机的网络名称

💡 **提示**：点击硬件ID右侧的复制按钮可以快速复制到剪贴板

### 许可证状态检查

系统会显示以下信息：
- **许可证状态**：有效/无效
- **到期日期**：许可证的有效期限
- **剩余天数**：距离到期还有多少天
- **授权功能**：当前许可证包含的功能列表
- **错误信息**：如果许可证无效，会显示具体原因

### 许可证安装

1. 联系管理员获取适用于当前硬件的许可证密钥
2. 在"许可证操作"区域的输入框中粘贴许可证密钥
3. 点击"安装"按钮
4. 系统会自动验证并安装许可证

### 许可证移除

⚠️ **注意**：此操作会永久删除当前许可证文件，请谨慎操作

1. 在"许可证操作"区域点击"移除许可证"按钮
2. 确认删除操作
3. 系统会删除许可证文件

## 🔒 安全特性

### 身份验证
- 所有许可证管理操作都需要先登录Web界面
- 使用session管理用户登录状态
- 未登录用户会被自动重定向到登录页面

### 操作日志
- 所有许可证操作都会记录到系统日志
- 包括安装、移除、查看等操作的时间和结果

### 数据保护
- 硬件ID使用加密算法生成，确保唯一性和安全性
- 许可证文件使用数字签名验证，防止篡改

## 🌍 远程访问

### 局域网访问

如果需要从其他设备访问许可证管理界面：

1. 修改配置文件 `wchat/config/config.json`：
```json
{
  "web": {
    "host": "0.0.0.0",
    "port": 5000
  }
}
```

2. 重启Web服务

3. 从其他设备访问：`http://[服务器IP]:5000`

### 安全建议

- 建议修改默认登录密码
- 在生产环境中使用HTTPS
- 限制访问IP范围
- 定期检查访问日志

## 🔧 故障排除

### 常见问题

#### 1. 无法访问Web界面
```
解决方案：
- 检查Web服务是否正常启动
- 确认端口5000没有被其他程序占用
- 检查防火墙设置
```

#### 2. 许可证安装失败
```
解决方案：
- 确认许可证密钥格式正确
- 检查许可证是否适用于当前硬件
- 确认许可证未过期
- 联系管理员重新生成许可证
```

#### 3. 硬件信息获取失败
```
解决方案：
- 以管理员权限运行程序
- 检查系统权限设置
- 确认硬件信息读取组件正常
```

#### 4. 登录失败
```
解决方案：
- 确认密码正确（默认：admin123）
- 清除浏览器缓存和Cookie
- 检查配置文件中的密码设置
```

## 📞 技术支持

如果遇到其他问题：

1. 查看系统日志文件获取详细错误信息
2. 确保系统环境满足运行要求
3. 联系技术支持并提供：
   - 硬件ID
   - 错误信息截图
   - 系统日志文件

---

**私域自动化系统 - 远程许可证管理**  
**安全便捷 · 功能完善 · 易于使用** 🔐🌐✨
