{"name": "WChat微信客服机器人", "version": "1.0.0", "description": "智能微信客服机器人，支持AI聊天、产品推荐、语音消息等功能", "author": "WChat Team", "created": "2025-07-14T22:47:44.756566", "requirements": {"python": ">=3.8", "os": ["Windows", "Linux", "macOS"], "dependencies": "见requirements.txt"}, "features": ["AI智能聊天", "产品推荐与图片发送", "FAQ自动回复", "语音消息转文字", "Web配置界面", "实时监控面板"], "quick_start": ["1. 运行 install_deps.py 安装依赖", "2. 配置 config/config.json 中的API密钥", "3. 运行 quick_start.py 启动机器人", "4. 访问 http://localhost:5000 进行配置"]}