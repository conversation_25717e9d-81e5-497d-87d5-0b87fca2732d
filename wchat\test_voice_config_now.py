#!/usr/bin/env python3
"""
测试当前语音配置状态
"""
import os
import sys
from pathlib import Path

# 添加项目路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def test_current_voice_config():
    """测试当前语音配置"""
    print("🔧 测试当前语音配置")
    print("=" * 60)
    
    try:
        from config import config
        
        print("语音相关配置:")
        print(f"  voice_to_text: {config.wechat.voice_to_text}")
        print(f"  voice_reply_enabled: {config.wechat.voice_reply_enabled}")
        print(f"  auto_reply: {config.wechat.auto_reply}")
        print(f"  listen_all: {config.wechat.listen_all}")
        
        # 检查配置状态
        if config.wechat.voice_to_text:
            print("✅ 语音转文字功能已启用")
        else:
            print("❌ 语音转文字功能已禁用")
            print("💡 需要在配置中启用 voice_to_text")
        
        if config.wechat.voice_reply_enabled:
            print("✅ 语音回复功能已启用")
        else:
            print("❌ 语音回复功能已禁用")
        
        if config.wechat.auto_reply:
            print("✅ 自动回复功能已启用")
        else:
            print("❌ 自动回复功能已禁用")
        
        return config.wechat.voice_to_text
        
    except Exception as e:
        print(f"❌ 配置检查失败: {e}")
        return False

def simulate_voice_message_processing():
    """模拟语音消息处理流程"""
    print("\n🎤 模拟语音消息处理流程")
    print("=" * 60)
    
    # 模拟接收到的语音消息
    test_voice_messages = [
        {
            "sender": "用户A",
            "type": "voice",
            "content": "voice_001.amr",
            "description": "标准语音消息"
        },
        {
            "sender": "用户B", 
            "type": "audio",
            "content": "audio_002.wav",
            "description": "音频消息"
        },
        {
            "sender": "用户C",
            "type": "ptt",
            "content": "ptt_003.silk",
            "description": "PTT语音消息"
        }
    ]
    
    print("模拟语音消息处理:")
    
    for i, msg in enumerate(test_voice_messages, 1):
        print(f"\n消息 {i}: {msg['description']}")
        print(f"  发送者: {msg['sender']}")
        print(f"  类型: {msg['type']}")
        print(f"  内容: {msg['content']}")
        
        # 模拟处理流程
        print(f"  处理流程:")
        print(f"    1. 检测消息类型: {msg['type']} → 识别为语音消息")
        print(f"    2. 检查语音配置: voice_to_text=True")
        print(f"    3. 调用语音转文字: _convert_voice_to_text()")
        print(f"    4. 尝试微信API: GetVoiceText() / VoiceToText()")
        print(f"    5. 转换结果: 需要实际测试")
        print(f"    6. 如果成功: 按文字消息处理")
        print(f"    7. 如果失败: 忽略消息")

def check_wechat_api_availability():
    """检查微信API可用性"""
    print("\n🔌 检查微信API可用性")
    print("=" * 60)
    
    print("微信语音转文字API检查:")
    print("  需要的API方法:")
    print("    - GetVoiceText(voice_content)")
    print("    - VoiceToText(voice_content)")
    
    print(f"\n  API可用性检查:")
    print(f"    ❓ 需要实际运行时检测")
    print(f"    ❓ 依赖微信PC版本")
    print(f"    ❓ 可能需要特定权限")
    
    print(f"\n  常见问题:")
    print(f"    1. 微信版本过旧，不支持语音转文字")
    print(f"    2. 语音文件格式不支持")
    print(f"    3. 语音质量太差，无法识别")
    print(f"    4. 网络问题，无法连接语音识别服务")

def provide_debugging_steps():
    """提供调试步骤"""
    print("\n🔍 调试步骤建议")
    print("=" * 60)
    
    print("立即调试步骤:")
    print("1. 重启机器人程序")
    print("2. 发送一条语音消息")
    print("3. 查看详细日志输出")
    print("4. 检查以下关键信息:")
    
    print(f"\n关键日志信息:")
    print(f"  ✓ '消息详细信息 - 类型: voice'")
    print(f"  ✓ '检测到语音消息 - 类型: voice'")
    print(f"  ✓ '语音配置状态: voice_to_text=True'")
    print(f"  ✓ '微信API可用性: GetVoiceText=True/False'")
    print(f"  ✓ '开始语音转文字 - 内容: ...'")
    print(f"  ✓ '尝试方法1: GetVoiceText'")
    print(f"  ✓ '微信语音转文字成功/失败'")
    
    print(f"\n根据日志结果:")
    print(f"  如果看到 '微信对象不支持 GetVoiceText 方法'")
    print(f"    → 微信版本不支持语音转文字API")
    print(f"  如果看到 'GetVoiceText 返回空结果'")
    print(f"    → 语音识别失败或内容为空")
    print(f"  如果看到 '语音转文字功能已禁用'")
    print(f"    → 检查配置文件中的 voice_to_text 设置")

def create_test_command():
    """创建测试命令"""
    print("\n🚀 测试命令")
    print("=" * 60)
    
    print("重启机器人并测试:")
    print("```bash")
    print("# 1. 重启机器人")
    print("python quick_start.py")
    print("")
    print("# 2. 发送语音消息到机器人")
    print("# 3. 观察控制台日志输出")
    print("```")
    
    print(f"\n期望看到的日志:")
    print(f"```")
    print(f"INFO - 消息详细信息 - 类型: voice, 内容: voice_xxx.amr")
    print(f"INFO - 检测到语音消息 - 类型: voice")
    print(f"INFO - 语音配置状态: voice_to_text=True")
    print(f"INFO - 微信API可用性: GetVoiceText=True, VoiceToText=True")
    print(f"INFO - 开始语音转文字 - 内容: voice_xxx.amr")
    print(f"DEBUG - 尝试方法1: GetVoiceText")
    print(f"INFO - 微信语音转文字成功: 你好")
    print(f"```")

def main():
    """主函数"""
    print("🎤 语音配置测试工具")
    print()
    
    # 执行测试步骤
    steps = [
        ("当前配置检查", test_current_voice_config),
        ("处理流程模拟", simulate_voice_message_processing),
        ("API可用性检查", check_wechat_api_availability),
        ("调试步骤建议", provide_debugging_steps),
        ("测试命令", create_test_command)
    ]
    
    for step_name, step_func in steps:
        print(f"\n🔍 执行: {step_name}")
        try:
            step_func()
        except Exception as e:
            print(f"❌ {step_name} 失败: {e}")
    
    print(f"\n" + "=" * 60)
    print(f"          语音功能测试总结")
    print(f"=" * 60)
    
    print("🎯 当前状态:")
    print("  ✅ 已添加详细的调试日志")
    print("  ✅ 语音配置检查逻辑已完善")
    print("  ✅ 语音转文字方法已增强")
    
    print("\n🔍 下一步:")
    print("  1. 重启机器人程序")
    print("  2. 发送语音消息测试")
    print("  3. 查看详细日志输出")
    print("  4. 根据日志结果进一步调试")
    
    print("\n💡 如果语音转文字仍然失败:")
    print("  - 检查微信PC版本是否支持语音转文字")
    print("  - 尝试不同格式的语音消息")
    print("  - 考虑集成第三方语音识别API")

if __name__ == "__main__":
    main()
