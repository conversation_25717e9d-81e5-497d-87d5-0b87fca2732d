#!/usr/bin/env python3
"""
测试模型选择功能
"""
import requests
import json

def test_model_selection():
    """测试模型选择功能"""
    print("🤖 测试AI模型选择功能")
    print("=" * 60)
    
    try:
        session = requests.Session()
        
        # 登录
        login_data = {'password': 'admin123'}
        login_response = session.post('http://localhost:5000/login', data=login_data)
        print(f"登录状态: {login_response.status_code}")
        
        if login_response.status_code != 200:
            print("❌ 登录失败")
            return False
        
        # 测试获取可用模型
        print("\n🔍 测试获取可用模型...")
        models_response = session.get('http://localhost:5000/api/available_models')
        print(f"获取模型API状态: {models_response.status_code}")
        
        if models_response.status_code == 200:
            models_data = models_response.json()
            if models_data.get('success'):
                models = models_data.get('models', [])
                current_model = models_data.get('current_model', '')
                total_count = models_data.get('total_count', 0)
                
                print(f"✅ 成功获取 {total_count} 个可用模型")
                print(f"📋 当前模型: {current_model}")
                
                # 显示前5个模型
                print("🎯 可用模型列表 (前5个):")
                for i, model in enumerate(models[:5]):
                    status = "⭐ 当前" if model['current'] else "  "
                    print(f"   {status} {model['id']}")
                
                if len(models) > 5:
                    print(f"   ... 还有 {len(models) - 5} 个模型")
                
                # 测试模型切换（如果有其他模型可选）
                if len(models) > 1:
                    # 找一个不同的模型进行测试
                    test_model = None
                    for model in models:
                        if not model['current']:
                            test_model = model['id']
                            break
                    
                    if test_model:
                        print(f"\n🔄 测试切换到模型: {test_model}")
                        switch_data = {'model': test_model}
                        switch_response = session.post(
                            'http://localhost:5000/api/switch_model',
                            json=switch_data
                        )
                        print(f"切换模型API状态: {switch_response.status_code}")
                        
                        if switch_response.status_code == 200:
                            switch_result = switch_response.json()
                            if switch_result.get('success'):
                                print(f"✅ 模型切换成功: {switch_result.get('old_model')} -> {switch_result.get('new_model')}")
                                
                                # 切换回原模型
                                print(f"\n🔄 切换回原模型: {current_model}")
                                restore_data = {'model': current_model}
                                restore_response = session.post(
                                    'http://localhost:5000/api/switch_model',
                                    json=restore_data
                                )
                                
                                if restore_response.status_code == 200:
                                    restore_result = restore_response.json()
                                    if restore_result.get('success'):
                                        print(f"✅ 已恢复原模型: {restore_result.get('new_model')}")
                                    else:
                                        print(f"⚠️ 恢复原模型失败: {restore_result.get('error')}")
                                else:
                                    print(f"❌ 恢复原模型请求失败: {restore_response.status_code}")
                            else:
                                print(f"❌ 模型切换失败: {switch_result.get('error')}")
                        else:
                            print(f"❌ 切换模型请求失败: {switch_response.status_code}")
                    else:
                        print("⚠️ 没有其他模型可供测试切换")
                else:
                    print("⚠️ 只有一个模型，无法测试切换功能")
                
                return True
            else:
                print(f"❌ 获取模型失败: {models_data.get('error')}")
                return False
        else:
            print(f"❌ 获取模型请求失败: {models_response.status_code}")
            if models_response.status_code != 200:
                print(f"   响应内容: {models_response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_dashboard_model_display():
    """测试仪表板模型显示"""
    print("\n🖥️ 测试仪表板模型显示...")
    print("=" * 60)
    
    try:
        session = requests.Session()
        
        # 登录
        login_data = {'password': 'admin123'}
        login_response = session.post('http://localhost:5000/login', data=login_data)
        
        if login_response.status_code == 200:
            # 获取统计信息
            stats_response = session.get('http://localhost:5000/api/stats')
            print(f"统计信息API状态: {stats_response.status_code}")
            
            if stats_response.status_code == 200:
                stats = stats_response.json()
                current_model = stats.get('current_model', '')
                ai_available = stats.get('ai_available', False)
                
                print(f"✅ 统计信息获取成功")
                print(f"📋 当前模型: {current_model}")
                print(f"🤖 AI状态: {'可用' if ai_available else '不可用'}")
                
                if 'ai_config' in stats:
                    ai_config = stats['ai_config']
                    print(f"⚙️ AI配置:")
                    print(f"   模型: {ai_config.get('model', 'N/A')}")
                    print(f"   API地址: {ai_config.get('base_url', 'N/A')}")
                    print(f"   启用状态: {ai_config.get('enabled', False)}")
                
                return True
            else:
                print(f"❌ 获取统计信息失败: {stats_response.status_code}")
                return False
        else:
            print("❌ 登录失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🎯 AI模型选择功能测试")
    print("=" * 60)
    
    # 测试模型选择功能
    model_test_ok = test_model_selection()
    
    # 测试仪表板显示
    dashboard_test_ok = test_dashboard_model_display()
    
    print("\n" + "=" * 60)
    print("📊 测试结果总结:")
    print(f"   模型选择功能: {'✅ 正常' if model_test_ok else '❌ 异常'}")
    print(f"   仪表板显示: {'✅ 正常' if dashboard_test_ok else '❌ 异常'}")
    
    if model_test_ok and dashboard_test_ok:
        print("\n🎉 所有测试通过！模型选择功能正常工作。")
        print("\n💡 使用说明:")
        print("   1. 访问配置页面: http://localhost:5000/config")
        print("   2. 在AI配置部分找到'AI模型选择'")
        print("   3. 点击刷新按钮加载可用模型")
        print("   4. 从下拉列表中选择不同的模型")
        print("   5. 在仪表板查看当前模型信息")
    else:
        print("\n❌ 部分测试失败，请检查相关功能。")

if __name__ == "__main__":
    main()
