@echo off
chcp 65001 >nul
echo ========================================
echo   私域自动化系统 - 一键启动
echo ========================================

echo.
echo 正在启动系统...

REM 检查Python环境
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: Python未安装或未添加到PATH环境变量
    echo 请先安装Python 3.8+
    pause
    exit /b 1
)

echo Python环境检查通过

REM 显示网络信息
echo.
echo 网络信息:
for /f "tokens=2 delims=:" %%a in ('ipconfig ^| findstr /i "IPv4"') do (
    set "ip=%%a"
    goto :found
)
:found
echo   本机IP:%ip%
echo   访问地址: http://127.0.0.1:5000
echo   局域网访问: http://%ip::= %:5000

echo.
echo 登录信息:
echo   普通用户密码: admin123
echo   管理员密码: admin888 (可生成许可证)

echo.
echo 正在启动Web服务器...
echo 启动后请在浏览器中访问: http://127.0.0.1:5000
echo.

REM 启动Web服务器
python web_config.py

echo.
echo 服务已停止
pause
