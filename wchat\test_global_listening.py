#!/usr/bin/env python3
"""
测试全局监听功能
"""
import sys
import time
from pathlib import Path

# 添加项目路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def test_global_listening():
    """测试全局监听功能"""
    print("=" * 60)
    print("          测试全局监听功能")
    print("=" * 60)
    
    try:
        from src.bot.wechat_handler import WeChatHandler
        from config import config
        
        # 显示当前配置
        print(f"监听列表: {config.wechat.listen_list}")
        print(f"全局监听: {getattr(config.wechat, 'listen_all', False)}")
        print(f"自动回复: {config.wechat.auto_reply}")
        
        # 创建处理器
        handler = WeChatHandler()
        print("\n✅ 微信处理器创建成功")
        
        # 初始化微信
        if handler.initialize_wechat():
            print("✅ 微信初始化成功")
        else:
            print("❌ 微信初始化失败")
            return False
        
        # 获取状态
        status = handler.get_status()
        print(f"\n处理器状态:")
        for key, value in status.items():
            print(f"  {key}: {value}")
        
        # 开始监听
        print("\n开始监听消息（60秒）...")
        print("请在微信中发送一条消息到任何聊天（包括文件传输助手）")
        print("注意观察日志输出...")
        
        handler.start_listening()
        
        # 等待60秒，每10秒显示一次状态
        for i in range(6):
            time.sleep(10)
            remaining = 60 - (i + 1) * 10
            print(f"监听中... 剩余 {remaining} 秒")
            
            # 显示处理器状态
            if i == 2:  # 30秒时显示一次状态
                status = handler.get_status()
                print(f"当前状态: 运行中={status['running']}, 微信连接={status['wechat_connected']}")
        
        # 停止监听
        handler.stop_listening()
        print("\n✅ 监听测试完成")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_message_processing_logic():
    """测试消息处理逻辑"""
    print("\n" + "=" * 60)
    print("          测试消息处理逻辑")
    print("=" * 60)
    
    try:
        from src.bot.wechat_handler import WeChatHandler
        
        handler = WeChatHandler()
        
        # 模拟不同类型的消息
        class MockMessage:
            def __init__(self, sender, content):
                self.sender = sender
                self.content = content
        
        test_messages = [
            MockMessage("self", "我自己发的消息"),
            MockMessage("Rocky", "我自己发的消息"),  # 当前用户
            MockMessage("朋友A", "你好，有什么产品推荐吗？"),
            MockMessage("客户B", "请问如何退货？"),
            MockMessage("", "系统消息"),
            MockMessage("system", "系统通知"),
        ]
        
        print("测试消息处理逻辑:")
        for msg in test_messages:
            should_process = handler._should_process_message(msg)
            print(f"  发送者: {msg.sender:10} | 内容: {msg.content:20} | 处理: {'✅' if should_process else '❌'}")
        
        return True
        
    except Exception as e:
        print(f"❌ 消息处理逻辑测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def send_test_message():
    """发送测试消息"""
    print("\n" + "=" * 60)
    print("          发送测试消息")
    print("=" * 60)
    
    try:
        from wxauto import WeChat
        
        wx = WeChat()
        print(f"✅ 微信连接成功，用户: {wx.nickname}")
        
        # 发送测试消息到文件传输助手
        if wx.ChatWith("文件传输助手"):
            test_msg = f"全局监听测试消息 - {time.strftime('%H:%M:%S')}"
            wx.SendMsg(test_msg)
            print(f"✅ 发送测试消息: {test_msg}")
            print("请观察机器人是否能监听到这条消息")
        else:
            print("❌ 无法切换到文件传输助手")
        
        return True
        
    except Exception as e:
        print(f"❌ 发送测试消息失败: {e}")
        return False

def main():
    """主函数"""
    print("开始测试全局监听功能...")
    
    # 测试消息处理逻辑
    if test_message_processing_logic():
        print("\n✅ 消息处理逻辑测试通过")
    else:
        print("\n❌ 消息处理逻辑测试失败")
        return
    
    # 发送测试消息
    if send_test_message():
        print("\n✅ 测试消息发送成功")
    else:
        print("\n❌ 测试消息发送失败")
    
    # 测试全局监听
    if test_global_listening():
        print("\n✅ 全局监听测试通过")
    else:
        print("\n❌ 全局监听测试失败")
    
    print("\n" + "=" * 60)
    print("测试完成！")
    print("=" * 60)
    
    print("\n说明:")
    print("1. 当监听列表为空时，系统会自动启用全局监听")
    print("2. 全局监听会处理所有消息（除了自己发送的）")
    print("3. 观察日志中是否有 '处理消息:' 的输出")
    print("4. 如果看到消息被处理，说明全局监听正常工作")

if __name__ == "__main__":
    main()
