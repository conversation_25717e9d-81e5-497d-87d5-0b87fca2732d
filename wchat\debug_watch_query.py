#!/usr/bin/env python3
"""
调试手表查询问题
"""
import os
import sys
from pathlib import Path

# 添加项目路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def debug_watch_query():
    """调试手表查询问题"""
    print("🔍 调试手表查询问题")
    print("=" * 60)
    
    try:
        from src.database.enhanced_reader import EnhancedProductReader
        from config import config
        
        product_reader = EnhancedProductReader(config.database.products_file)
        
        # 查询"手表"
        query = "手表"
        print(f"查询: '{query}'")
        print("-" * 40)
        
        # 手动分析每个产品的匹配分数
        for _, row in product_reader.data.iterrows():
            if row['状态'] != '上架':
                continue
            
            product_name = row['产品名称']
            print(f"\n产品: {product_name}")
            
            # 计算匹配分数
            score = product_reader._calculate_product_score(query, row)
            print(f"总分数: {score:.3f}")
            
            # 使用通用匹配器详细分析
            product_data = {
                '产品名称': row.get('产品名称', ''),
                '产品描述': row.get('产品描述', ''),
                '分类': row.get('分类', ''),
                '产品关键词': row.get('产品关键词', ''),
            }
            
            matcher = product_reader.matcher
            
            # 分析各个维度的分数
            name_score = matcher._calculate_name_score(query, product_data['产品名称'])
            print(f"  名称匹配: {name_score:.3f}")
            
            keyword_score = matcher._calculate_keyword_score(query, product_data['产品关键词'])
            print(f"  关键词匹配: {keyword_score:.3f}")
            
            desc_score = matcher._calculate_description_score(query, product_data['产品描述'])
            print(f"  描述匹配: {desc_score:.3f}")
            
            category_score = matcher._calculate_category_score(query, product_data['分类'], product_data['产品名称'])
            print(f"  分类匹配: {category_score:.3f}")
            
            exclusion_penalty = matcher._check_exclusion_rules(query, product_data['产品名称'], product_data['分类'])
            print(f"  排除惩罚: {exclusion_penalty:.3f}")
            
            # 计算加权分数
            scores = []
            scores.append(name_score * 2.0)
            if keyword_score > 0:
                scores.append(keyword_score * 1.5)
            scores.append(desc_score * 0.8)
            scores.append(category_score * 1.2)
            
            max_score = max(scores) if scores else 0
            final_score = max_score * exclusion_penalty
            
            print(f"  各项加权分数: {[f'{s:.3f}' for s in scores]}")
            print(f"  最高分数: {max_score:.3f}")
            print(f"  最终分数: {final_score:.3f}")
            
            # 分析为什么智能手机A1会匹配
            if product_name == "智能手机A1":
                print(f"  🔍 详细分析智能手机A1:")
                print(f"    产品名称: '{product_data['产品名称']}'")
                print(f"    产品描述: '{product_data['产品描述']}'")
                print(f"    分类: '{product_data['分类']}'")
                print(f"    关键词: '{product_data['产品关键词']}'")
                
                # 检查是否有"手表"相关的词
                text_to_check = (product_data['产品名称'] + " " + 
                               product_data['产品描述'] + " " + 
                               product_data['分类'] + " " + 
                               str(product_data['产品关键词'])).lower()
                
                if "手表" in text_to_check:
                    print(f"    ❌ 发现问题: 文本中包含'手表'")
                else:
                    print(f"    ❓ 奇怪: 文本中不包含'手表'，为什么会匹配？")
        
        # 使用实际搜索方法
        print(f"\n🔍 实际搜索结果:")
        products = product_reader.search_products(query)
        for i, product in enumerate(products, 1):
            print(f"   {i}. {product['产品名称']} (分数: {product['score']:.3f})")
        
        return True
        
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_query_intent_recognition():
    """测试查询意图识别"""
    print("\n🧠 测试查询意图识别")
    print("=" * 60)
    
    try:
        from src.database.universal_product_matcher import UniversalProductMatcher
        
        matcher = UniversalProductMatcher()
        
        test_queries = ["手表", "智能手表", "watch", "手机", "电脑"]
        
        for query in test_queries:
            intent = matcher._identify_query_intent(query)
            print(f"查询: '{query}' → 意图: {intent}")
        
        return True
        
    except Exception as e:
        print(f"❌ 意图识别测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 手表查询问题调试")
    print()
    
    # 运行调试
    debug_watch_query()
    test_query_intent_recognition()

if __name__ == "__main__":
    main()
