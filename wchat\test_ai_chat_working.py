#!/usr/bin/env python3
"""
测试AI聊天功能是否正常工作
"""
import os
import sys
from pathlib import Path

# 添加项目路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def test_ai_configuration():
    """测试AI配置"""
    print("🔧 测试AI配置")
    print("=" * 60)
    
    try:
        from config import config
        
        print("当前AI配置:")
        print(f"  API密钥: {'已配置' if config.ai.api_key else '未配置'}")
        print(f"  基础URL: {config.ai.base_url}")
        print(f"  模型: {config.ai.model}")
        print(f"  AI启用: {config.ai.enabled}")
        print(f"  AI回退: {config.reply.use_ai_fallback}")
        
        # 验证配置正确性
        checks = []
        
        if config.ai.api_key:
            checks.append("✅ API密钥已配置")
        else:
            checks.append("❌ API密钥未配置")
        
        if "chat/completions" in config.ai.base_url:
            checks.append("✅ API地址正确")
        else:
            checks.append("❌ API地址错误")
        
        if "Qwen" in config.ai.model:
            checks.append("✅ 模型正确")
        else:
            checks.append("❌ 模型错误")
        
        if config.ai.enabled:
            checks.append("✅ AI已启用")
        else:
            checks.append("❌ AI未启用")
        
        if config.reply.use_ai_fallback:
            checks.append("✅ AI回退已启用")
        else:
            checks.append("❌ AI回退未启用")
        
        print(f"\n配置检查:")
        for check in checks:
            print(f"  {check}")
        
        all_good = all("✅" in check for check in checks)
        return all_good
        
    except Exception as e:
        print(f"❌ 配置测试失败: {e}")
        return False

def test_ai_service():
    """测试AI服务"""
    print("\n🤖 测试AI服务")
    print("=" * 60)
    
    try:
        from config import config
        from src.ai.llm_service import LLMService
        
        if not config.ai.api_key:
            print("⚠️ API密钥未配置，跳过实际AI测试")
            return True
        
        # 创建AI服务
        llm_service = LLMService(
            api_key=config.ai.api_key,
            base_url=config.ai.base_url,
            model=config.ai.model,
            max_tokens=config.ai.max_tokens,
            temperature=config.ai.temperature,
            system_prompt=config.ai.system_prompt
        )
        
        print("✅ AI服务创建成功")
        
        # 测试简单对话
        test_messages = ["你好", "你是谁", "谢谢"]
        
        for message in test_messages:
            print(f"\n测试消息: '{message}'")
            try:
                reply = llm_service.generate_reply(message)
                if reply and reply.strip():
                    print(f"AI回复: {reply}")
                    
                    # 检查回复质量
                    if "AI" in reply or "机器人" in reply:
                        print("⚠️ 回复暴露了AI身份")
                    elif "联系客服" in reply:
                        print("⚠️ 回复提及了客服")
                    else:
                        print("✅ 回复质量良好")
                else:
                    print("❌ AI无回复")
                    
            except Exception as e:
                print(f"❌ AI回复失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ AI服务测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_reply_engine_integration():
    """测试回复引擎集成"""
    print("\n🔗 测试回复引擎集成")
    print("=" * 60)
    
    try:
        from src.bot.enhanced_reply_engine_with_images import EnhancedReplyEngineWithImages
        
        reply_engine = EnhancedReplyEngineWithImages()
        
        # 测试基本聊天
        chat_tests = [
            {
                "message": "你好",
                "type": "基本问候",
                "should_have_reply": True
            },
            {
                "message": "您好",
                "type": "礼貌问候", 
                "should_have_reply": True
            },
            {
                "message": "你是谁",
                "type": "身份询问",
                "should_have_reply": True
            },
            {
                "message": "手机推荐",
                "type": "产品查询",
                "should_have_reply": True
            },
            {
                "message": "随便聊聊",
                "type": "闲聊",
                "should_have_reply": True
            }
        ]
        
        print("回复引擎测试:")
        print("-" * 40)
        
        all_passed = True
        
        for test in chat_tests:
            print(f"\n测试: '{test['message']}' ({test['type']})")
            
            reply, image_paths = reply_engine.generate_reply_with_images(test['message'])
            
            has_reply = bool(reply and reply.strip())
            should_have = test['should_have_reply']
            
            if has_reply and should_have:
                print(f"✅ 有回复: {reply[:50]}...")
                if image_paths:
                    print(f"📷 图片: {len(image_paths)} 张")
            elif not has_reply and not should_have:
                print("✅ 正确保持沉默")
            elif has_reply and not should_have:
                print(f"⚠️ 意外有回复: {reply[:50]}...")
            else:
                print("❌ 应该有回复但没有")
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        print(f"❌ 回复引擎测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 AI聊天功能测试")
    print()
    
    # 运行测试
    tests = [
        ("AI配置", test_ai_configuration),
        ("AI服务", test_ai_service),
        ("回复引擎集成", test_reply_engine_integration)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n🔍 运行测试: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
            if result:
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 总结
    print(f"\n" + "=" * 60)
    print(f"          AI聊天功能测试总结")
    print(f"=" * 60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{status} {test_name}")
    
    print(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 AI聊天功能完全正常！")
        print("\n💡 现在支持:")
        print("   1. ✅ 基本问候 ('你好', '您好')")
        print("   2. ✅ 身份询问 ('你是谁')")
        print("   3. ✅ 产品查询 (推荐产品+图片)")
        print("   4. ✅ 闲聊对话 (自然拟人化)")
        print("   5. ✅ 避免AI身份暴露")
        print("   6. ✅ 不提及联系客服")
        print("\n🚀 重启微信机器人，'你好'将有友好回复！")
    else:
        print("⚠️ 部分功能存在问题")
        
        from config import config
        if not config.ai.api_key:
            print("\n💡 提示: 需要配置硅基流动API密钥")
            print("   1. 访问: https://siliconflow.cn")
            print("   2. 获取API密钥")
            print("   3. 配置到 config/config.json")

if __name__ == "__main__":
    main()
