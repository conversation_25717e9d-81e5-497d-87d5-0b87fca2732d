"""
简化的机器人运行测试
测试修复后的微信处理器
"""
import os
import sys
import time

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def test_wechat_handler():
    """测试微信处理器"""
    print("=" * 50)
    print("测试微信处理器")
    print("=" * 50)
    
    try:
        from src.bot.wechat_handler import WeChatHandler
        print("✅ 微信处理器导入成功")
        
        # 创建处理器实例
        handler = WeChatHandler()
        print("✅ 微信处理器创建成功")
        
        # 测试微信初始化
        print("\n正在初始化微信连接...")
        if handler.initialize_wechat():
            print("✅ 微信初始化成功")
            
            # 获取状态
            status = handler.get_status()
            print(f"微信连接状态: {status['wechat_connected']}")
            print(f"监听列表: {status['listen_list']}")
            print(f"自动回复: {status['auto_reply']}")
            
            return True
        else:
            print("❌ 微信初始化失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_reply_engine():
    """测试回复引擎"""
    print("\n" + "=" * 50)
    print("测试回复引擎")
    print("=" * 50)
    
    try:
        from src.bot.reply_engine import ReplyEngine
        
        engine = ReplyEngine()
        print("✅ 回复引擎创建成功")
        
        # 测试回复生成
        test_questions = [
            "如何退货",
            "什么时候发货",
            "有什么优惠"
        ]
        
        for question in test_questions:
            reply = engine.generate_reply(question)
            print(f"问题: {question}")
            print(f"回复: {reply[:100]}...")
            print()
        
        return True
        
    except Exception as e:
        print(f"❌ 回复引擎测试失败: {e}")
        return False


def test_integration():
    """集成测试"""
    print("\n" + "=" * 50)
    print("集成测试")
    print("=" * 50)
    
    try:
        from src.bot.wechat_handler import WeChatHandler
        
        handler = WeChatHandler()
        
        if handler.initialize_wechat():
            print("✅ 微信连接成功")
            
            # 模拟消息处理
            test_messages = [
                ["测试用户", "如何退货", "text"],
                ["测试用户", "什么时候发货", "text"],
                ["测试用户", "有什么优惠", "text"]
            ]
            
            print("\n模拟消息处理:")
            for msg in test_messages:
                try:
                    handler._process_message(msg)
                    print(f"✅ 处理消息: {msg[1]}")
                except Exception as e:
                    print(f"❌ 处理消息失败: {e}")
            
            return True
        else:
            print("❌ 微信连接失败")
            return False
            
    except Exception as e:
        print(f"❌ 集成测试失败: {e}")
        return False


def main():
    """主函数"""
    print("微信客服机器人 - 修复验证测试")
    print("=" * 50)
    
    # 测试微信处理器
    if not test_wechat_handler():
        print("\n❌ 微信处理器测试失败，请检查:")
        print("1. 微信PC版是否已登录")
        print("2. wxauto库是否正确安装")
        return
    
    # 测试回复引擎
    if not test_reply_engine():
        print("\n❌ 回复引擎测试失败")
        return
    
    # 集成测试
    if test_integration():
        print("\n" + "=" * 50)
        print("🎉 所有测试通过！")
        print("=" * 50)
        print("修复成功，机器人可以正常运行")
        print("可以使用以下命令启动完整机器人:")
        print("python run.py")
    else:
        print("\n❌ 集成测试失败")


if __name__ == "__main__":
    main()
