#!/usr/bin/env python3
"""
测试微信PC端性能和机器人监听影响
"""
import sys
import time
import psutil
import threading
from pathlib import Path

# 添加项目路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

class WeChatPerformanceMonitor:
    def __init__(self):
        self.monitoring = False
        self.wechat_processes = []
        self.performance_data = []
        
    def find_wechat_processes(self):
        """查找微信进程"""
        processes = []
        for proc in psutil.process_iter(['pid', 'name', 'cpu_percent', 'memory_info']):
            try:
                if 'wechat' in proc.info['name'].lower() or 'weixin' in proc.info['name'].lower():
                    processes.append(proc)
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
        return processes
    
    def get_system_performance(self):
        """获取系统性能数据"""
        return {
            'cpu_percent': psutil.cpu_percent(interval=1),
            'memory_percent': psutil.virtual_memory().percent,
            'timestamp': time.time()
        }
    
    def get_wechat_performance(self):
        """获取微信进程性能数据"""
        wechat_data = []
        processes = self.find_wechat_processes()
        
        for proc in processes:
            try:
                cpu_percent = proc.cpu_percent()
                memory_info = proc.memory_info()
                memory_mb = memory_info.rss / 1024 / 1024  # 转换为MB
                
                wechat_data.append({
                    'pid': proc.pid,
                    'name': proc.info['name'],
                    'cpu_percent': cpu_percent,
                    'memory_mb': memory_mb,
                    'timestamp': time.time()
                })
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
        
        return wechat_data
    
    def start_monitoring(self, duration=60):
        """开始性能监控"""
        print(f"开始监控微信性能，持续 {duration} 秒...")
        self.monitoring = True
        self.performance_data = []
        
        start_time = time.time()
        while self.monitoring and (time.time() - start_time) < duration:
            # 获取系统性能
            system_perf = self.get_system_performance()
            
            # 获取微信性能
            wechat_perf = self.get_wechat_performance()
            
            # 记录数据
            data_point = {
                'system': system_perf,
                'wechat': wechat_perf,
                'timestamp': time.time()
            }
            self.performance_data.append(data_point)
            
            # 实时显示
            if wechat_perf:
                total_cpu = sum(p['cpu_percent'] for p in wechat_perf)
                total_memory = sum(p['memory_mb'] for p in wechat_perf)
                print(f"微信CPU: {total_cpu:.1f}%, 内存: {total_memory:.1f}MB, 系统CPU: {system_perf['cpu_percent']:.1f}%")
            else:
                print("未检测到微信进程")
            
            time.sleep(2)  # 每2秒检查一次
    
    def stop_monitoring(self):
        """停止监控"""
        self.monitoring = False
    
    def analyze_performance(self):
        """分析性能数据"""
        if not self.performance_data:
            print("没有性能数据可分析")
            return
        
        print("\n" + "=" * 60)
        print("          性能分析报告")
        print("=" * 60)
        
        # 分析微信CPU使用率
        wechat_cpu_data = []
        wechat_memory_data = []
        system_cpu_data = []
        
        for data in self.performance_data:
            if data['wechat']:
                total_cpu = sum(p['cpu_percent'] for p in data['wechat'])
                total_memory = sum(p['memory_mb'] for p in data['wechat'])
                wechat_cpu_data.append(total_cpu)
                wechat_memory_data.append(total_memory)
            
            system_cpu_data.append(data['system']['cpu_percent'])
        
        if wechat_cpu_data:
            print(f"微信CPU使用率:")
            print(f"  平均: {sum(wechat_cpu_data)/len(wechat_cpu_data):.1f}%")
            print(f"  最大: {max(wechat_cpu_data):.1f}%")
            print(f"  最小: {min(wechat_cpu_data):.1f}%")
            
            print(f"\n微信内存使用:")
            print(f"  平均: {sum(wechat_memory_data)/len(wechat_memory_data):.1f}MB")
            print(f"  最大: {max(wechat_memory_data):.1f}MB")
            print(f"  最小: {min(wechat_memory_data):.1f}MB")
        
        print(f"\n系统CPU使用率:")
        print(f"  平均: {sum(system_cpu_data)/len(system_cpu_data):.1f}%")
        print(f"  最大: {max(system_cpu_data):.1f}%")
        print(f"  最小: {min(system_cpu_data):.1f}%")
        
        # 性能评估
        if wechat_cpu_data:
            avg_wechat_cpu = sum(wechat_cpu_data)/len(wechat_cpu_data)
            max_wechat_cpu = max(wechat_cpu_data)
            
            print(f"\n性能评估:")
            if avg_wechat_cpu < 5:
                print("✅ 微信CPU使用率正常，性能良好")
            elif avg_wechat_cpu < 15:
                print("⚠️  微信CPU使用率偏高，可能有轻微影响")
            else:
                print("❌ 微信CPU使用率过高，可能导致卡顿")
            
            if max_wechat_cpu > 50:
                print("⚠️  检测到CPU峰值过高，可能有瞬时卡顿")

def test_without_bot():
    """测试没有机器人时的微信性能"""
    print("=" * 60)
    print("          测试基准性能（无机器人）")
    print("=" * 60)
    
    monitor = WeChatPerformanceMonitor()
    
    # 检查微信是否运行
    wechat_processes = monitor.find_wechat_processes()
    if not wechat_processes:
        print("❌ 未检测到微信进程，请先启动微信PC端")
        return None
    
    print(f"检测到 {len(wechat_processes)} 个微信进程:")
    for proc in wechat_processes:
        print(f"  - {proc.info['name']} (PID: {proc.pid})")
    
    # 监控30秒基准性能
    monitor.start_monitoring(30)
    monitor.analyze_performance()
    
    return monitor.performance_data

def test_with_bot():
    """测试有机器人时的微信性能"""
    print("\n" + "=" * 60)
    print("          测试机器人监听性能")
    print("=" * 60)
    
    try:
        # 启动机器人监听
        from src.bot.wechat_handler import WeChatHandler
        
        print("正在启动微信机器人...")
        handler = WeChatHandler()
        
        if not handler.initialize_wechat():
            print("❌ 微信机器人初始化失败")
            return None
        
        print("✅ 微信机器人初始化成功")
        
        # 启动监听（但不启动完整的监听循环，避免干扰）
        print("开始性能监控...")
        
        monitor = WeChatPerformanceMonitor()
        
        # 模拟机器人监听活动
        def simulate_bot_activity():
            """模拟机器人活动"""
            for i in range(15):  # 30秒内进行15次检查
                try:
                    # 模拟获取消息
                    msgs = handler._get_new_messages()
                    if msgs:
                        print(f"检测到 {len(msgs)} 条新消息")
                    time.sleep(2)
                except Exception as e:
                    print(f"模拟活动错误: {e}")
                    time.sleep(2)
        
        # 在后台运行模拟活动
        bot_thread = threading.Thread(target=simulate_bot_activity)
        bot_thread.daemon = True
        bot_thread.start()
        
        # 监控30秒性能
        monitor.start_monitoring(30)
        monitor.analyze_performance()
        
        return monitor.performance_data
        
    except Exception as e:
        print(f"❌ 机器人测试失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def compare_performance(baseline_data, bot_data):
    """比较性能数据"""
    print("\n" + "=" * 60)
    print("          性能对比分析")
    print("=" * 60)
    
    if not baseline_data or not bot_data:
        print("❌ 缺少对比数据")
        return
    
    # 提取CPU数据
    def extract_wechat_cpu(data):
        cpu_values = []
        for point in data:
            if point['wechat']:
                total_cpu = sum(p['cpu_percent'] for p in point['wechat'])
                cpu_values.append(total_cpu)
        return cpu_values
    
    baseline_cpu = extract_wechat_cpu(baseline_data)
    bot_cpu = extract_wechat_cpu(bot_data)
    
    if baseline_cpu and bot_cpu:
        baseline_avg = sum(baseline_cpu) / len(baseline_cpu)
        bot_avg = sum(bot_cpu) / len(bot_cpu)
        
        print(f"微信CPU使用率对比:")
        print(f"  基准性能: {baseline_avg:.1f}%")
        print(f"  机器人运行: {bot_avg:.1f}%")
        print(f"  差异: {bot_avg - baseline_avg:+.1f}%")
        
        if abs(bot_avg - baseline_avg) < 2:
            print("✅ 机器人对微信性能影响很小")
        elif abs(bot_avg - baseline_avg) < 5:
            print("⚠️  机器人对微信性能有轻微影响")
        else:
            print("❌ 机器人对微信性能影响较大")
    
    print(f"\n建议:")
    print(f"• 如果CPU使用率增加 < 5%：正常，可以放心使用")
    print(f"• 如果CPU使用率增加 5-10%：可接受，注意监控")
    print(f"• 如果CPU使用率增加 > 10%：需要优化监听频率")

def main():
    """主函数"""
    print("微信PC端性能测试工具")
    print("此工具将测试机器人监听对微信性能的影响")
    
    # 测试基准性能
    print("\n第一阶段：测试基准性能（请确保微信正常运行）")
    input("按回车键开始基准测试...")
    
    baseline_data = test_without_bot()
    
    if not baseline_data:
        print("❌ 基准测试失败")
        return
    
    # 测试机器人性能
    print("\n第二阶段：测试机器人监听性能")
    input("按回车键开始机器人测试...")
    
    bot_data = test_with_bot()
    
    # 对比分析
    compare_performance(baseline_data, bot_data)
    
    print("\n" + "=" * 60)
    print("性能测试完成！")
    print("=" * 60)
    
    print("\n总结:")
    print("1. 如果性能影响很小，可以放心使用机器人")
    print("2. 如果有明显影响，可以调整监听频率")
    print("3. 建议定期监控微信性能")

if __name__ == "__main__":
    main()
