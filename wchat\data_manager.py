"""
数据管理工具
支持上传、替换、备份数据
"""
import os
import shutil
import pandas as pd
from datetime import datetime
import json
from typing import List, Dict

class DataManager:
    """数据管理器"""
    
    def __init__(self):
        self.data_dir = os.path.join(os.path.dirname(__file__), 'data')
        self.backup_dir = os.path.join(self.data_dir, 'backups')
        self.images_dir = os.path.join(self.data_dir, 'images')
        
        # 确保目录存在
        os.makedirs(self.backup_dir, exist_ok=True)
        os.makedirs(self.images_dir, exist_ok=True)
    
    def backup_data(self, data_type: str) -> str:
        """备份数据"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        if data_type == "faq":
            source_file = os.path.join(self.data_dir, 'faq_enhanced.xlsx')
            backup_file = os.path.join(self.backup_dir, f'faq_backup_{timestamp}.xlsx')
        elif data_type == "products":
            source_file = os.path.join(self.data_dir, 'products_enhanced.xlsx')
            backup_file = os.path.join(self.backup_dir, f'products_backup_{timestamp}.xlsx')
        else:
            raise ValueError("数据类型必须是 'faq' 或 'products'")
        
        if os.path.exists(source_file):
            shutil.copy2(source_file, backup_file)
            print(f"✅ 数据已备份到: {backup_file}")
            return backup_file
        else:
            print(f"❌ 源文件不存在: {source_file}")
            return ""
    
    def upload_faq_data(self, file_path: str, replace: bool = False) -> bool:
        """上传FAQ数据"""
        try:
            # 读取上传的文件
            if file_path.endswith('.xlsx'):
                df = pd.read_excel(file_path)
            elif file_path.endswith('.csv'):
                df = pd.read_csv(file_path, encoding='utf-8')
            else:
                print("❌ 不支持的文件格式，请使用 .xlsx 或 .csv")
                return False
            
            # 验证数据格式
            required_columns = ['问题关键词', '标准问题', '回复内容', '分类', '状态']
            if not all(col in df.columns for col in required_columns):
                print(f"❌ 数据格式不正确，需要包含列: {required_columns}")
                print(f"当前列: {df.columns.tolist()}")
                return False
            
            # 备份现有数据
            if replace:
                self.backup_data("faq")
            
            # 保存新数据
            target_file = os.path.join(self.data_dir, 'faq_enhanced.xlsx')
            
            if replace:
                df.to_excel(target_file, index=False)
                print(f"✅ FAQ数据已替换，共 {len(df)} 条记录")
            else:
                # 合并数据
                if os.path.exists(target_file):
                    existing_df = pd.read_excel(target_file)
                    combined_df = pd.concat([existing_df, df], ignore_index=True)
                    combined_df.to_excel(target_file, index=False)
                    print(f"✅ FAQ数据已合并，共 {len(combined_df)} 条记录")
                else:
                    df.to_excel(target_file, index=False)
                    print(f"✅ FAQ数据已上传，共 {len(df)} 条记录")
            
            return True
            
        except Exception as e:
            print(f"❌ 上传FAQ数据失败: {e}")
            return False
    
    def upload_products_data(self, file_path: str, replace: bool = False) -> bool:
        """上传产品数据"""
        try:
            # 读取上传的文件
            if file_path.endswith('.xlsx'):
                df = pd.read_excel(file_path)
            elif file_path.endswith('.csv'):
                df = pd.read_csv(file_path, encoding='utf-8')
            else:
                print("❌ 不支持的文件格式，请使用 .xlsx 或 .csv")
                return False
            
            # 验证数据格式
            required_columns = ['产品名称', '产品关键词', '产品描述', '价格', '分类']
            if not all(col in df.columns for col in required_columns):
                print(f"❌ 数据格式不正确，需要包含列: {required_columns}")
                print(f"当前列: {df.columns.tolist()}")
                return False
            
            # 补充缺失的列
            optional_columns = ['产品图片', '详细信息', '库存状态', '状态']
            for col in optional_columns:
                if col not in df.columns:
                    if col == '产品图片':
                        df[col] = ''
                    elif col == '详细信息':
                        df[col] = ''
                    elif col == '库存状态':
                        df[col] = '有货'
                    elif col == '状态':
                        df[col] = '上架'
            
            # 备份现有数据
            if replace:
                self.backup_data("products")
            
            # 保存新数据
            target_file = os.path.join(self.data_dir, 'products_enhanced.xlsx')
            
            if replace:
                df.to_excel(target_file, index=False)
                print(f"✅ 产品数据已替换，共 {len(df)} 条记录")
            else:
                # 合并数据
                if os.path.exists(target_file):
                    existing_df = pd.read_excel(target_file)
                    combined_df = pd.concat([existing_df, df], ignore_index=True)
                    combined_df.to_excel(target_file, index=False)
                    print(f"✅ 产品数据已合并，共 {len(combined_df)} 条记录")
                else:
                    df.to_excel(target_file, index=False)
                    print(f"✅ 产品数据已上传，共 {len(df)} 条记录")
            
            return True
            
        except Exception as e:
            print(f"❌ 上传产品数据失败: {e}")
            return False
    
    def upload_images(self, images_folder: str) -> bool:
        """上传产品图片"""
        try:
            if not os.path.exists(images_folder):
                print(f"❌ 图片文件夹不存在: {images_folder}")
                return False
            
            # 支持的图片格式
            supported_formats = ['.jpg', '.jpeg', '.png', '.gif', '.bmp']
            uploaded_count = 0
            
            for filename in os.listdir(images_folder):
                file_ext = os.path.splitext(filename)[1].lower()
                if file_ext in supported_formats:
                    source_path = os.path.join(images_folder, filename)
                    target_path = os.path.join(self.images_dir, filename)
                    
                    shutil.copy2(source_path, target_path)
                    uploaded_count += 1
                    print(f"✅ 已上传图片: {filename}")
            
            print(f"✅ 共上传 {uploaded_count} 张图片到: {self.images_dir}")
            return True
            
        except Exception as e:
            print(f"❌ 上传图片失败: {e}")
            return False
    
    def list_backups(self) -> List[Dict]:
        """列出所有备份"""
        backups = []
        
        if os.path.exists(self.backup_dir):
            for filename in os.listdir(self.backup_dir):
                if filename.endswith('.xlsx'):
                    filepath = os.path.join(self.backup_dir, filename)
                    stat = os.stat(filepath)
                    
                    backup_info = {
                        'filename': filename,
                        'filepath': filepath,
                        'size': stat.st_size,
                        'created': datetime.fromtimestamp(stat.st_ctime),
                        'type': 'FAQ' if 'faq' in filename else 'Products'
                    }
                    backups.append(backup_info)
        
        # 按创建时间排序
        backups.sort(key=lambda x: x['created'], reverse=True)
        return backups
    
    def restore_backup(self, backup_file: str) -> bool:
        """恢复备份"""
        try:
            if not os.path.exists(backup_file):
                print(f"❌ 备份文件不存在: {backup_file}")
                return False
            
            # 确定目标文件
            if 'faq' in os.path.basename(backup_file):
                target_file = os.path.join(self.data_dir, 'faq_enhanced.xlsx')
                data_type = "FAQ"
            elif 'products' in os.path.basename(backup_file):
                target_file = os.path.join(self.data_dir, 'products_enhanced.xlsx')
                data_type = "产品"
            else:
                print("❌ 无法识别备份文件类型")
                return False
            
            # 备份当前数据
            if os.path.exists(target_file):
                current_backup = self.backup_data("faq" if "faq" in target_file else "products")
                print(f"当前数据已备份到: {current_backup}")
            
            # 恢复备份
            shutil.copy2(backup_file, target_file)
            print(f"✅ {data_type}数据已从备份恢复")
            return True
            
        except Exception as e:
            print(f"❌ 恢复备份失败: {e}")
            return False
    
    def export_template(self, data_type: str) -> str:
        """导出数据模板"""
        try:
            if data_type == "faq":
                template_data = {
                    '问题关键词': ['退货,退款,退换', '发货,物流,快递'],
                    '标准问题': ['如何申请退货退款？', '什么时候发货？'],
                    '回复内容': ['您可以在订单页面申请退货...', '我们会在24小时内发货...'],
                    '分类': ['售后服务', '物流配送'],
                    '状态': ['启用', '启用']
                }
                filename = 'faq_template.xlsx'
            
            elif data_type == "products":
                template_data = {
                    '产品名称': ['智能手机A1', '蓝牙耳机B2'],
                    '产品关键词': ['手机,智能手机,通话', '耳机,蓝牙,音乐'],
                    '产品描述': ['6.1寸全面屏，大电池', '降噪耳机，长续航'],
                    '价格': [2999.0, 399.0],
                    '分类': ['数码产品', '音频设备'],
                    '产品图片': ['images/phone_a1.jpg', 'images/earphone_b2.jpg'],
                    '详细信息': ['屏幕：6.1寸\\n电池：5000mAh', '续航：30小时\\n防水：IPX7'],
                    '库存状态': ['有货', '有货'],
                    '状态': ['上架', '上架']
                }
                filename = 'products_template.xlsx'
            
            else:
                print("❌ 数据类型必须是 'faq' 或 'products'")
                return ""
            
            df = pd.DataFrame(template_data)
            template_path = os.path.join(self.data_dir, filename)
            df.to_excel(template_path, index=False)
            
            print(f"✅ 模板已导出到: {template_path}")
            return template_path
            
        except Exception as e:
            print(f"❌ 导出模板失败: {e}")
            return ""


def main():
    """主函数 - 数据管理界面"""
    manager = DataManager()
    
    while True:
        print("\n" + "=" * 50)
        print("数据管理工具")
        print("=" * 50)
        print("1. 📤 上传FAQ数据")
        print("2. 📤 上传产品数据")
        print("3. 🖼️  上传产品图片")
        print("4. 📋 导出数据模板")
        print("5. 💾 查看备份列表")
        print("6. 🔄 恢复备份")
        print("7. 🚪 退出")
        
        choice = input("\n请输入选择 (1-7): ").strip()
        
        if choice == "1":
            file_path = input("请输入FAQ文件路径: ").strip()
            replace = input("是否替换现有数据？(y/n): ").strip().lower() == 'y'
            manager.upload_faq_data(file_path, replace)
        
        elif choice == "2":
            file_path = input("请输入产品文件路径: ").strip()
            replace = input("是否替换现有数据？(y/n): ").strip().lower() == 'y'
            manager.upload_products_data(file_path, replace)
        
        elif choice == "3":
            folder_path = input("请输入图片文件夹路径: ").strip()
            manager.upload_images(folder_path)
        
        elif choice == "4":
            data_type = input("导出模板类型 (faq/products): ").strip().lower()
            manager.export_template(data_type)
        
        elif choice == "5":
            backups = manager.list_backups()
            if backups:
                print("\n备份列表:")
                for i, backup in enumerate(backups, 1):
                    print(f"{i}. {backup['filename']} ({backup['type']}) - {backup['created']}")
            else:
                print("没有找到备份文件")
        
        elif choice == "6":
            backups = manager.list_backups()
            if backups:
                print("\n备份列表:")
                for i, backup in enumerate(backups, 1):
                    print(f"{i}. {backup['filename']} ({backup['type']}) - {backup['created']}")
                
                try:
                    index = int(input("请选择要恢复的备份 (序号): ")) - 1
                    if 0 <= index < len(backups):
                        manager.restore_backup(backups[index]['filepath'])
                    else:
                        print("❌ 无效序号")
                except ValueError:
                    print("❌ 请输入有效数字")
            else:
                print("没有找到备份文件")
        
        elif choice == "7":
            print("👋 退出数据管理工具")
            break
        
        else:
            print("❌ 无效选择")


if __name__ == "__main__":
    main()
