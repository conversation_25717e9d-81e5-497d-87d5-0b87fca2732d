"""
创建示例数据文件
生成FAQ库和产品库的Excel示例文件
"""
import os
try:
    import pandas as pd
    HAS_PANDAS = True
except ImportError:
    HAS_PANDAS = False
    print("警告: 未安装pandas，将创建CSV格式文件")


def create_faq_sample():
    """创建FAQ示例数据"""
    if not HAS_PANDAS:
        # 返回原始数据列表
        return [
            ['问题关键词', '标准问题', '回复内容', '分类', '状态'],
            ['退货,退款,退换', '如何申请退货退款？', '您可以在订单页面点击"申请退货"，或联系客服办理退货退款。退货商品需保持原包装完整，7天内可无理由退货。', '售后服务', '启用'],
            ['发货,物流,快递', '什么时候发货？', '我们会在您付款后24小时内安排发货，节假日可能会有延迟。您可以在订单详情中查看物流信息。', '物流配送', '启用'],
            ['价格,优惠,折扣', '有什么优惠活动吗？', '我们经常有各种优惠活动，建议您关注我们的官方公众号获取最新优惠信息。新用户首单可享受9折优惠。', '促销活动', '启用'],
            ['质量,品质,保修', '产品质量如何保证？', '我们所有产品都经过严格质检，提供1年质保服务。如有质量问题，可免费维修或更换。', '产品质量', '启用'],
            ['支付,付款,微信,支付宝', '支持哪些付款方式？', '我们支持微信支付、支付宝、银行卡等多种付款方式，您可以选择最方便的方式进行支付。', '支付方式', '启用'],
            ['客服,联系,电话', '如何联系客服？', '您可以通过以下方式联系我们：\n1. 微信客服（当前对话）\n2. 客服热线：400-123-4567\n3. 邮箱：<EMAIL>\n工作时间：9:00-18:00', '联系方式', '启用']
        ]

    faq_data = [
        {
            '问题关键词': '退货,退款,退换',
            '标准问题': '如何申请退货退款？',
            '回复内容': '您可以在订单页面点击"申请退货"，或联系客服办理退货退款。退货商品需保持原包装完整，7天内可无理由退货。',
            '分类': '售后服务',
            '状态': '启用'
        },
        {
            '问题关键词': '发货,物流,快递',
            '标准问题': '什么时候发货？',
            '回复内容': '我们会在您付款后24小时内安排发货，节假日可能会有延迟。您可以在订单详情中查看物流信息。',
            '分类': '物流配送',
            '状态': '启用'
        },
        {
            '问题关键词': '价格,优惠,折扣',
            '标准问题': '有什么优惠活动吗？',
            '回复内容': '我们经常有各种优惠活动，建议您关注我们的官方公众号获取最新优惠信息。新用户首单可享受9折优惠。',
            '分类': '促销活动',
            '状态': '启用'
        },
        {
            '问题关键词': '质量,品质,保修',
            '标准问题': '产品质量如何保证？',
            '回复内容': '我们所有产品都经过严格质检，提供1年质保服务。如有质量问题，可免费维修或更换。',
            '分类': '产品质量',
            '状态': '启用'
        },
        {
            '问题关键词': '支付,付款,微信,支付宝',
            '标准问题': '支持哪些付款方式？',
            '回复内容': '我们支持微信支付、支付宝、银行卡等多种付款方式，您可以选择最方便的方式进行支付。',
            '分类': '支付方式',
            '状态': '启用'
        },
        {
            '问题关键词': '客服,联系,电话',
            '标准问题': '如何联系客服？',
            '回复内容': '您可以通过以下方式联系我们：\n1. 微信客服（当前对话）\n2. 客服热线：400-123-4567\n3. 邮箱：<EMAIL>\n工作时间：9:00-18:00',
            '分类': '联系方式',
            '状态': '启用'
        }
    ]

    if HAS_PANDAS:
        df = pd.DataFrame(faq_data)
        return df
    else:
        return faq_data


def create_products_sample():
    """创建产品示例数据"""
    if not HAS_PANDAS:
        # 返回原始数据列表
        return [
            ['产品名称', '产品描述', '价格', '分类', '详细信息', '状态'],
            ['智能手机A1', '6.1寸全面屏，128GB存储，5000mAh大电池，支持快充', '2999.00', '数码电子', '处理器：骁龙888\n内存：8GB\n存储：128GB\n屏幕：6.1寸OLED\n电池：5000mAh\n摄像头：5000万像素三摄', '上架'],
            ['无线蓝牙耳机B2', '降噪蓝牙耳机，30小时续航，IPX7防水', '399.00', '数码配件', '连接方式：蓝牙5.0\n续航时间：30小时\n防水等级：IPX7\n降噪：主动降噪\n充电接口：Type-C', '上架'],
            ['智能手表C3', '健康监测，运动追踪，7天续航，支持通话', '1299.00', '智能穿戴', '屏幕：1.4寸彩屏\n续航：7天\n防水：50米\n功能：心率监测、血氧检测、GPS定位\n系统：自研OS', '上架'],
            ['笔记本电脑D4', '轻薄本，14寸屏幕，16GB内存，512GB固态硬盘', '5999.00', '电脑办公', '处理器：Intel i7-12700H\n内存：16GB DDR4\n硬盘：512GB SSD\n显卡：集成显卡\n屏幕：14寸2K屏\n重量：1.5kg', '上架'],
            ['无线充电器E5', '15W快速无线充电，支持多种设备，带LED指示灯', '99.00', '数码配件', '功率：15W\n兼容性：支持Qi标准设备\n材质：铝合金+玻璃\n指示灯：LED状态显示\n安全保护：过温、过压、异物检测', '上架'],
            ['游戏鼠标F6', '电竞游戏鼠标，12000DPI，RGB灯效，人体工学设计', '199.00', '电脑配件', 'DPI：12000\n按键：7个可编程按键\n连接：有线USB\n灯效：RGB背光\n重量：85g\n线长：1.8m', '上架']
        ]

    products_data = [
        {
            '产品名称': '智能手机A1',
            '产品描述': '6.1寸全面屏，128GB存储，5000mAh大电池，支持快充',
            '价格': '2999.00',
            '分类': '数码电子',
            '详细信息': '处理器：骁龙888\n内存：8GB\n存储：128GB\n屏幕：6.1寸OLED\n电池：5000mAh\n摄像头：5000万像素三摄',
            '状态': '上架'
        },
        {
            '产品名称': '无线蓝牙耳机B2',
            '产品描述': '降噪蓝牙耳机，30小时续航，IPX7防水',
            '价格': '399.00',
            '分类': '数码配件',
            '详细信息': '连接方式：蓝牙5.0\n续航时间：30小时\n防水等级：IPX7\n降噪：主动降噪\n充电接口：Type-C',
            '状态': '上架'
        },
        {
            '产品名称': '智能手表C3',
            '产品描述': '健康监测，运动追踪，7天续航，支持通话',
            '价格': '1299.00',
            '分类': '智能穿戴',
            '详细信息': '屏幕：1.4寸彩屏\n续航：7天\n防水：50米\n功能：心率监测、血氧检测、GPS定位\n系统：自研OS',
            '状态': '上架'
        },
        {
            '产品名称': '笔记本电脑D4',
            '产品描述': '轻薄本，14寸屏幕，16GB内存，512GB固态硬盘',
            '价格': '5999.00',
            '分类': '电脑办公',
            '详细信息': '处理器：Intel i7-12700H\n内存：16GB DDR4\n硬盘：512GB SSD\n显卡：集成显卡\n屏幕：14寸2K屏\n重量：1.5kg',
            '状态': '上架'
        },
        {
            '产品名称': '无线充电器E5',
            '产品描述': '15W快速无线充电，支持多种设备，带LED指示灯',
            '价格': '99.00',
            '分类': '数码配件',
            '详细信息': '功率：15W\n兼容性：支持Qi标准设备\n材质：铝合金+玻璃\n指示灯：LED状态显示\n安全保护：过温、过压、异物检测',
            '状态': '上架'
        },
        {
            '产品名称': '游戏鼠标F6',
            '产品描述': '电竞游戏鼠标，12000DPI，RGB灯效，人体工学设计',
            '价格': '199.00',
            '分类': '电脑配件',
            '详细信息': 'DPI：12000\n按键：7个可编程按键\n连接：有线USB\n灯效：RGB背光\n重量：85g\n线长：1.8m',
            '状态': '上架'
        }
    ]

    if HAS_PANDAS:
        df = pd.DataFrame(products_data)
        return df
    else:
        return products_data


def create_csv_file(filename, data):
    """创建CSV文件"""
    with open(filename, 'w', encoding='utf-8-sig') as f:
        for i, row in enumerate(data):
            if i == 0:
                # 写入表头
                f.write(','.join(row) + '\n')
            else:
                # 写入数据行，处理包含逗号的字段
                formatted_row = []
                for field in row:
                    if ',' in str(field) or '\n' in str(field):
                        formatted_row.append(f'"{field}"')
                    else:
                        formatted_row.append(str(field))
                f.write(','.join(formatted_row) + '\n')


def main():
    """主函数"""
    # 创建data目录
    if not os.path.exists('data'):
        os.makedirs('data')

    if HAS_PANDAS:
        # 使用pandas创建Excel文件
        faq_df = create_faq_sample()
        faq_df.to_excel('data/faq.xlsx', index=False, engine='openpyxl')
        print("已创建FAQ示例文件: data/faq.xlsx")

        products_df = create_products_sample()
        products_df.to_excel('data/products.xlsx', index=False, engine='openpyxl')
        print("已创建产品示例文件: data/products.xlsx")
    else:
        # 创建CSV文件
        faq_data = create_faq_sample()
        if isinstance(faq_data, list):
            faq_rows = faq_data
        else:
            # 转换DataFrame为列表
            faq_rows = [list(faq_data.columns)] + faq_data.values.tolist()

        create_csv_file('data/faq.csv', faq_rows)
        print("已创建FAQ示例文件: data/faq.csv (请用Excel打开并保存为.xlsx格式)")

        products_data = create_products_sample()
        if isinstance(products_data, list):
            products_rows = products_data
        else:
            products_rows = [list(products_data.columns)] + products_data.values.tolist()

        create_csv_file('data/products.csv', products_rows)
        print("已创建产品示例文件: data/products.csv (请用Excel打开并保存为.xlsx格式)")

    print("\n示例数据创建完成！")
    print("FAQ库包含6条常见问题")
    print("产品库包含6个示例产品")


if __name__ == "__main__":
    main()
