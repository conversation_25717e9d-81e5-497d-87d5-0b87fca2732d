wchat许可证使用指南
====================

🔐 什么是许可证？
许可证是软件的使用授权，确保软件只能在授权的硬件上运行。

📋 获取许可证的步骤：

1. 安装软件
   - 运行 安装.bat
   - 等待安装完成

2. 获取硬件ID
   - 运行 许可证管理.bat
   - 选择"1. 查看硬件信息"
   - 记录显示的硬件ID

3. 申请许可证
   - 将硬件ID发送给管理员
   - 说明需要的功能和使用期限
   - 等待管理员生成许可证密钥

4. 安装许可证
   - 运行 许可证管理.bat
   - 选择"3. 安装许可证"
   - 输入获取的许可证密钥

5. 验证许可证
   - 运行 许可证管理.bat
   - 选择"2. 检查许可证状态"
   - 确认许可证有效

6. 启动程序
   - 运行 启动.bat
   - 系统自动验证许可证
   - 验证通过后正常使用

🔧 常见问题：

Q: 硬件ID是什么？
A: 硬件ID是根据电脑硬件信息生成的唯一标识，用于绑定许可证。

Q: 可以在多台电脑上使用吗？
A: 不可以，许可证与特定硬件绑定，只能在授权的电脑上使用。

Q: 许可证过期了怎么办？
A: 联系管理员续期或获取新的许可证密钥。

Q: 更换硬件后还能使用吗？
A: 需要重新获取硬件ID并申请新的许可证。

💡 注意事项：
- 请妥善保管许可证密钥
- 不要随意更换硬件配置
- 定期检查许可证有效期
- 及时联系管理员续期

📞 技术支持：
如有问题，请联系管理员并提供：
- 硬件ID
- 错误信息
- 问题描述
