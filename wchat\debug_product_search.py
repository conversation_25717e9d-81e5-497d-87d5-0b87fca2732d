#!/usr/bin/env python3
"""
调试产品搜索逻辑
"""
import os
import sys
from pathlib import Path

# 添加项目路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def debug_product_search():
    """调试产品搜索逻辑"""
    print("🔍 调试产品搜索逻辑")
    print("=" * 60)
    
    try:
        from src.database.enhanced_reader import EnhancedProductReader
        from config import config
        
        # 加载产品数据
        product_reader = EnhancedProductReader(config.database.products_file)
        print(f"✅ 加载产品数据: {len(product_reader.data)} 条记录")
        
        # 显示所有产品
        print(f"\n📦 所有产品:")
        for i, (_, row) in enumerate(product_reader.data.iterrows(), 1):
            print(f"   {i}. {row['产品名称']} - {row['分类']} - ¥{row['价格']}")
            print(f"      描述: {row['产品描述']}")
            if '产品关键词' in row:
                print(f"      关键词: {row.get('产品关键词', 'N/A')}")
            print()
        
        # 测试查询"电脑"
        test_query = "电脑"
        print(f"🔍 测试查询: '{test_query}'")
        print("-" * 40)
        
        # 手动计算每个产品的匹配分数
        query = test_query.lower()
        
        for _, row in product_reader.data.iterrows():
            if row['状态'] != '上架':
                continue
            
            product_name = row['产品名称']
            print(f"\n产品: {product_name}")
            
            # 计算匹配分数
            score = product_reader._calculate_product_score(query, row)
            print(f"总分数: {score:.3f}")
            
            # 详细分析
            product_name_lower = str(row['产品名称']).lower()
            description = str(row.get('产品描述', '')).lower()
            category = str(row.get('分类', '')).lower()
            
            print(f"  产品名称: '{product_name_lower}'")
            print(f"  描述: '{description}'")
            print(f"  分类: '{category}'")
            
            # 名称匹配
            from fuzzywuzzy import fuzz
            name_score = fuzz.ratio(query, product_name_lower) / 100
            print(f"  名称匹配分数: {name_score:.3f}")
            
            # 特殊关键词匹配
            if query in ['电脑', '计算机', 'pc'] and ('电脑' in product_name_lower or 'pc' in product_name_lower):
                enhanced_name_score = max(name_score, 0.95)
                print(f"  增强名称分数: {enhanced_name_score:.3f} (特殊关键词匹配)")
            else:
                enhanced_name_score = name_score
                print(f"  增强名称分数: {enhanced_name_score:.3f} (无特殊匹配)")
            
            # 描述匹配
            desc_score = fuzz.partial_ratio(query, description) / 100
            print(f"  描述匹配分数: {desc_score:.3f}")
            
            # 分类匹配
            category_score = fuzz.partial_ratio(query, category) / 100
            print(f"  分类匹配分数: {category_score:.3f}")
            
            # 关键词匹配
            keywords = str(row.get('产品关键词', '')).lower().split(',')
            keyword_score = 0
            if keywords and keywords != ['']:
                for keyword in keywords:
                    keyword = keyword.strip()
                    if keyword in query or query in keyword:
                        keyword_score += 1
                    else:
                        fuzzy_score = fuzz.partial_ratio(keyword, query) / 100
                        if fuzzy_score > 0.8:
                            keyword_score += fuzzy_score
                keyword_final_score = keyword_score / len(keywords) if len(keywords) > 0 else 0
                print(f"  关键词匹配分数: {keyword_final_score:.3f} (关键词: {keywords})")
            else:
                keyword_final_score = 0
                print(f"  关键词匹配分数: {keyword_final_score:.3f} (无关键词)")
            
            # 计算各项得分
            scores = []
            scores.append(enhanced_name_score * 2.0)  # 名称权重
            if keywords and keywords != ['']:
                scores.append(keyword_final_score * 1.5)  # 关键词权重
            scores.append(desc_score * 0.8)  # 描述权重
            scores.append(category_score * 1.2)  # 分类权重
            
            print(f"  各项加权分数: {[f'{s:.3f}' for s in scores]}")
            print(f"  最终分数: {max(scores):.3f}")
        
        # 使用实际搜索方法
        print(f"\n🔍 实际搜索结果:")
        products = product_reader.search_products(test_query)
        for i, product in enumerate(products, 1):
            print(f"   {i}. {product['产品名称']} (分数: {product['score']:.3f})")
        
        return True
        
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_different_queries():
    """测试不同查询的匹配结果"""
    print("\n🧪 测试不同查询")
    print("=" * 60)
    
    try:
        from src.database.enhanced_reader import EnhancedProductReader
        from config import config
        
        product_reader = EnhancedProductReader(config.database.products_file)
        
        test_queries = [
            "电脑",
            "笔记本",
            "笔记本电脑", 
            "手机",
            "耳机",
            "鼠标",
            "充电器",
            "手表"
        ]
        
        for query in test_queries:
            print(f"\n查询: '{query}'")
            products = product_reader.search_products(query)
            
            if products:
                print(f"  找到 {len(products)} 个产品:")
                for i, product in enumerate(products, 1):
                    print(f"    {i}. {product['产品名称']} (分数: {product['score']:.3f})")
            else:
                print(f"  无匹配产品")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 产品搜索逻辑调试")
    print()
    
    # 运行调试
    debug_product_search()
    
    # 测试不同查询
    test_different_queries()

if __name__ == "__main__":
    main()
