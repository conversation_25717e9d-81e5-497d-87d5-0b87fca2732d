"""
监听模式切换工具
快速切换全部监听和列表监听模式
"""
import os
import json

def load_config():
    """加载配置"""
    config_path = os.path.join(os.path.dirname(__file__), 'config', 'config.json')
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            return json.load(f), config_path
    except Exception as e:
        print(f"加载配置失败: {e}")
        return None, None

def save_config(config, config_path):
    """保存配置"""
    try:
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=2)
        return True
    except Exception as e:
        print(f"保存配置失败: {e}")
        return False

def show_current_config(config):
    """显示当前配置"""
    wechat_config = config.get('wechat', {})
    listen_all = wechat_config.get('listen_all', False)
    listen_list = wechat_config.get('listen_list', [])
    auto_reply = wechat_config.get('auto_reply', True)
    reply_delay = wechat_config.get('reply_delay', 2)
    
    print("=" * 50)
    print("当前配置")
    print("=" * 50)
    print(f"监听模式: {'🌐 全部消息' if listen_all else '📋 指定列表'}")
    if not listen_all and listen_list:
        print(f"监听列表: {', '.join(listen_list)}")
    elif not listen_all and not listen_list:
        print("监听列表: 空（将监听所有消息）")
    print(f"自动回复: {'✅ 开启' if auto_reply else '❌ 关闭'}")
    print(f"回复延迟: {reply_delay}秒")

def switch_to_listen_all(config):
    """切换到全部监听模式"""
    config['wechat']['listen_all'] = True
    print("✅ 已切换到全部监听模式")
    print("📢 机器人将监听所有微信消息（除了自己发送的）")

def switch_to_list_mode(config):
    """切换到列表监听模式"""
    config['wechat']['listen_all'] = False
    print("✅ 已切换到列表监听模式")
    
    listen_list = config['wechat'].get('listen_list', [])
    if listen_list:
        print(f"📋 将只监听以下对象: {', '.join(listen_list)}")
    else:
        print("⚠️  监听列表为空，将监听所有消息")

def edit_listen_list(config):
    """编辑监听列表"""
    current_list = config['wechat'].get('listen_list', [])
    
    print("\n当前监听列表:")
    if current_list:
        for i, name in enumerate(current_list, 1):
            print(f"  {i}. {name}")
    else:
        print("  (空)")
    
    print("\n选择操作:")
    print("1. 添加监听对象")
    print("2. 删除监听对象")
    print("3. 清空列表")
    print("4. 返回主菜单")
    
    choice = input("\n请输入选择 (1-4): ").strip()
    
    if choice == "1":
        name = input("请输入要添加的监听对象名称: ").strip()
        if name and name not in current_list:
            current_list.append(name)
            config['wechat']['listen_list'] = current_list
            print(f"✅ 已添加: {name}")
        elif name in current_list:
            print("⚠️  该对象已在列表中")
        else:
            print("❌ 名称不能为空")
    
    elif choice == "2":
        if not current_list:
            print("❌ 列表为空，无法删除")
            return
        
        print("请选择要删除的对象:")
        for i, name in enumerate(current_list, 1):
            print(f"  {i}. {name}")
        
        try:
            index = int(input("请输入序号: ")) - 1
            if 0 <= index < len(current_list):
                removed = current_list.pop(index)
                config['wechat']['listen_list'] = current_list
                print(f"✅ 已删除: {removed}")
            else:
                print("❌ 无效序号")
        except ValueError:
            print("❌ 请输入有效数字")
    
    elif choice == "3":
        confirm = input("确认清空监听列表？(y/n): ").strip().lower()
        if confirm in ['y', 'yes', '是']:
            config['wechat']['listen_list'] = []
            print("✅ 已清空监听列表")
    
    elif choice == "4":
        return
    
    else:
        print("❌ 无效选择")

def toggle_auto_reply(config):
    """切换自动回复"""
    current = config['wechat'].get('auto_reply', True)
    config['wechat']['auto_reply'] = not current
    status = "开启" if not current else "关闭"
    print(f"✅ 自动回复已{status}")

def set_reply_delay(config):
    """设置回复延迟"""
    current = config['wechat'].get('reply_delay', 2)
    print(f"当前回复延迟: {current}秒")
    
    try:
        delay = float(input("请输入新的延迟时间（秒）: "))
        if delay >= 0:
            config['wechat']['reply_delay'] = delay
            print(f"✅ 回复延迟已设置为 {delay}秒")
        else:
            print("❌ 延迟时间不能为负数")
    except ValueError:
        print("❌ 请输入有效数字")

def main():
    """主函数"""
    print("微信客服机器人 - 监听模式切换工具")
    
    # 加载配置
    config, config_path = load_config()
    if not config:
        return
    
    while True:
        show_current_config(config)
        
        print("\n" + "=" * 50)
        print("操作菜单")
        print("=" * 50)
        print("1. 🌐 切换到全部监听模式")
        print("2. 📋 切换到列表监听模式")
        print("3. ✏️  编辑监听列表")
        print("4. 🔄 切换自动回复")
        print("5. ⏱️  设置回复延迟")
        print("6. 💾 保存并退出")
        print("7. 🚪 退出（不保存）")
        
        choice = input("\n请输入选择 (1-7): ").strip()
        
        if choice == "1":
            switch_to_listen_all(config)
        
        elif choice == "2":
            switch_to_list_mode(config)
        
        elif choice == "3":
            edit_listen_list(config)
        
        elif choice == "4":
            toggle_auto_reply(config)
        
        elif choice == "5":
            set_reply_delay(config)
        
        elif choice == "6":
            if save_config(config, config_path):
                print("✅ 配置已保存")
                break
            else:
                print("❌ 保存失败")
        
        elif choice == "7":
            confirm = input("确认退出而不保存？(y/n): ").strip().lower()
            if confirm in ['y', 'yes', '是']:
                print("👋 已退出，配置未保存")
                break
        
        else:
            print("❌ 无效选择")
        
        input("\n按回车键继续...")

if __name__ == "__main__":
    main()
