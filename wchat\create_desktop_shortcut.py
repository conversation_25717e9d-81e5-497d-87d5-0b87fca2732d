#!/usr/bin/env python3
"""
创建WChat桌面快捷方式
"""
import os
import sys
from pathlib import Path

def create_windows_shortcut():
    """创建Windows桌面快捷方式"""
    try:
        import winshell
        from win32com.client import Dispatch
        
        # 获取桌面路径
        desktop = winshell.desktop()
        
        # 快捷方式路径
        shortcut_path = os.path.join(desktop, "WChat智能客服系统.lnk")
        
        # 当前目录
        current_dir = Path(__file__).parent
        bat_file = current_dir / "启动WChat.bat"
        
        # 创建快捷方式
        shell = Dispatch('WScript.Shell')
        shortcut = shell.CreateShortCut(shortcut_path)
        shortcut.Targetpath = str(bat_file)
        shortcut.WorkingDirectory = str(current_dir)
        shortcut.IconLocation = str(bat_file)
        shortcut.Description = "WChat智能客服系统 - 一键启动"
        shortcut.save()
        
        print(f"✅ 桌面快捷方式已创建: {shortcut_path}")
        return True
        
    except ImportError:
        print("❌ 需要安装 pywin32 和 winshell 模块")
        print("请运行: pip install pywin32 winshell")
        return False
    except Exception as e:
        print(f"❌ 创建快捷方式失败: {e}")
        return False

def create_linux_desktop_entry():
    """创建Linux桌面快捷方式"""
    try:
        # 获取桌面路径
        desktop_dir = Path.home() / "Desktop"
        if not desktop_dir.exists():
            desktop_dir = Path.home() / "桌面"
        
        if not desktop_dir.exists():
            print("❌ 未找到桌面目录")
            return False
        
        # 当前目录
        current_dir = Path(__file__).parent
        script_file = current_dir / "start_wchat.sh"
        
        # 桌面文件内容
        desktop_content = f"""[Desktop Entry]
Version=1.0
Type=Application
Name=WChat智能客服系统
Comment=WChat智能客服系统 - 一键启动
Exec=bash "{script_file}"
Icon=applications-internet
Path={current_dir}
Terminal=true
StartupNotify=false
"""
        
        # 创建桌面文件
        desktop_file = desktop_dir / "WChat智能客服系统.desktop"
        with open(desktop_file, 'w', encoding='utf-8') as f:
            f.write(desktop_content)
        
        # 设置可执行权限
        os.chmod(desktop_file, 0o755)
        
        print(f"✅ 桌面快捷方式已创建: {desktop_file}")
        return True
        
    except Exception as e:
        print(f"❌ 创建快捷方式失败: {e}")
        return False

def main():
    """主函数"""
    print("🔗 创建WChat桌面快捷方式")
    print("=" * 50)
    
    if sys.platform.startswith('win'):
        print("检测到Windows系统...")
        success = create_windows_shortcut()
    elif sys.platform.startswith('linux'):
        print("检测到Linux系统...")
        success = create_linux_desktop_entry()
    elif sys.platform.startswith('darwin'):
        print("❌ macOS暂不支持自动创建快捷方式")
        print("请手动将启动脚本添加到Dock或创建别名")
        success = False
    else:
        print(f"❌ 不支持的操作系统: {sys.platform}")
        success = False
    
    if success:
        print("\n🎉 快捷方式创建成功！")
        print("现在您可以双击桌面图标启动WChat系统")
    else:
        print("\n❌ 快捷方式创建失败")
        print("请手动使用启动脚本")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
