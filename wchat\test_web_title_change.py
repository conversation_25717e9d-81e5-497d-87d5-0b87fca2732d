#!/usr/bin/env python3
"""
测试Web界面标题修改
"""
import os
import sys
from pathlib import Path

# 添加项目路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def test_html_templates():
    """测试HTML模板文件中的标题修改"""
    print("🔍 测试HTML模板标题修改")
    print("=" * 60)
    
    templates_dir = current_dir / "src" / "web" / "templates"
    
    # 需要检查的模板文件
    template_files = [
        "base.html",
        "dashboard.html", 
        "config.html",
        "faq.html",
        "products.html",
        "data_management.html",
        "login.html"
    ]
    
    old_title = "微信客服机器人"
    new_title = "私域自动化"
    
    results = []
    
    for template_file in template_files:
        template_path = templates_dir / template_file
        
        if template_path.exists():
            print(f"\n📄 检查文件: {template_file}")
            
            with open(template_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查是否还有旧标题
            old_count = content.count(old_title)
            new_count = content.count(new_title)
            
            if old_count > 0:
                print(f"  ❌ 仍包含旧标题 '{old_title}': {old_count} 处")
                results.append((template_file, False, f"旧标题{old_count}处"))
            elif new_count > 0:
                print(f"  ✅ 已更新为新标题 '{new_title}': {new_count} 处")
                results.append((template_file, True, f"新标题{new_count}处"))
            else:
                print(f"  ⚠️ 未找到相关标题")
                results.append((template_file, True, "无相关标题"))
        else:
            print(f"\n❌ 文件不存在: {template_file}")
            results.append((template_file, False, "文件不存在"))
    
    return results

def test_documentation_files():
    """测试文档文件中的标题修改"""
    print("\n📚 测试文档文件标题修改")
    print("=" * 60)
    
    # 需要检查的文档文件
    doc_files = [
        "README.md",
        "项目完成总结.md",
        "WChat机器人部署包说明.md", 
        "WChat机器人打包完成报告.md"
    ]
    
    old_title = "微信客服机器人"
    new_title = "私域自动化"
    
    results = []
    
    for doc_file in doc_files:
        doc_path = current_dir / doc_file
        
        if doc_path.exists():
            print(f"\n📄 检查文件: {doc_file}")
            
            with open(doc_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查是否还有旧标题
            old_count = content.count(old_title)
            new_count = content.count(new_title)
            
            if old_count > 0:
                print(f"  ⚠️ 仍包含旧标题 '{old_title}': {old_count} 处")
                # 显示包含旧标题的行
                lines = content.split('\n')
                for i, line in enumerate(lines, 1):
                    if old_title in line:
                        print(f"    第{i}行: {line.strip()}")
                results.append((doc_file, False, f"旧标题{old_count}处"))
            elif new_count > 0:
                print(f"  ✅ 已更新为新标题 '{new_title}': {new_count} 处")
                results.append((doc_file, True, f"新标题{new_count}处"))
            else:
                print(f"  ⚠️ 未找到相关标题")
                results.append((doc_file, True, "无相关标题"))
        else:
            print(f"\n❌ 文件不存在: {doc_file}")
            results.append((doc_file, False, "文件不存在"))
    
    return results

def test_startup_scripts():
    """测试启动脚本中的标题修改"""
    print("\n🚀 测试启动脚本标题修改")
    print("=" * 60)
    
    # 需要检查的启动脚本
    script_files = [
        "快速启动.bat",
        "启动WChat.bat"
    ]
    
    old_title = "微信客服机器人"
    new_title = "私域自动化"
    
    results = []
    
    for script_file in script_files:
        script_path = current_dir / script_file
        
        if script_path.exists():
            print(f"\n📄 检查文件: {script_file}")
            
            try:
                with open(script_path, 'r', encoding='utf-8') as f:
                    content = f.read()
            except UnicodeDecodeError:
                # 尝试其他编码
                try:
                    with open(script_path, 'r', encoding='gbk') as f:
                        content = f.read()
                except:
                    print(f"  ❌ 无法读取文件编码")
                    results.append((script_file, False, "编码错误"))
                    continue
            
            # 检查是否还有旧标题
            old_count = content.count(old_title)
            new_count = content.count(new_title)
            
            if old_count > 0:
                print(f"  ⚠️ 仍包含旧标题 '{old_title}': {old_count} 处")
                results.append((script_file, False, f"旧标题{old_count}处"))
            elif new_count > 0:
                print(f"  ✅ 已更新为新标题 '{new_title}': {new_count} 处")
                results.append((script_file, True, f"新标题{new_count}处"))
            else:
                print(f"  ⚠️ 未找到相关标题")
                results.append((script_file, True, "无相关标题"))
        else:
            print(f"\n❌ 文件不存在: {script_file}")
            results.append((script_file, False, "文件不存在"))
    
    return results

def simulate_web_interface():
    """模拟Web界面显示效果"""
    print("\n🌐 模拟Web界面显示效果")
    print("=" * 60)
    
    # 模拟各个页面的标题显示
    pages = [
        ("登录页面", "登录 - 私域自动化"),
        ("仪表板", "仪表板 - 私域自动化"),
        ("基础配置", "基础配置 - 私域自动化"),
        ("FAQ管理", "FAQ管理 - 私域自动化"),
        ("产品管理", "产品管理 - 私域自动化"),
        ("数据管理", "数据管理 - 私域自动化")
    ]
    
    print("页面标题预览:")
    print("-" * 40)
    
    for page_name, page_title in pages:
        print(f"📄 {page_name:10} → {page_title}")
    
    print(f"\n导航栏显示:")
    print(f"🤖 私域自动化")
    
    print(f"\n浏览器标签页:")
    print(f"🔖 私域自动化")
    
    return True

def main():
    """主函数"""
    print("🔄 Web界面标题修改测试")
    print()
    
    # 运行测试
    tests = [
        ("HTML模板", test_html_templates),
        ("文档文件", test_documentation_files),
        ("启动脚本", test_startup_scripts),
        ("界面预览", simulate_web_interface)
    ]
    
    all_results = []
    
    for test_name, test_func in tests:
        print(f"\n🔍 运行测试: {test_name}")
        try:
            results = test_func()
            if isinstance(results, list):
                all_results.extend([(test_name, *result) for result in results])
            else:
                all_results.append((test_name, "模拟测试", True, "预览成功"))
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            all_results.append((test_name, "测试异常", False, str(e)))
    
    # 总结
    print(f"\n" + "=" * 60)
    print(f"          标题修改测试总结")
    print(f"=" * 60)
    
    success_count = sum(1 for _, _, success, _ in all_results if success)
    total_count = len(all_results)
    
    print(f"测试结果统计:")
    for test_type, file_name, success, detail in all_results:
        status = "✅" if success else "❌"
        print(f"  {status} {test_type} - {file_name}: {detail}")
    
    print(f"\n总计: {success_count}/{total_count} 项测试通过")
    
    if success_count == total_count:
        print("🎉 所有标题修改完成！")
        print("\n💡 修改效果:")
        print("   - Web界面标题: 微信客服机器人 → 私域自动化")
        print("   - 浏览器标签页: 私域自动化")
        print("   - 导航栏品牌: 私域自动化")
        print("   - 登录页面: 私域自动化")
        print("   - 文档标题: 已全部更新")
        print("\n🚀 重启Web服务器查看效果!")
    else:
        print("⚠️ 部分文件可能需要手动检查")

if __name__ == "__main__":
    main()
