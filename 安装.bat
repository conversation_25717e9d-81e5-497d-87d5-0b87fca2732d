@echo off
chcp 65001 >nul
echo ========================================
echo   wchat微信智能客服机器人 - 加密版
echo ========================================
echo.

echo 🔍 检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 未检测到Python环境！
    echo 💡 请先安装Python 3.8或更高版本
    echo 📥 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo ✅ Python环境检查通过

echo.
echo 📦 安装Python依赖包...
cd wchat
pip install -r requirements.txt

if errorlevel 1 (
    echo ❌ 依赖包安装失败！
    echo 💡 请检查网络连接或尝试使用国内镜像源
    pause
    exit /b 1
)

echo ✅ 依赖包安装完成

echo.
echo 🔐 获取硬件信息...
python license_manager.py

echo.
echo 🎉 安装完成！
echo.
echo 📋 下一步操作:
echo    1. 联系管理员获取许可证密钥
echo    2. 运行 许可证管理.bat 安装许可证
echo    3. 运行 启动.bat 启动程序
echo.
pause
