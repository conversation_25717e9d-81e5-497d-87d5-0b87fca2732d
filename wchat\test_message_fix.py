#!/usr/bin/env python3
"""
测试消息监听修复
"""
import sys
import time
from pathlib import Path

# 添加项目路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def test_message_handler():
    """测试修复后的消息处理器"""
    print("=" * 60)
    print("          测试修复后的消息处理器")
    print("=" * 60)
    
    try:
        from src.bot.wechat_handler import WeChatHandler
        
        # 创建处理器
        handler = WeChatHandler()
        print("✅ 微信处理器创建成功")
        
        # 初始化微信
        if handler.initialize_wechat():
            print("✅ 微信初始化成功")
        else:
            print("❌ 微信初始化失败")
            return False
        
        # 获取状态
        status = handler.get_status()
        print(f"处理器状态: {status}")
        
        # 开始监听
        print("\n开始监听消息（30秒）...")
        handler.start_listening()
        
        # 提示用户发送测试消息
        print("\n请在微信中发送一条消息到任何聊天（包括文件传输助手）")
        print("监听中...")
        
        # 等待30秒
        for i in range(30):
            time.sleep(1)
            if i % 5 == 0:
                print(f"监听中... {30-i}秒剩余")
        
        # 停止监听
        handler.stop_listening()
        print("\n✅ 监听测试完成")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_direct_message_methods():
    """直接测试消息获取方法"""
    print("\n" + "=" * 60)
    print("          直接测试消息获取方法")
    print("=" * 60)
    
    try:
        from wxauto import WeChat
        
        wx = WeChat()
        print(f"✅ 微信连接成功，用户: {wx.nickname}")
        
        # 切换到文件传输助手并发送测试消息
        if wx.ChatWith("文件传输助手"):
            test_msg = f"测试消息修复 - {time.strftime('%H:%M:%S')}"
            wx.SendMsg(test_msg)
            print(f"✅ 发送测试消息: {test_msg}")
            
            # 等待消息
            time.sleep(3)
            
            # 测试不同的获取方法
            print("\n测试GetAllMessage方法:")
            try:
                all_msgs = wx.GetAllMessage()
                if all_msgs:
                    print(f"✅ 获取到 {len(all_msgs)} 条历史消息")
                    # 显示最后几条消息
                    for msg in all_msgs[-3:]:
                        sender = getattr(msg, 'sender', 'Unknown')
                        content = getattr(msg, 'content', 'No content')
                        print(f"   {sender}: {content}")
                else:
                    print("⚠️  未获取到消息")
            except Exception as e:
                print(f"❌ GetAllMessage失败: {e}")
            
            print("\n测试GetNextNewMessage方法:")
            try:
                next_msg = wx.GetNextNewMessage()
                if next_msg:
                    print(f"✅ 获取到新消息: {next_msg}")
                else:
                    print("⚠️  未获取到新消息")
            except Exception as e:
                print(f"❌ GetNextNewMessage失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 直接测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("开始测试消息监听修复...")
    
    # 测试直接方法
    if test_direct_message_methods():
        print("\n✅ 直接方法测试通过")
    else:
        print("\n❌ 直接方法测试失败")
        return
    
    # 测试处理器
    if test_message_handler():
        print("\n✅ 处理器测试通过")
    else:
        print("\n❌ 处理器测试失败")
    
    print("\n" + "=" * 60)
    print("测试完成！")
    print("=" * 60)
    
    print("\n修复说明:")
    print("1. 使用多种方法获取消息（GetNewMessage, GetNextNewMessage, GetAllMessage）")
    print("2. 监听列表为空时自动启用全局监听")
    print("3. 增强了消息过滤和重复检测")
    print("4. 改进了错误处理和日志记录")

if __name__ == "__main__":
    main()
