# 私域自动化系统 - 加密版

## 🔐 产品特点

这是私域自动化系统的**加密版本**，具有以下特点：

- 🔒 **硬件锁定** - 绑定特定硬件，防止非授权使用
- 🎫 **许可证管理** - 基于许可证的授权机制
- 🛡️ **防拷贝保护** - 防止软件被随意复制和传播
- ⏰ **有效期控制** - 支持时间限制的许可证

## 🚀 快速开始

### 1. 系统要求

- **操作系统**: Windows 10/11 (推荐)
- **Python版本**: 3.8 或更高版本
- **微信**: 微信PC版 (必须)
- **许可证**: 有效的许可证密钥

### 2. 安装步骤

#### 第一步：安装程序
1. 双击运行 `安装.bat`
2. 等待依赖包安装完成
3. 记录显示的硬件ID

#### 第二步：获取许可证
1. 将硬件ID发送给管理员
2. 获取对应的许可证密钥
3. 双击运行 `许可证管理.bat`
4. 选择"安装许可证"并输入密钥

#### 第三步：启动程序
1. 双击运行 `启动.bat`
2. 系统会自动验证许可证
3. 验证通过后正常启动

### 3. 许可证管理

#### 查看硬件信息
```bash
# 运行许可证管理工具
python license_manager.py

# 选择"1. 查看硬件信息"
# 记录显示的硬件ID
```

#### 安装许可证
```bash
# 运行许可证管理工具
python license_manager.py

# 选择"3. 安装许可证"
# 输入从管理员获取的许可证密钥
```

#### 检查许可证状态
```bash
# 运行许可证管理工具
python license_manager.py

# 选择"2. 检查许可证状态"
# 查看许可证有效期和授权功能
```

## 🔐 安全特性

### 硬件绑定
- 软件与特定硬件绑定
- 无法在其他电脑上运行
- 防止非授权复制

### 许可证验证
- 启动时自动验证许可证
- 支持有效期检查
- 功能权限控制

### 防拷贝保护
- 许可证文件加密存储
- 硬件指纹验证
- 防篡改机制

## 📋 功能说明

### 核心功能
- 🎤 **语音识别** - 自动识别微信语音消息
- 💬 **智能回复** - FAQ + 产品推荐 + AI对话
- 🛍️ **产品推荐** - 关键词匹配 + 图片展示
- 🌐 **Web管理** - 可视化配置和数据管理

### 授权功能
根据许可证类型，可能包含：
- ✅ 语音识别功能
- ✅ AI对话功能
- ✅ 产品推荐功能
- ✅ Web管理界面
- ✅ 多用户支持

## 🔧 故障排除

### 许可证问题

#### 许可证验证失败
```
解决方案:
1. 检查许可证是否正确安装
2. 确认许可证适用于当前硬件
3. 检查许可证是否已过期
4. 联系管理员重新获取许可证
```

#### 硬件ID不匹配
```
解决方案:
1. 确认在正确的电脑上运行
2. 检查硬件是否有变更
3. 联系管理员更新许可证
```

#### 许可证过期
```
解决方案:
1. 联系管理员续期许可证
2. 获取新的许可证密钥
3. 重新安装许可证
```

### 程序问题

#### 微信连接失败
```
解决方案:
1. 确保微信PC版已登录
2. 以管理员身份运行程序
3. 检查微信版本兼容性
```

#### 功能受限
```
解决方案:
1. 检查许可证授权的功能
2. 确认许可证类型是否支持该功能
3. 联系管理员升级许可证
```

## 📞 技术支持

### 获取支持
1. **许可证问题** - 联系管理员
2. **技术问题** - 查看程序日志
3. **功能问题** - 检查许可证授权

### 联系方式
- 请通过指定渠道联系管理员
- 提供硬件ID和错误信息
- 说明具体的问题现象

## 📄 许可协议

- 本软件受许可证保护
- 仅限授权用户使用
- 禁止复制、传播或逆向工程
- 违反许可协议将承担法律责任

---

**私域自动化系统 - 加密版**
**安全可靠 · 功能强大 · 专业服务** 🔐🤖✨
