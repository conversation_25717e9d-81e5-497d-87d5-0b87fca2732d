@echo off
chcp 65001 >nul
echo ========================================
echo   WChat微信客服机器人 - 自动安装
echo ========================================
echo.

echo 正在检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] 未检测到Python环境
    echo 请先安装Python 3.8或更高版本
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo [OK] Python环境检测成功

echo.
echo 正在安装依赖包...
python install_deps.py

echo.
echo 正在创建桌面快捷方式...
python -c "
import os
from pathlib import Path
desktop = Path.home() / 'Desktop'
shortcut_path = desktop / 'WChat机器人.bat'
current_dir = Path.cwd()
content = f'''@echo off
cd /d "{current_dir}"
python quick_start.py
pause'''
with open(shortcut_path, 'w', encoding='gbk') as f:
    f.write(content)
print(f'[OK] 桌面快捷方式已创建: {shortcut_path}')
"

echo.
echo ========================================
echo   安装完成！
echo ========================================
echo.
echo 下一步操作:
echo 1. 配置API密钥 (编辑 config/config.json)
echo 2. 双击桌面上的 "WChat机器人" 启动
echo 3. 或运行: python quick_start.py
echo.
pause
