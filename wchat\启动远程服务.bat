@echo off
chcp 65001 >nul
echo ========================================
echo   私域自动化系统 - 远程服务启动
echo ========================================

echo.
echo 🌐 正在启动远程访问服务...
echo.

REM 检查Python环境
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python未安装或未添加到PATH环境变量
    echo 💡 请先安装Python 3.8+
    pause
    exit /b 1
)

REM 检查依赖包
echo 📦 检查依赖包...
python -c "import flask" >nul 2>&1
if errorlevel 1 (
    echo ❌ Flask未安装，正在安装依赖包...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo ❌ 依赖包安装失败
        pause
        exit /b 1
    )
)

REM 显示网络信息
echo.
echo 📡 网络信息:
python -c "
import socket
import requests
import json
from pathlib import Path

# 获取本机IP
try:
    s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
    s.connect(('*******', 80))
    local_ip = s.getsockname()[0]
    s.close()
    print(f'   本机IP: {local_ip}')
except:
    print('   本机IP: 获取失败')

# 获取公网IP
try:
    response = requests.get('https://api.ipify.org', timeout=5)
    public_ip = response.text.strip()
    print(f'   公网IP: {public_ip}')
except:
    print('   公网IP: 获取失败')

# 读取配置
config_file = Path('config/config.json')
if config_file.exists():
    with open(config_file, 'r', encoding='utf-8') as f:
        config = json.load(f)
    web_config = config.get('web', {})
    host = web_config.get('host', '127.0.0.1')
    port = web_config.get('port', 5000)
    
    print(f'   监听地址: {host}:{port}')
    
    if host == '0.0.0.0':
        print(f'   局域网访问: http://{local_ip}:{port}')
        try:
            print(f'   公网访问: http://{public_ip}:{port} (需要端口映射)')
        except:
            pass
    
    external_url = web_config.get('external_url')
    if external_url:
        print(f'   外部URL: {external_url}')
    
    allowed_ips = web_config.get('allowed_ips', [])
    if allowed_ips:
        print(f'   IP白名单: {len(allowed_ips)}个IP')
    else:
        print('   IP白名单: 未设置（允许所有IP）')
"

echo.
echo ⚠️  重要提示:
echo    1. 确保防火墙已开放相应端口
echo    2. 如需公网访问，请配置路由器端口映射
echo    3. 建议设置IP白名单限制访问
echo    4. 生产环境建议启用HTTPS
echo.

REM 询问是否继续
set /p confirm="是否继续启动远程服务? (y/n): "
if /i not "%confirm%"=="y" (
    echo 取消启动
    pause
    exit /b 0
)

echo.
echo 🚀 正在启动Web服务器...
echo.
echo ============================================================
echo                    服务启动信息
echo ============================================================

REM 启动Web服务器
cd /d "%~dp0"
python web_config.py

echo.
echo 服务已停止
pause
