#!/bin/bash

# WChat私域自动化系统 - 一键启动脚本 (Linux/Mac)

# 设置脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

# 打印横幅
echo ""
echo "╔══════════════════════════════════════════════════════════════╗"
echo "║                                                              ║"
echo "║                    🤖 WChat私域自动化系统                     ║"
echo "║                                                              ║"
echo "║                        一键启动程序                          ║"
echo "║                                                              ║"
echo "╚══════════════════════════════════════════════════════════════╝"
echo ""

echo "🚀 正在启动 WChat私域自动化系统..."
echo ""

# 检查Python
if ! command -v python3 &> /dev/null; then
    if ! command -v python &> /dev/null; then
        echo "❌ 未找到Python，请先安装Python 3.8+"
        exit 1
    else
        PYTHON_CMD="python"
    fi
else
    PYTHON_CMD="python3"
fi

echo "✅ 使用Python: $PYTHON_CMD"

# 启动程序
$PYTHON_CMD start_wchat.py

echo ""
echo "👋 感谢使用 WChat私域自动化系统！"
