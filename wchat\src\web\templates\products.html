{% extends "base.html" %}

{% block title %}产品管理 - 私域自动化{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-box me-2"></i>
        产品管理
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <button type="button" class="btn btn-outline-primary btn-sm me-2" onclick="location.reload()">
            <i class="fas fa-sync-alt me-1"></i>刷新数据
        </button>
        <button type="button" class="btn btn-outline-info btn-sm me-2" onclick="showUploadModal()">
            <i class="fas fa-upload me-1"></i>上传数据
        </button>
        <button type="button" class="btn btn-success btn-sm" onclick="downloadTemplate()">
            <i class="fas fa-download me-1"></i>下载模板
        </button>
    </div>
</div>

<!-- 统计信息 -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-primary">{{ product_list|length }}</h5>
                <p class="card-text">总产品数量</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-success">
                    {{ product_list|selectattr('状态', 'equalto', '上架')|list|length }}
                </h5>
                <p class="card-text">上架状态</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-warning">
                    {{ product_list|map(attribute='分类')|unique|list|length }}
                </h5>
                <p class="card-text">分类数量</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-info">Excel</h5>
                <p class="card-text">数据格式</p>
            </div>
        </div>
    </div>
</div>

<!-- 搜索和筛选 -->
<div class="card mb-4">
    <div class="card-body">
        <div class="row">
            <div class="col-md-4">
                <input type="text" class="form-control" id="searchInput" placeholder="搜索产品名称或描述...">
            </div>
            <div class="col-md-3">
                <select class="form-select" id="categoryFilter">
                    <option value="">所有分类</option>
                    {% for category in product_list|map(attribute='分类')|unique %}
                        <option value="{{ category }}">{{ category }}</option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-3">
                <select class="form-select" id="statusFilter">
                    <option value="">所有状态</option>
                    <option value="上架">上架</option>
                    <option value="下架">下架</option>
                </select>
            </div>
            <div class="col-md-2">
                <button class="btn btn-primary w-100" onclick="filterProducts()">
                    <i class="fas fa-search me-1"></i>筛选
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 产品列表 -->
<div class="card">
    <div class="card-header">
        <h6 class="mb-0">产品列表</h6>
    </div>
    <div class="card-body">
        {% if product_list %}
            <div class="table-responsive">
                <table class="table table-hover" id="productTable">
                    <thead>
                        <tr>
                            <th>序号</th>
                            <th>产品名称</th>
                            <th>产品描述</th>
                            <th>价格</th>
                            <th>分类</th>
                            <th>状态</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for product in product_list %}
                        <tr data-category="{{ product.分类 }}" data-status="{{ product.状态 }}">
                            <td>{{ loop.index }}</td>
                            <td>
                                <strong>{{ product.产品名称 }}</strong>
                            </td>
                            <td>
                                <div class="text-truncate" style="max-width: 250px;" 
                                     title="{{ product.产品描述 }}">
                                    {{ product.产品描述 }}
                                </div>
                            </td>
                            <td>
                                <span class="text-success fw-bold">¥{{ product.价格 }}</span>
                            </td>
                            <td>
                                <span class="badge bg-info">{{ product.分类 }}</span>
                            </td>
                            <td>
                                {% if product.状态 == '上架' %}
                                    <span class="badge bg-success">上架</span>
                                {% else %}
                                    <span class="badge bg-secondary">下架</span>
                                {% endif %}
                            </td>
                            <td>
                                <button class="btn btn-sm btn-outline-primary" 
                                        onclick="showProductDetail({{ loop.index0 }})">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        {% else %}
            <div class="text-center py-5">
                <i class="fas fa-box fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">暂无产品数据</h5>
                <p class="text-muted">请确保产品文件存在并格式正确</p>
                <button class="btn btn-primary" onclick="downloadTemplate()">
                    <i class="fas fa-download me-1"></i>下载Excel模板
                </button>
            </div>
        {% endif %}
    </div>
</div>

<!-- 产品详情模态框 -->
<div class="modal fade" id="productDetailModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">产品详情</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="productDetailContent">
                <!-- 产品详情内容 -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>

<!-- 使用说明 -->
<div class="card mt-4">
    <div class="card-header">
        <h6 class="mb-0">
            <i class="fas fa-info-circle me-2"></i>
            使用说明
        </h6>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <h6>Excel文件格式要求：</h6>
                <ul>
                    <li>文件名：products.xlsx</li>
                    <li>必需列：产品名称、产品描述、价格、分类、详细信息、状态</li>
                    <li>价格：数字格式，不包含货币符号</li>
                    <li>状态：只能是"上架"或"下架"</li>
                </ul>
            </div>
            <div class="col-md-6">
                <h6>匹配规则：</h6>
                <ul>
                    <li>优先匹配产品名称</li>
                    <li>然后匹配产品描述</li>
                    <li>只有状态为"上架"的产品才会被推荐</li>
                    <li>支持关键词搜索和相似度匹配</li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
const productData = {{ product_list|tojson }};

function filterProducts() {
    const searchText = $('#searchInput').val().toLowerCase();
    const categoryFilter = $('#categoryFilter').val();
    const statusFilter = $('#statusFilter').val();
    
    $('#productTable tbody tr').each(function() {
        const row = $(this);
        const text = row.text().toLowerCase();
        const category = row.data('category');
        const status = row.data('status');
        
        let show = true;
        
        // 文本搜索
        if (searchText && !text.includes(searchText)) {
            show = false;
        }
        
        // 分类筛选
        if (categoryFilter && category !== categoryFilter) {
            show = false;
        }
        
        // 状态筛选
        if (statusFilter && status !== statusFilter) {
            show = false;
        }
        
        row.toggle(show);
    });
}

function showProductDetail(index) {
    const product = productData[index];
    if (!product) return;
    
    const detailHtml = `
        <div class="row">
            <div class="col-md-6">
                <h6>基本信息</h6>
                <table class="table table-borderless">
                    <tr><td><strong>产品名称：</strong></td><td>${product.产品名称}</td></tr>
                    <tr><td><strong>价格：</strong></td><td class="text-success fw-bold">¥${product.价格}</td></tr>
                    <tr><td><strong>分类：</strong></td><td><span class="badge bg-info">${product.分类}</span></td></tr>
                    <tr><td><strong>状态：</strong></td><td>
                        ${product.状态 === '上架' ? '<span class="badge bg-success">上架</span>' : '<span class="badge bg-secondary">下架</span>'}
                    </td></tr>
                </table>
            </div>
            <div class="col-md-6">
                <h6>产品描述</h6>
                <p>${product.产品描述}</p>
            </div>
        </div>
        <div class="row mt-3">
            <div class="col-12">
                <h6>详细信息</h6>
                <div class="bg-light p-3 rounded">
                    <pre style="white-space: pre-wrap; margin: 0;">${product.详细信息 || '暂无详细信息'}</pre>
                </div>
            </div>
        </div>
    `;
    
    $('#productDetailContent').html(detailHtml);
    $('#productDetailModal').modal('show');
}

function downloadTemplate() {
    // 创建示例数据
    const templateData = [
        ['产品名称', '产品描述', '价格', '分类', '详细信息', '状态'],
        ['智能手机A1', '6.1寸全面屏，128GB存储', '2999.00', '数码电子', '处理器：骁龙888\\n内存：8GB\\n存储：128GB', '上架'],
        ['蓝牙耳机B2', '降噪蓝牙耳机，30小时续航', '399.00', '数码配件', '连接方式：蓝牙5.0\\n续航时间：30小时', '上架'],
        ['智能手表C3', '健康监测，运动追踪', '1299.00', '智能穿戴', '屏幕：1.4寸彩屏\\n续航：7天', '上架']
    ];
    
    // 转换为CSV格式
    const csvContent = templateData.map(row => row.join(',')).join('\n');
    
    // 创建下载链接
    const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', 'products_template.csv');
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    showAlert('产品模板下载成功，请用Excel打开并保存为.xlsx格式', 'success');
}

function showAlert(message, type) {
    const alertHtml = `
        <div class="alert alert-${type} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    $('.main-content .pt-3').prepend(alertHtml);
}

// 实时搜索
$('#searchInput').on('input', function() {
    filterProducts();
});

$('#categoryFilter, #statusFilter').on('change', function() {
    filterProducts();
});

// 上传功能
function showUploadModal() {
    $('#uploadModal').modal('show');
}

function downloadTemplate() {
    window.location.href = '/api/download_template/products';
}

function uploadProducts() {
    const fileInput = document.getElementById('productFile');
    const replaceData = document.getElementById('replaceData').checked;

    if (!fileInput.files.length) {
        alert('请选择文件');
        return;
    }

    const formData = new FormData();
    formData.append('file', fileInput.files[0]);
    formData.append('replace', replaceData);

    // 显示上传进度
    $('#uploadProgress').show();
    $('#uploadBtn').prop('disabled', true);

    $.ajax({
        url: '/api/upload_products',
        type: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        success: function(response) {
            alert('产品数据上传成功！');
            $('#uploadModal').modal('hide');
            location.reload();
        },
        error: function(xhr) {
            const error = xhr.responseJSON ? xhr.responseJSON.error : '上传失败';
            alert('上传失败：' + error);
        },
        complete: function() {
            $('#uploadProgress').hide();
            $('#uploadBtn').prop('disabled', false);
        }
    });
}
</script>

<!-- 上传模态框 -->
<div class="modal fade" id="uploadModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-upload me-2"></i>上传产品数据
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label for="productFile" class="form-label">选择文件</label>
                    <input type="file" class="form-control" id="productFile" accept=".xlsx,.xls,.csv">
                    <div class="form-text">支持Excel (.xlsx, .xls) 和CSV (.csv) 格式</div>
                </div>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="replaceData">
                    <label class="form-check-label" for="replaceData">
                        替换现有数据（不勾选则合并数据）
                    </label>
                </div>
                <div id="uploadProgress" class="mt-3" style="display: none;">
                    <div class="progress">
                        <div class="progress-bar progress-bar-striped progress-bar-animated"
                             style="width: 100%">上传中...</div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="uploadBtn" onclick="uploadProducts()">
                    <i class="fas fa-upload me-1"></i>上传
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}
