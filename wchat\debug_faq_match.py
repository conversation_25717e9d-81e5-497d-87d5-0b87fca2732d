#!/usr/bin/env python3
"""
调试FAQ匹配问题
"""
import pandas as pd
from src.database.enhanced_reader import EnhancedFAQReader
from config import config

def debug_faq_matching():
    """调试FAQ匹配"""
    print("=" * 60)
    print("          调试FAQ匹配")
    print("=" * 60)
    
    # 加载FAQ数据
    faq_reader = EnhancedFAQReader(config.database.faq_file)
    print(f"FAQ数据加载: {len(faq_reader.data)} 条记录")
    
    # 测试具体的匹配
    test_question = "能退货么"
    print(f"\n测试问题: '{test_question}'")
    
    # 调用search_faq方法
    result = faq_reader.search_faq(test_question)
    print(f"匹配结果: {result}")
    
    # 手动检查匹配逻辑
    print(f"\n手动检查匹配过程:")
    question_lower = test_question.lower()
    print(f"问题小写: '{question_lower}'")
    
    for i, row in faq_reader.data.iterrows():
        if row['状态'] != '启用':
            continue
        
        keywords = str(row['问题关键词']).lower().split(',')
        print(f"\n第{i+1}行: {row['标准问题']}")
        print(f"关键词: {keywords}")
        
        keyword_score = 0
        for keyword in keywords:
            keyword = keyword.strip()
            print(f"  检查关键词: '{keyword}' 是否在 '{question_lower}' 中")
            if keyword in question_lower:
                print(f"    ✅ 匹配!")
                keyword_score += 1
            else:
                print(f"    ❌ 不匹配")
        
        print(f"关键词得分: {keyword_score}/{len(keywords)}")
        
        # 检查模糊匹配
        from fuzzywuzzy import fuzz
        for keyword in keywords:
            keyword = keyword.strip()
            if keyword:
                fuzzy_score = fuzz.partial_ratio(keyword, question_lower) / 100
                print(f"  模糊匹配 '{keyword}' vs '{question_lower}': {fuzzy_score:.3f}")
        
        if i == 0:  # 只检查第一行退货相关的
            break

if __name__ == "__main__":
    debug_faq_matching()
