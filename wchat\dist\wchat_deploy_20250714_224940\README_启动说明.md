# WChat 智能客服系统 - 一键启动说明

## 🚀 快速启动

### Windows 用户
1. **双击启动**：直接双击 `启动WChat.bat` 文件
2. **快速启动**：在命令提示符中运行 `python quick_start.py`
3. **完整启动**：在命令提示符中运行 `python start_wchat.py`

### Linux/Mac 用户
1. **脚本启动**：在终端中运行 `./start_wchat.sh`
2. **Python启动**：在终端中运行 `python3 quick_start.py`

## 📁 启动文件说明

### 推荐使用
- **`启动WChat.bat`** (Windows) - 双击即可启动，最简单
- **`quick_start.py`** - 快速启动脚本，适合日常使用

### 高级选项
- **`start_wchat.py`** - 完整启动脚本，包含依赖检查和详细日志
- **`start_wchat.sh`** - Linux/Mac启动脚本

## 📋 启动流程

启动程序会自动执行以下步骤：

1. **🔍 检查系统依赖**
   - 自动检测并安装缺失的Python包
   - 验证所有必需组件

2. **🔧 检查配置文件**
   - 验证 `config.py` 配置文件
   - 检查AI服务配置

3. **📊 检查数据文件**
   - 验证FAQ和产品数据文件
   - 自动创建缺失的目录

4. **🌐 启动Web服务器**
   - 启动Flask Web应用
   - 监控启动状态

5. **🌍 打开浏览器**
   - 自动打开系统管理界面
   - 访问地址：http://localhost:5000

## 🎯 系统信息

### 默认登录信息
- **用户名**：`admin`
- **密码**：`admin123`

### 主要功能模块
- **📊 仪表板**：系统状态监控和统计信息
- **❓ FAQ管理**：常见问题的增删改查
- **🛍️ 产品管理**：产品信息的管理维护
- **⚙️ 配置管理**：系统参数和AI模型配置
- **📁 数据管理**：数据文件的导入导出

### 端口信息
- **Web界面**：http://localhost:5000
- **API接口**：http://localhost:5000/api/*

## 🔧 故障排除

### 常见问题

#### 1. 依赖包安装失败
```bash
# 手动安装依赖包
pip install flask pandas openpyxl requests jieba fuzzywuzzy openai beautifulsoup4
```

#### 2. 端口被占用
- 检查5000端口是否被其他程序占用
- 修改 `src/web/app.py` 中的端口配置

#### 3. 配置文件错误
- 检查 `config.py` 文件格式
- 确保AI服务配置正确

#### 4. 数据文件问题
- 确保 `data/` 目录存在
- 检查Excel文件格式是否正确

### 日志查看
启动程序会显示详细的启动日志，包括：
- 依赖检查结果
- 配置验证状态
- 服务器启动信息
- 错误诊断信息

## 🛑 停止服务

### 正常停止
- 在启动程序的控制台中按 `Ctrl+C`
- 程序会自动清理并安全退出

### 强制停止
如果程序无响应：
- Windows：在任务管理器中结束Python进程
- Linux/Mac：使用 `killall python` 或 `pkill -f start_wchat`

## 📁 文件结构

```
wchat/
├── start_wchat.py          # Python启动脚本
├── 启动WChat.bat           # Windows批处理启动文件
├── start_wchat.sh          # Linux/Mac启动脚本
├── README_启动说明.md      # 本说明文件
├── config.py               # 系统配置文件
├── data/                   # 数据文件目录
│   ├── faq.xlsx           # FAQ数据
│   └── products.xlsx      # 产品数据
└── src/                   # 源代码目录
    ├── web/               # Web应用
    ├── bot/               # 机器人逻辑
    ├── ai/                # AI服务
    └── database/          # 数据处理
```

## 🔄 更新和维护

### 配置更新
- 修改 `config.py` 后需要重启服务器
- AI模型配置可在Web界面中修改

### 数据更新
- 可通过Web界面上传新的数据文件
- 支持Excel格式的FAQ和产品数据

### 系统更新
- 定期检查依赖包更新
- 备份重要的配置和数据文件

## 📞 技术支持

如果遇到问题：
1. 查看启动日志中的错误信息
2. 检查系统要求和依赖
3. 参考故障排除部分
4. 确保网络连接正常（AI功能需要）

---

**🎉 祝您使用愉快！**
