#!/usr/bin/env python3
"""
测试回复优先级逻辑
"""
import sys
from pathlib import Path

# 添加项目路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def test_enhanced_reply_engine():
    """测试增强回复引擎"""
    print("=" * 60)
    print("          测试增强回复引擎")
    print("=" * 60)
    
    try:
        from src.bot.enhanced_reply_engine import EnhancedReplyEngine
        
        # 创建增强回复引擎
        reply_engine = EnhancedReplyEngine()
        print("✅ 增强回复引擎创建成功")
        
        # 检查数据加载
        if hasattr(reply_engine, 'faq_reader') and reply_engine.faq_reader:
            faq_count = len(reply_engine.faq_reader.data) if reply_engine.faq_reader.data is not None else 0
            print(f"✅ FAQ数据: {faq_count} 条")
        
        if hasattr(reply_engine, 'product_reader') and reply_engine.product_reader:
            product_count = len(reply_engine.product_reader.data) if reply_engine.product_reader.data is not None else 0
            print(f"✅ 产品数据: {product_count} 条")
        
        return reply_engine
        
    except Exception as e:
        print(f"❌ 增强回复引擎测试失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_reply_priority(reply_engine):
    """测试回复优先级"""
    print("\n" + "=" * 60)
    print("          测试回复优先级")
    print("=" * 60)
    
    # 测试消息
    test_cases = [
        {
            "message": "手机",
            "expected": "产品推荐",
            "description": "应该匹配产品库中的手机"
        },
        {
            "message": "如何退货",
            "expected": "FAQ回复",
            "description": "应该匹配FAQ中的退货问题"
        },
        {
            "message": "耳机推荐",
            "expected": "产品推荐",
            "description": "应该匹配产品库中的耳机"
        },
        {
            "message": "发货时间",
            "expected": "FAQ回复",
            "description": "应该匹配FAQ中的发货问题"
        },
        {
            "message": "你好",
            "expected": "AI回复",
            "description": "应该使用AI回复"
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n测试 {i}: {test_case['message']}")
        print(f"期望: {test_case['expected']}")
        print(f"说明: {test_case['description']}")
        print("-" * 40)
        
        try:
            reply = reply_engine.generate_reply(test_case['message'])
            
            if reply:
                print(f"✅ 回复: {reply[:100]}...")
                
                # 简单判断回复类型
                if "产品" in reply or "价格" in reply or "推荐" in reply:
                    reply_type = "产品推荐"
                elif "退货" in reply or "发货" in reply or "客服" in reply:
                    reply_type = "FAQ回复"
                else:
                    reply_type = "AI回复"
                
                print(f"实际类型: {reply_type}")
                
                if reply_type == test_case['expected']:
                    print("🎯 优先级正确")
                else:
                    print("⚠️  优先级可能不正确")
            else:
                print("❌ 无回复")
                
        except Exception as e:
            print(f"❌ 回复生成失败: {e}")

def test_wechat_handler():
    """测试微信处理器"""
    print("\n" + "=" * 60)
    print("          测试微信处理器")
    print("=" * 60)
    
    try:
        from src.bot.wechat_handler import WeChatHandler
        
        # 创建微信处理器
        handler = WeChatHandler()
        print("✅ 微信处理器创建成功")
        
        # 检查是否使用增强回复引擎
        if hasattr(handler, 'reply_engine'):
            engine_type = type(handler.reply_engine).__name__
            print(f"✅ 回复引擎类型: {engine_type}")
            
            if "Enhanced" in engine_type:
                print("✅ 使用增强回复引擎")
            else:
                print("⚠️  使用标准回复引擎")
        
        # 测试回复生成
        test_message = "手机推荐"
        print(f"\n测试消息: {test_message}")
        
        reply = handler._generate_reply("测试用户", test_message, "测试聊天")
        if reply:
            print(f"✅ 回复: {reply[:100]}...")
        else:
            print("❌ 无回复")
        
        return True
        
    except Exception as e:
        print(f"❌ 微信处理器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("开始测试回复优先级逻辑...")
    
    # 测试增强回复引擎
    reply_engine = test_enhanced_reply_engine()
    if not reply_engine:
        print("\n❌ 增强回复引擎测试失败")
        return
    
    # 测试回复优先级
    test_reply_priority(reply_engine)
    
    # 测试微信处理器
    if test_wechat_handler():
        print("\n✅ 微信处理器测试通过")
    else:
        print("\n❌ 微信处理器测试失败")
    
    print("\n" + "=" * 60)
    print("测试完成！")
    print("=" * 60)
    
    print("\n修复说明:")
    print("1. ✅ 微信处理器现在使用增强回复引擎")
    print("2. ✅ 回复优先级: FAQ → 产品库 → AI回复")
    print("3. ✅ 数据匹配使用相似度计算")
    print("4. ✅ 统一的数据源和匹配逻辑")

if __name__ == "__main__":
    main()
