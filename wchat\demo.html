<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>微信客服机器人 - 演示页面</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
            font-family: 'Microsoft YaHei', Arial, sans-serif; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
            max-width: 900px;
            width: 90%;
            margin: 20px;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 { font-size: 2.5em; margin-bottom: 10px; }
        .header p { opacity: 0.9; font-size: 1.1em; }
        .content { padding: 40px; }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }
        .stat-card {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 15px;
            text-align: center;
            border: 2px solid #e9ecef;
            transition: transform 0.3s ease;
        }
        .stat-card:hover { transform: translateY(-5px); }
        .stat-number {
            font-size: 2.5em;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 5px;
        }
        .stat-label { color: #666; font-size: 1.1em; }
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 25px;
            margin-bottom: 40px;
        }
        .feature-card {
            padding: 25px;
            border: 2px solid #e9ecef;
            border-radius: 15px;
            text-align: center;
            transition: all 0.3s ease;
        }
        .feature-card:hover {
            border-color: #667eea;
            transform: translateY(-3px);
        }
        .feature-icon {
            font-size: 3em;
            margin-bottom: 15px;
        }
        .feature-title {
            font-size: 1.3em;
            font-weight: bold;
            margin-bottom: 10px;
            color: #333;
        }
        .feature-desc { color: #666; line-height: 1.6; }
        .demo-section {
            background: #f8f9fa;
            padding: 30px;
            border-radius: 15px;
            margin-bottom: 30px;
        }
        .demo-title {
            font-size: 1.5em;
            font-weight: bold;
            margin-bottom: 20px;
            color: #333;
            text-align: center;
        }
        .qa-example {
            background: white;
            padding: 20px;
            border-radius: 10px;
            margin: 15px 0;
            border-left: 4px solid #667eea;
        }
        .question {
            font-weight: bold;
            color: #667eea;
            margin-bottom: 10px;
        }
        .answer {
            color: #333;
            line-height: 1.6;
        }
        .tech-stack {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            justify-content: center;
            margin-top: 20px;
        }
        .tech-tag {
            background: #667eea;
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.9em;
        }
        .status {
            text-align: center;
            padding: 20px;
            background: #d4edda;
            border-radius: 10px;
            margin-top: 20px;
        }
        .status-icon { font-size: 2em; margin-bottom: 10px; }
        .status-text { color: #155724; font-weight: bold; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 微信客服机器人</h1>
            <p>基于AI的智能客服解决方案 - 测试演示</p>
        </div>
        
        <div class="content">
            <!-- 统计数据 -->
            <div class="stats">
                <div class="stat-card">
                    <div class="stat-number">6</div>
                    <div class="stat-label">FAQ条目</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">6</div>
                    <div class="stat-label">产品数量</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">6</div>
                    <div class="stat-label">分类数量</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">100%</div>
                    <div class="stat-label">测试通过率</div>
                </div>
            </div>
            
            <!-- 核心功能 -->
            <div class="features">
                <div class="feature-card">
                    <div class="feature-icon">💬</div>
                    <div class="feature-title">智能FAQ回复</div>
                    <div class="feature-desc">基于关键词匹配和相似度计算，快速找到最合适的FAQ回复</div>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">📦</div>
                    <div class="feature-title">产品信息推荐</div>
                    <div class="feature-desc">智能匹配产品信息，提供详细的产品介绍和推荐</div>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">🧠</div>
                    <div class="feature-title">AI辅助回复</div>
                    <div class="feature-desc">当知识库无法匹配时，使用大语言模型生成智能回复</div>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">⚙️</div>
                    <div class="feature-title">Web配置管理</div>
                    <div class="feature-desc">直观的Web界面，轻松管理FAQ库、产品库和系统配置</div>
                </div>
            </div>
            
            <!-- 演示示例 -->
            <div class="demo-section">
                <div class="demo-title">💡 智能回复演示</div>
                
                <div class="qa-example">
                    <div class="question">Q: 如何退货？</div>
                    <div class="answer">A: 您可以在订单页面点击"申请退货"，或联系客服办理退货退款。退货商品需保持原包装完整，7天内可无理由退货。</div>
                </div>
                
                <div class="qa-example">
                    <div class="question">Q: 什么时候发货？</div>
                    <div class="answer">A: 我们会在您付款后24小时内安排发货，节假日可能会有延迟。您可以在订单详情中查看物流信息。</div>
                </div>
                
                <div class="qa-example">
                    <div class="question">Q: 有什么优惠活动吗？</div>
                    <div class="answer">A: 我们经常有各种优惠活动，建议您关注我们的官方公众号获取最新优惠信息。新用户首单可享受9折优惠。</div>
                </div>
            </div>
            
            <!-- 技术栈 -->
            <div class="demo-section">
                <div class="demo-title">🛠️ 技术栈</div>
                <div class="tech-stack">
                    <span class="tech-tag">Python 3.12</span>
                    <span class="tech-tag">Flask</span>
                    <span class="tech-tag">pandas</span>
                    <span class="tech-tag">wxauto</span>
                    <span class="tech-tag">OpenAI API</span>
                    <span class="tech-tag">jieba</span>
                    <span class="tech-tag">Bootstrap</span>
                    <span class="tech-tag">Excel/CSV</span>
                </div>
            </div>
            
            <!-- 项目状态 -->
            <div class="status">
                <div class="status-icon">✅</div>
                <div class="status-text">核心功能测试通过 - 项目可用于开发和测试</div>
            </div>
            
            <!-- 使用说明 -->
            <div class="demo-section">
                <div class="demo-title">🚀 快速开始</div>
                <div style="text-align: left; max-width: 600px; margin: 0 auto;">
                    <p><strong>1. 安装依赖：</strong></p>
                    <code style="background: #f1f1f1; padding: 5px 10px; border-radius: 5px; display: block; margin: 10px 0;">python install_deps.py</code>
                    
                    <p><strong>2. 启动Web配置：</strong></p>
                    <code style="background: #f1f1f1; padding: 5px 10px; border-radius: 5px; display: block; margin: 10px 0;">python web_config.py</code>
                    
                    <p><strong>3. 运行机器人：</strong></p>
                    <code style="background: #f1f1f1; padding: 5px 10px; border-radius: 5px; display: block; margin: 10px 0;">python run.py</code>
                    
                    <p><strong>4. 测试功能：</strong></p>
                    <code style="background: #f1f1f1; padding: 5px 10px; border-radius: 5px; display: block; margin: 10px 0;">python test_basic.py</code>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
