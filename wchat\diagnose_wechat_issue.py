#!/usr/bin/env python3
"""
微信监听问题诊断脚本
"""
import sys
import time
import traceback
from pathlib import Path

# 添加项目路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def test_wxauto_installation():
    """测试wxauto安装"""
    print("🔍 检查wxauto安装...")
    try:
        import wxauto
        print(f"✅ wxauto版本: {wxauto.__version__ if hasattr(wxauto, '__version__') else '未知'}")
        return True
    except ImportError as e:
        print(f"❌ wxauto未安装: {e}")
        print("请运行: pip install wxauto")
        return False

def test_wechat_connection():
    """测试微信连接"""
    print("\n🔍 测试微信连接...")
    try:
        from wxauto import WeChat
        
        print("正在连接微信...")
        wx = WeChat()
        
        # 检查基本属性
        if hasattr(wx, 'nickname'):
            print(f"✅ 微信连接成功，用户: {wx.nickname}")
        else:
            print("⚠️  微信连接成功，但无法获取用户信息")
        
        return wx
    except Exception as e:
        print(f"❌ 微信连接失败: {e}")
        print("请确保:")
        print("  1. 微信PC版已安装并登录")
        print("  2. 微信版本与wxauto兼容")
        traceback.print_exc()
        return None

def test_message_methods(wx):
    """测试消息相关方法"""
    print("\n🔍 测试消息方法...")
    
    # 检查可用方法
    methods = [method for method in dir(wx) if 'message' in method.lower() or 'msg' in method.lower()]
    print("可用的消息相关方法:")
    for method in sorted(methods):
        print(f"  - {method}")
    
    # 测试GetNewMessage方法
    print("\n测试GetNewMessage方法...")
    try:
        msgs = wx.GetNewMessage()
        print(f"✅ GetNewMessage成功，返回类型: {type(msgs)}")
        if msgs:
            print(f"   获取到 {len(msgs)} 条消息")
            for i, msg in enumerate(msgs[:2]):
                print(f"   消息{i+1}: {type(msg)} - {msg}")
        else:
            print("   当前无新消息")
        return True
    except Exception as e:
        print(f"❌ GetNewMessage失败: {e}")
        traceback.print_exc()
        return False

def test_alternative_methods(wx):
    """测试其他消息获取方法"""
    print("\n🔍 测试其他消息方法...")
    
    # 尝试其他可能的方法
    alternative_methods = [
        'GetAllMessage',
        'GetNextNewMessage', 
        'get_new_msgs',
        'get_messages',
        'GetMessages'
    ]
    
    for method_name in alternative_methods:
        if hasattr(wx, method_name):
            try:
                method = getattr(wx, method_name)
                result = method()
                print(f"✅ {method_name}成功，返回: {type(result)}")
            except Exception as e:
                print(f"❌ {method_name}失败: {e}")
        else:
            print(f"⚠️  {method_name}方法不存在")

def test_chat_switching(wx):
    """测试聊天切换"""
    print("\n🔍 测试聊天切换...")
    
    try:
        # 尝试切换到文件传输助手
        result = wx.ChatWith("文件传输助手")
        print(f"✅ 切换到文件传输助手: {result}")
        
        # 发送测试消息
        test_msg = f"测试消息 - {time.strftime('%H:%M:%S')}"
        wx.SendMsg(test_msg)
        print(f"✅ 发送测试消息: {test_msg}")
        
        # 等待并获取消息
        time.sleep(2)
        msgs = wx.GetNewMessage()
        if msgs:
            print(f"✅ 获取到 {len(msgs)} 条新消息")
        else:
            print("⚠️  未获取到新消息")
            
        return True
    except Exception as e:
        print(f"❌ 聊天切换测试失败: {e}")
        traceback.print_exc()
        return False

def test_current_handler():
    """测试当前的微信处理器"""
    print("\n🔍 测试当前微信处理器...")
    
    try:
        from src.bot.wechat_handler import WeChatHandler
        
        handler = WeChatHandler()
        print("✅ 微信处理器创建成功")
        
        # 测试初始化
        if handler.initialize_wechat():
            print("✅ 微信初始化成功")
            
            # 测试状态
            status = handler.get_status()
            print(f"处理器状态: {status}")
            
            return True
        else:
            print("❌ 微信初始化失败")
            return False
            
    except Exception as e:
        print(f"❌ 微信处理器测试失败: {e}")
        traceback.print_exc()
        return False

def check_wxauto_version_compatibility():
    """检查wxauto版本兼容性"""
    print("\n🔍 检查wxauto版本兼容性...")
    
    try:
        import wxauto
        from wxauto import WeChat
        
        # 检查WeChat类的方法
        wx_methods = dir(WeChat)
        
        required_methods = ['GetNewMessage', 'ChatWith', 'SendMsg']
        missing_methods = []
        
        for method in required_methods:
            if method not in wx_methods:
                missing_methods.append(method)
        
        if missing_methods:
            print(f"❌ 缺少必需方法: {missing_methods}")
            print("可能需要更新wxauto版本")
            return False
        else:
            print("✅ 所有必需方法都存在")
            return True
            
    except Exception as e:
        print(f"❌ 版本兼容性检查失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("          微信监听问题诊断工具")
    print("=" * 60)
    
    # 1. 检查wxauto安装
    if not test_wxauto_installation():
        return
    
    # 2. 检查版本兼容性
    if not check_wxauto_version_compatibility():
        print("\n建议:")
        print("  pip uninstall wxauto")
        print("  pip install wxauto")
        return
    
    # 3. 测试微信连接
    wx = test_wechat_connection()
    if not wx:
        return
    
    # 4. 测试消息方法
    if not test_message_methods(wx):
        test_alternative_methods(wx)
    
    # 5. 测试聊天切换
    test_chat_switching(wx)
    
    # 6. 测试当前处理器
    test_current_handler()
    
    print("\n" + "=" * 60)
    print("诊断完成！")
    print("=" * 60)
    
    print("\n如果问题仍然存在，请尝试:")
    print("1. 重启微信PC版")
    print("2. 更新wxauto: pip install --upgrade wxauto")
    print("3. 检查微信版本是否与wxauto兼容")
    print("4. 确保微信处于前台或最小化状态（不要完全关闭）")

if __name__ == "__main__":
    main()
