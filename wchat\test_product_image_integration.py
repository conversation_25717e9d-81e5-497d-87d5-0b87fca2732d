#!/usr/bin/env python3
"""
测试产品图片集成功能
模拟完整的产品推荐和图片发送流程
"""
import os
import sys
import time
from pathlib import Path

# 添加项目路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

class MockWeChatAPI:
    """模拟微信API"""
    
    def __init__(self):
        self.sent_messages = []
        self.sent_files = []
    
    def SendMsg(self, msg: str, who: str = None):
        """发送文本消息"""
        print(f"📤 发送消息: {msg}")
        self.sent_messages.append(msg)
        time.sleep(0.5)  # 模拟发送延迟
    
    def SendFiles(self, filepath: str, who: str = None):
        """发送文件"""
        filename = os.path.basename(filepath)
        if os.path.exists(filepath):
            file_size = os.path.getsize(filepath)
            print(f"📷 发送图片: {filename} ({file_size} bytes)")
            self.sent_files.append(filepath)
            time.sleep(1)  # 模拟图片发送延迟
            return True
        else:
            print(f"❌ 图片发送失败: {filename} 不存在")
            return False

def handle_product_query_with_images(user_message: str, mock_wx: MockWeChatAPI):
    """处理产品查询并发送图片"""
    try:
        from src.bot.enhanced_reply_engine_with_images import EnhancedReplyEngineWithImages
        
        # 创建支持图片的回复引擎
        reply_engine = EnhancedReplyEngineWithImages()
        
        print(f"👤 用户: {user_message}")
        
        # 生成回复和图片
        reply, image_paths = reply_engine.generate_reply_with_images(user_message)
        
        if reply:
            # 发送文本回复
            mock_wx.SendMsg(reply)
            
            # 发送产品图片
            if image_paths:
                print(f"📷 准备发送 {len(image_paths)} 张产品图片...")
                for img_path in image_paths:
                    success = mock_wx.SendFiles(img_path)
                    if not success:
                        print(f"⚠️ 图片发送失败: {os.path.basename(img_path)}")
            else:
                print(f"ℹ️ 该产品暂无图片")
        else:
            print(f"🤖 机器人: (保持沉默)")
        
        return reply, image_paths
        
    except Exception as e:
        print(f"❌ 处理失败: {e}")
        import traceback
        traceback.print_exc()
        return "", []

def test_product_image_integration():
    """测试产品图片集成功能"""
    print("🔗 测试产品图片集成功能")
    print("=" * 60)
    
    # 创建模拟微信API
    mock_wx = MockWeChatAPI()
    
    # 测试场景
    test_scenarios = [
        "推荐一款手机",
        "有什么好的耳机",
        "笔记本电脑多少钱",
        "充电器",
        "智能手表怎么样",
        "游戏鼠标",
        "什么时候发货",  # FAQ测试
        "随便聊聊"       # 无匹配测试
    ]
    
    results = []
    
    for i, scenario in enumerate(test_scenarios, 1):
        print(f"\n场景 {i}: {scenario}")
        print("-" * 40)
        
        reply, image_paths = handle_product_query_with_images(scenario, mock_wx)
        
        result = {
            "scenario": scenario,
            "has_reply": bool(reply),
            "reply_length": len(reply) if reply else 0,
            "image_count": len(image_paths),
            "images": [os.path.basename(p) for p in image_paths]
        }
        results.append(result)
        
        print(f"✅ 场景 {i} 完成")
    
    # 统计结果
    print(f"\n" + "=" * 60)
    print(f"          测试结果统计")
    print(f"=" * 60)
    
    total_scenarios = len(results)
    scenarios_with_reply = sum(1 for r in results if r["has_reply"])
    scenarios_with_images = sum(1 for r in results if r["image_count"] > 0)
    total_images = sum(r["image_count"] for r in results)
    
    print(f"总测试场景: {total_scenarios}")
    print(f"有回复场景: {scenarios_with_reply} ({scenarios_with_reply/total_scenarios*100:.1f}%)")
    print(f"有图片场景: {scenarios_with_images} ({scenarios_with_images/total_scenarios*100:.1f}%)")
    print(f"总发送图片: {total_images} 张")
    
    print(f"\n详细结果:")
    for result in results:
        status = "✅" if result["has_reply"] else "⭕"
        images_info = f", 图片: {result['image_count']}张" if result["image_count"] > 0 else ""
        print(f"{status} {result['scenario']} - 回复: {result['reply_length']}字符{images_info}")
        if result["images"]:
            for img in result["images"]:
                print(f"    📷 {img}")
    
    print(f"\n发送统计:")
    print(f"📝 文本消息: {len(mock_wx.sent_messages)} 条")
    print(f"📷 图片文件: {len(mock_wx.sent_files)} 张")
    
    return results

def test_specific_product_query():
    """测试特定产品查询"""
    print("\n🎯 测试特定产品查询")
    print("=" * 60)
    
    mock_wx = MockWeChatAPI()
    
    # 测试您提到的具体场景
    test_message = "手机"  # 这应该匹配到智能手机和智能手表
    
    print(f"测试消息: '{test_message}'")
    print("预期结果: 应该推荐智能手机A1和智能手表C3，并发送对应图片")
    print()
    
    reply, image_paths = handle_product_query_with_images(test_message, mock_wx)
    
    print(f"\n实际结果分析:")
    print(f"回复内容: {len(reply)} 字符")
    print(f"图片数量: {len(image_paths)} 张")
    
    if image_paths:
        print(f"图片文件:")
        for img_path in image_paths:
            filename = os.path.basename(img_path)
            exists = "✅" if os.path.exists(img_path) else "❌"
            print(f"  {filename} {exists}")
    
    # 检查回复内容
    if "智能手机A1" in reply and "智能手表C3" in reply:
        print(f"✅ 产品推荐正确")
    else:
        print(f"⚠️ 产品推荐可能有问题")
    
    if len(image_paths) >= 2:
        print(f"✅ 图片数量符合预期")
    else:
        print(f"⚠️ 图片数量少于预期")

def main():
    """主测试函数"""
    print("🚀 产品图片集成功能测试")
    print()
    
    try:
        # 运行集成测试
        results = test_product_image_integration()
        
        # 运行特定测试
        test_specific_product_query()
        
        # 总结
        print(f"\n" + "=" * 60)
        print(f"          总结")
        print(f"=" * 60)
        
        scenarios_with_images = sum(1 for r in results if r["image_count"] > 0)
        
        if scenarios_with_images > 0:
            print("🎉 产品图片集成功能工作正常！")
            print("\n💡 集成建议:")
            print("1. 将 EnhancedReplyEngineWithImages 集成到主要的消息处理器中")
            print("2. 修改微信处理器支持图片发送")
            print("3. 添加图片发送的错误处理和重试机制")
            print("4. 考虑添加图片发送间隔，避免刷屏")
        else:
            print("⚠️ 产品图片功能可能存在问题，请检查配置")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
