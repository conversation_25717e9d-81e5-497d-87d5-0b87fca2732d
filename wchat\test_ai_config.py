#!/usr/bin/env python3
"""
测试AI配置功能
"""
import sys
import json
from pathlib import Path

# 添加项目路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def test_ai_config_loading():
    """测试AI配置加载"""
    print("=" * 60)
    print("          测试AI配置加载")
    print("=" * 60)
    
    try:
        from config import config
        
        print("当前AI配置:")
        print(f"  API密钥: {config.ai.api_key[:20]}...{config.ai.api_key[-10:]}")
        print(f"  基础URL: {config.ai.base_url}")
        print(f"  模型: {config.ai.model}")
        print(f"  最大令牌: {config.ai.max_tokens}")
        print(f"  温度: {config.ai.temperature}")
        print(f"  启用状态: {config.ai.enabled}")
        print(f"  系统提示词: {config.ai.system_prompt[:100]}...")
        
        return config
        
    except Exception as e:
        print(f"❌ 配置加载失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_llm_service_with_config():
    """测试LLM服务使用配置"""
    print("\n" + "=" * 60)
    print("          测试LLM服务使用配置")
    print("=" * 60)
    
    try:
        from src.ai.llm_service import LLMService
        from config import config
        
        # 创建LLM服务
        llm = LLMService(
            api_key=config.ai.api_key,
            base_url=config.ai.base_url,
            model=config.ai.model,
            max_tokens=config.ai.max_tokens,
            temperature=config.ai.temperature,
            system_prompt=config.ai.system_prompt
        )
        
        print("LLM服务配置:")
        print(f"  最大令牌: {llm.max_tokens}")
        print(f"  温度: {llm.temperature}")
        print(f"  系统提示词: {llm.system_prompt[:100]}...")
        
        # 测试AI回复
        print("\n测试AI回复:")
        test_messages = [
            "你好",
            "我想买台电脑",
            "谢谢"
        ]
        
        for msg in test_messages:
            print(f"\n测试: {msg}")
            try:
                reply = llm.generate_reply(msg)
                print(f"回复: {reply}")
                print(f"回复长度: {len(reply)} 字符")
            except Exception as e:
                print(f"回复失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ LLM服务测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_config_update():
    """测试配置更新"""
    print("\n" + "=" * 60)
    print("          测试配置更新")
    print("=" * 60)
    
    try:
        # 读取当前配置
        config_file = current_dir / "config" / "config.json"
        with open(config_file, 'r', encoding='utf-8') as f:
            config_data = json.load(f)
        
        print("当前配置文件中的AI设置:")
        ai_config = config_data.get('ai', {})
        print(f"  最大令牌: {ai_config.get('max_tokens')}")
        print(f"  温度: {ai_config.get('temperature')}")
        print(f"  系统提示词: {ai_config.get('system_prompt', '未设置')[:100]}...")
        
        # 测试配置更新
        print("\n测试配置更新...")
        test_config = {
            "max_tokens": 200,
            "temperature": 0.8,
            "system_prompt": "这是一个测试提示词，用于验证配置更新功能。"
        }
        
        # 更新配置
        config_data['ai'].update(test_config)
        
        # 保存配置
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(config_data, f, ensure_ascii=False, indent=2)
        
        print("✅ 配置更新成功")
        
        # 重新加载配置验证
        from config import Config
        new_config = Config()
        
        print("更新后的配置:")
        print(f"  最大令牌: {new_config.ai.max_tokens}")
        print(f"  温度: {new_config.ai.temperature}")
        print(f"  系统提示词: {new_config.ai.system_prompt[:100]}...")
        
        # 恢复原配置
        config_data['ai']['max_tokens'] = 150
        config_data['ai']['temperature'] = 0.7
        config_data['ai']['system_prompt'] = ai_config.get('system_prompt', '')
        
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(config_data, f, ensure_ascii=False, indent=2)
        
        print("✅ 配置已恢复")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置更新测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_web_config_api():
    """测试Web配置API"""
    print("\n" + "=" * 60)
    print("          测试Web配置API")
    print("=" * 60)
    
    try:
        import requests
        
        # 测试获取配置
        print("测试获取配置...")
        response = requests.get('http://localhost:5000/api/config')
        
        if response.status_code == 200:
            config_data = response.json()
            ai_config = config_data.get('ai', {})
            
            print("Web API返回的AI配置:")
            print(f"  最大令牌: {ai_config.get('max_tokens')}")
            print(f"  温度: {ai_config.get('temperature')}")
            print(f"  系统提示词: {ai_config.get('system_prompt', '未设置')[:100]}...")
            
            return True
        else:
            print(f"❌ Web API请求失败: {response.status_code}")
            return False
        
    except Exception as e:
        print(f"❌ Web配置API测试失败: {e}")
        print("提示: 请确保Web服务器正在运行")
        return False

def main():
    """主函数"""
    print("开始测试AI配置功能...")
    
    # 测试配置加载
    config = test_ai_config_loading()
    if not config:
        print("\n❌ 配置加载测试失败")
        return
    
    # 测试LLM服务
    if test_llm_service_with_config():
        print("\n✅ LLM服务测试成功")
    else:
        print("\n❌ LLM服务测试失败")
    
    # 测试配置更新
    if test_config_update():
        print("\n✅ 配置更新测试成功")
    else:
        print("\n❌ 配置更新测试失败")
    
    # 测试Web配置API
    if test_web_config_api():
        print("\n✅ Web配置API测试成功")
    else:
        print("\n⚠️  Web配置API测试失败（可能Web服务器未运行）")
    
    print("\n" + "=" * 60)
    print("AI配置功能测试完成！")
    print("=" * 60)
    
    print("\n功能说明:")
    print("1. ✅ AI参数配置：最大令牌数、温度参数")
    print("2. ✅ 系统提示词配置：可在Web界面自定义")
    print("3. ✅ 配置实时生效：修改后立即应用")
    print("4. ✅ Web界面管理：友好的配置界面")
    
    print("\n使用方法:")
    print("1. 访问 http://localhost:5000")
    print("2. 进入配置页面")
    print("3. 修改AI设置中的参数")
    print("4. 保存配置即可生效")

if __name__ == "__main__":
    main()
