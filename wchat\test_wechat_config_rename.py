#!/usr/bin/env python3
"""
测试"微信配置"改为"系统配置"的修改效果
"""
import os
import sys
from pathlib import Path

# 添加项目路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def test_html_templates():
    """测试HTML模板文件中的配置名称修改"""
    print("🔍 测试HTML模板配置名称修改")
    print("=" * 60)
    
    templates_dir = current_dir / "src" / "web" / "templates"
    
    # 需要检查的模板文件
    template_files = [
        "config.html",
        "config_simple.html",
        "base.html",
        "dashboard.html"
    ]
    
    old_name = "微信配置"
    new_name = "系统配置"
    
    results = []
    
    for template_file in template_files:
        template_path = templates_dir / template_file
        
        if template_path.exists():
            print(f"\n📄 检查文件: {template_file}")
            
            with open(template_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查是否还有旧名称
            old_count = content.count(old_name)
            new_count = content.count(new_name)
            
            if old_count > 0:
                print(f"  ❌ 仍包含旧名称 '{old_name}': {old_count} 处")
                # 显示包含旧名称的行
                lines = content.split('\n')
                for i, line in enumerate(lines, 1):
                    if old_name in line:
                        print(f"    第{i}行: {line.strip()}")
                results.append((template_file, False, f"旧名称{old_count}处"))
            elif new_count > 0:
                print(f"  ✅ 已更新为新名称 '{new_name}': {new_count} 处")
                results.append((template_file, True, f"新名称{new_count}处"))
            else:
                print(f"  ⚠️ 未找到相关配置名称")
                results.append((template_file, True, "无相关名称"))
        else:
            print(f"\n❌ 文件不存在: {template_file}")
            results.append((template_file, False, "文件不存在"))
    
    return results

def test_python_code():
    """测试Python代码中的配置名称修改"""
    print("\n🐍 测试Python代码配置名称修改")
    print("=" * 60)
    
    # 需要检查的Python文件
    python_files = [
        "src/web/app.py"
    ]
    
    old_name = "微信配置"
    new_name = "系统配置"
    
    results = []
    
    for python_file in python_files:
        python_path = current_dir / python_file
        
        if python_path.exists():
            print(f"\n📄 检查文件: {python_file}")
            
            with open(python_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查是否还有旧名称
            old_count = content.count(old_name)
            new_count = content.count(new_name)
            
            if old_count > 0:
                print(f"  ❌ 仍包含旧名称 '{old_name}': {old_count} 处")
                # 显示包含旧名称的行
                lines = content.split('\n')
                for i, line in enumerate(lines, 1):
                    if old_name in line:
                        print(f"    第{i}行: {line.strip()}")
                results.append((python_file, False, f"旧名称{old_count}处"))
            elif new_count > 0:
                print(f"  ✅ 已更新为新名称 '{new_name}': {new_count} 处")
                results.append((python_file, True, f"新名称{new_count}处"))
            else:
                print(f"  ⚠️ 未找到相关配置名称")
                results.append((python_file, True, "无相关名称"))
        else:
            print(f"\n❌ 文件不存在: {python_file}")
            results.append((python_file, False, "文件不存在"))
    
    return results

def simulate_web_interface():
    """模拟Web界面显示效果"""
    print("\n🌐 模拟Web界面显示效果")
    print("=" * 60)
    
    print("配置页面预览:")
    print("-" * 40)
    print("🔧 基础配置")
    print("   ├── 📱 系统配置")
    print("   │   ├── 监听模式选择")
    print("   │   ├── 自动回复设置")
    print("   │   ├── 回复延迟配置")
    print("   │   └── 语音消息设置")
    print("   ├── 🤖 AI配置")
    print("   │   ├── API密钥")
    print("   │   ├── 模型选择")
    print("   │   └── 系统提示词")
    print("   └── 📊 数据库配置")
    print("       ├── FAQ文件路径")
    print("       └── 产品文件路径")
    
    print(f"\n配置卡片标题:")
    print(f"🔧 系统配置 (原: 微信配置)")
    
    print(f"\n图标变化:")
    print(f"原图标: 🟢 fab fa-weixin (微信图标)")
    print(f"新图标: 🔵 fas fa-cogs (系统齿轮图标)")
    
    return True

def test_config_page_structure():
    """测试配置页面结构"""
    print("\n📋 测试配置页面结构")
    print("=" * 60)
    
    try:
        config_file = current_dir / "src" / "web" / "templates" / "config.html"
        
        if config_file.exists():
            with open(config_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查配置页面的主要结构
            sections = [
                ("系统配置", "fas fa-cogs"),
                ("AI配置", "fas fa-brain"),
                ("数据库配置", "fas fa-database"),
                ("回复配置", "fas fa-comments"),
                ("Web配置", "fas fa-globe")
            ]
            
            print("配置页面结构检查:")
            for section_name, icon_class in sections:
                if section_name in content:
                    print(f"  ✅ {section_name} 配置节存在")
                    if icon_class in content:
                        print(f"    ✅ 图标 {icon_class} 正确")
                    else:
                        print(f"    ⚠️ 图标可能需要检查")
                else:
                    print(f"  ❌ {section_name} 配置节缺失")
            
            # 检查是否有旧的微信图标
            if "fab fa-weixin" in content:
                print(f"  ⚠️ 仍包含旧的微信图标 'fab fa-weixin'")
            else:
                print(f"  ✅ 已移除旧的微信图标")
            
            return True
        else:
            print("❌ config.html 文件不存在")
            return False
            
    except Exception as e:
        print(f"❌ 配置页面结构检查失败: {e}")
        return False

def main():
    """主函数"""
    print("🔄 微信配置 → 系统配置 修改测试")
    print()
    
    # 运行测试
    tests = [
        ("HTML模板", test_html_templates),
        ("Python代码", test_python_code),
        ("配置页面结构", test_config_page_structure),
        ("界面预览", simulate_web_interface)
    ]
    
    all_results = []
    
    for test_name, test_func in tests:
        print(f"\n🔍 运行测试: {test_name}")
        try:
            results = test_func()
            if isinstance(results, list):
                all_results.extend([(test_name, *result) for result in results])
            else:
                all_results.append((test_name, "测试完成", True, "成功"))
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            all_results.append((test_name, "测试异常", False, str(e)))
    
    # 总结
    print(f"\n" + "=" * 60)
    print(f"          配置名称修改测试总结")
    print(f"=" * 60)
    
    success_count = sum(1 for _, _, success, _ in all_results if success)
    total_count = len(all_results)
    
    print(f"测试结果统计:")
    for test_type, file_name, success, detail in all_results:
        status = "✅" if success else "❌"
        print(f"  {status} {test_type} - {file_name}: {detail}")
    
    print(f"\n总计: {success_count}/{total_count} 项测试通过")
    
    if success_count == total_count:
        print("🎉 所有配置名称修改完成！")
        print("\n💡 修改效果:")
        print("   - 配置页面标题: 微信配置 → 系统配置")
        print("   - 配置卡片图标: 微信图标 → 系统齿轮图标")
        print("   - 日志信息: 微信配置 → 系统配置")
        print("   - 调试页面: 微信配置 → 系统配置")
        print("\n🚀 重启Web服务器查看效果!")
        print("\n📋 配置页面现在显示:")
        print("   🔧 系统配置")
        print("   🤖 AI配置")
        print("   📊 数据库配置")
        print("   💬 回复配置")
        print("   🌐 Web配置")
    else:
        print("⚠️ 部分文件可能需要手动检查")
        
        # 给出具体建议
        failed_items = [item for item in all_results if not item[2]]
        if failed_items:
            print("\n💡 需要检查的项目:")
            for test_type, file_name, _, detail in failed_items:
                print(f"   - {test_type}: {file_name} ({detail})")

if __name__ == "__main__":
    main()
